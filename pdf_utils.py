from reportlab.lib.pagesizes import A4
from reportlab.pdfgen import canvas
from reportlab.lib import colors
from reportlab.platypus import Table, TableStyle, Paragraph
from reportlab.lib.units import cm
import arabic_reshaper
from bidi.algorithm import get_display
import os
from datetime import datetime
from reportlab.pdfbase import pdfmetrics
from reportlab.pdfbase.ttfonts import TTFont
from reportlab.lib.styles import getSampleStyleSheet, ParagraphStyle
from reportlab.lib.enums import TA_CENTER, TA_RIGHT
import sys
import logging

def get_app_path():
    """الحصول على مسار التطبيق"""
    try:
        if getattr(sys, 'frozen', False):
            return os.path.dirname(sys.executable)
        return os.path.dirname(os.path.abspath(__file__))
    except:
        return os.getcwd()

# تسجيل الخط العربي
try:
    font_path = os.path.join(get_app_path(), 'amiri-regular.ttf')
    pdfmetrics.registerFont(TTFont('Amir<PERSON>', font_path))
    arabic_font = 'Amiri'
except Exception as e:
    logging.error(f"فشل تحميل الخط العربي: {str(e)}")
    arabic_font = 'Helvetica'


# Create text styles
styles = getSampleStyleSheet()
arabic_style = ParagraphStyle(
    'ArabicStyle',
    parent=styles['Normal'],
    fontName=arabic_font,
    fontSize=12,
    alignment=TA_RIGHT,
    leading=14,
    spaceAfter=6
)

title_style = ParagraphStyle(
    'TitleStyle',
    parent=styles['Heading1'],
    fontName=arabic_font,
    fontSize=18,
    alignment=TA_CENTER,
    textColor=colors.darkblue,
    spaceAfter=15
)

header_style = ParagraphStyle(
    'HeaderStyle',
    parent=styles['Heading2'],
    fontName=arabic_font,
    fontSize=14,
    alignment=TA_RIGHT,
    textColor=colors.darkblue,
    spaceAfter=5
)

def reshape_arabic(text):
    """Format Arabic text for correct display in PDF"""
    if not text:
        return ""
    
    reshaped_text = arabic_reshaper.reshape(text)
    return get_display(reshaped_text)

class PDFGenerator:
    @staticmethod
    def create_sale_invoice(invoice_id, date, customer_name, items, total, payment_method, db):
        """Create a sale invoice PDF"""
        invoices_dir = os.path.join(get_app_path(), "invoices")
        if not os.path.exists(invoices_dir):
            os.makedirs(invoices_dir)
        
        filename = os.path.join(invoices_dir, f"sale_invoice_{invoice_id}.pdf")
        c = canvas.Canvas(filename, pagesize=A4)
        width, height = A4
        
        # Invoice title
        title_text = reshape_arabic(f"فاتورة مبيعات - رقم #{invoice_id}")
        title = Paragraph(title_text, title_style)
        title.wrapOn(c, width - 100, 50)
        title.drawOn(c, 50, height - 70)
        
        # Invoice info
        info_data = [
            [reshape_arabic(": التاريخ"), date],
            [reshape_arabic(": اسم العميل"), customer_name],
            [reshape_arabic(": طريقة الدفع"), payment_method]
        ]
        
        info_table = Table(info_data, colWidths=[4*cm, 10*cm])
        info_table.setStyle(TableStyle([
            ('FONT', (0, 0), (-1, -1), arabic_font, 12),
            ('ALIGN', (0, 0), (0, -1), 'RIGHT'),
            ('ALIGN', (1, 0), (1, -1), 'RIGHT'),
            ('VALIGN', (0, 0), (-1, -1), 'MIDDLE'),
            ('BOX', (0, 0), (-1, -1), 0.5, colors.lightgrey),
            ('GRID', (0, 0), (-1, -1), 0.5, colors.lightgrey),
            ('BACKGROUND', (0, 0), (0, -1), colors.lightgrey),
        ]))
        
        info_table.wrapOn(c, width - 100, 100)
        info_table.drawOn(c, 50, height - 150)
        
        # Products table headers
        headers = [
            reshape_arabic("المنتج"),
            reshape_arabic("الكمية"),
            reshape_arabic("السعر"),
            reshape_arabic("الإجمالي")
        ]
        
        data = [headers]
        
        # Add products to table
        for item in items:
            product = db.get_product_by_id(item['id'])
            product_name = product[1] if product else "منتج غير معروف"
            
            data.append([
                reshape_arabic(product_name),
                str(item['quantity']),
                f"{item['price']:.2f} ج.م",
                f"{item['total']:.2f} ج.م"
            ])
        
        # Add total row
        data.append([
            reshape_arabic("الإجمالي:"), 
            "", 
            "", 
            f"{total:.2f} ج.م"
        ])
        
        # Configure table
        table = Table(data, colWidths=[8*cm, 3*cm, 4*cm, 4*cm])
        table.setStyle(TableStyle([
            ('BACKGROUND', (0, 0), (-1, 0), colors.HexColor("#4CAF50")),
            ('TEXTCOLOR', (0, 0), (-1, 0), colors.white),
            ('ALIGN', (0, 0), (-1, -1), 'CENTER'),
            ('FONTNAME', (0, 0), (-1, 0), arabic_font),
            ('FONTSIZE', (0, 0), (-1, 0), 14),
            ('BOTTOMPADDING', (0, 0), (-1, 0), 12),
            ('BACKGROUND', (0, 1), (-1, -1), colors.white),
            ('GRID', (0, 0), (-1, -1), 1, colors.black),
            ('VALIGN', (0, 0), (-1, -1), 'MIDDLE'),
            ('FONTNAME', (0, 1), (-1, -1), arabic_font),
            ('FONTSIZE', (0, 1), (-1, -1), 12),
        ]))
        
        # Draw table
        table.wrapOn(c, width, height)
        table.drawOn(c, 50, height - 300)
        
        # Footer
        footer_text = reshape_arabic("شكراً لثقتكم بنا - نرجو زيارة متجرنا مرة أخرى")
        footer = Paragraph(footer_text, header_style)
        footer.wrapOn(c, width - 100, 50)
        footer.drawOn(c, 50, 50)
        
        # Save PDF
        c.save()
        return filename

    @staticmethod
    def create_purchase_invoice(invoice_id, date, supplier_name, items, total, db):
        """Create a purchase invoice PDF"""
        invoices_dir = os.path.join(get_app_path(), "invoices")
        if not os.path.exists(invoices_dir):
            os.makedirs(invoices_dir)
        
        filename = os.path.join(invoices_dir, f"purchase_invoice_{invoice_id}.pdf")
        c = canvas.Canvas(filename, pagesize=A4)
        width, height = A4
        
        # Invoice title
        title_text = reshape_arabic(f"فاتورة مشتريات - رقم #{invoice_id}")
        title = Paragraph(title_text, title_style)
        title.wrapOn(c, width - 100, 50)
        title.drawOn(c, 50, height - 70)
        
        # Invoice info
        info_data = [
            [reshape_arabic("التاريخ:"), date],
            [reshape_arabic("اسم المورد:"), supplier_name]
        ]
        
        info_table = Table(info_data, colWidths=[4*cm, 10*cm])
        info_table.setStyle(TableStyle([
            ('FONT', (0, 0), (-1, -1), arabic_font, 12),
            ('ALIGN', (0, 0), (0, -1), 'RIGHT'),
            ('ALIGN', (1, 0), (1, -1), 'RIGHT'),
            ('VALIGN', (0, 0), (-1, -1), 'MIDDLE'),
            ('BOX', (0, 0), (-1, -1), 0.5, colors.lightgrey),
            ('GRID', (0, 0), (-1, -1), 0.5, colors.lightgrey),
            ('BACKGROUND', (0, 0), (0, -1), colors.lightgrey),
        ]))
        
        info_table.wrapOn(c, width - 100, 100)
        info_table.drawOn(c, 50, height - 150)
        
        # Products table headers
        headers = [
            reshape_arabic("المنتج"),
            reshape_arabic("الكمية"),
            reshape_arabic("سعر الشراء"),
            reshape_arabic("الإجمالي")
        ]
        
        data = [headers]
        
        # Add products to table
        for item in items:
            product = db.get_product_by_id(item['id'])
            product_name = product[1] if product else "منتج غير معروف"
            
            data.append([
                reshape_arabic(product_name),
                str(item['quantity']),
                f"{item['price']:.2f} ج.م",
                f"{item['total']:.2f} ج.م"
            ])
        
        # Add total row
        data.append([
            reshape_arabic("الإجمالي:"), 
            "", 
            "", 
            f"{total:.2f} ج.م"
        ])
        
        # Configure table
        table = Table(data, colWidths=[8*cm, 3*cm, 4*cm, 4*cm])
        table.setStyle(TableStyle([
            ('BACKGROUND', (0, 0), (-1, 0), colors.HexColor("#2196F3")),
            ('TEXTCOLOR', (0, 0), (-1, 0), colors.white),
            ('ALIGN', (0, 0), (-1, -1), 'CENTER'),
            ('FONTNAME', (0, 0), (-1, 0), arabic_font),
            ('FONTSIZE', (0, 0), (-1, 0), 14),
            ('BOTTOMPADDING', (0, 0), (-1, 0), 12),
            ('BACKGROUND', (0, 1), (-1, -1), colors.white),
            ('GRID', (0, 0), (-1, -1), 1, colors.black),
            ('VALIGN', (0, 0), (-1, -1), 'MIDDLE'),
            ('FONTNAME', (0, 1), (-1, -1), arabic_font),
            ('FONTSIZE', (0, 1), (-1, -1), 12),
        ]))
        
        # Draw table
        table.wrapOn(c, width, height)
        table.drawOn(c, 50, height - 300)
        
        # Footer
        footer_text = reshape_arabic("شكراً لتعاملكم معنا")
        footer = Paragraph(footer_text, header_style)
        footer.wrapOn(c, width - 100, 50)
        footer.drawOn(c, 50, 50)
        
        # Save PDF
        c.save()
        return filename