import customtkinter as ctk
from tkinter import messagebox, filedialog
from database import Database
import os
import shutil
from datetime import datetime

class SettingsPage(ctk.CTkFrame):
    def __init__(self, parent, app):
        super().__init__(parent, fg_color="transparent")
        self.app = app
        self.db = Database()
        
        self.grid_columnconfigure(0, weight=1)
        self.grid_rowconfigure(0, weight=1)
        
        self.create_widgets()
        self.load_settings()
    
    def create_widgets(self):
        # Main container
        main_frame = ctk.CTkFrame(self, corner_radius=15)
        main_frame.pack(fill="both", expand=True, padx=20, pady=20)
        main_frame.grid_columnconfigure(0, weight=1)
        
        # Title
        title_label = ctk.CTkLabel(
            main_frame, 
            text=self.app.lang.get("system_settings"),
            font=("Arial", 24, "bold"),
            text_color="#2c3e50"
        )
        title_label.grid(row=0, column=0, pady=20)
        
        # Shop settings section
        shop_frame = ctk.CTkFrame(main_frame, corner_radius=12)
        shop_frame.grid(row=1, column=0, sticky="ew", pady=10, padx=20)
        shop_frame.grid_columnconfigure(1, weight=1)
        
        ctk.CTkLabel(
            shop_frame, 
            text=self.app.lang.get("shop_settings"),
            font=("Arial", 18, "bold")
        ).grid(row=0, column=0, columnspan=2, pady=15)
        
        # Shop name
        ctk.CTkLabel(
            shop_frame, 
            text=self.app.lang.get("shop_name"),
            font=("Arial", 14)
        ).grid(row=1, column=0, padx=10, pady=5, sticky="w")
        
        self.shop_name = ctk.CTkEntry(shop_frame, width=200)
        self.shop_name.grid(row=1, column=1, padx=10, pady=5, sticky="ew")
        
        # Currency
        ctk.CTkLabel(
            shop_frame, 
            text=self.app.lang.get("currency"),
            font=("Arial", 14)
        ).grid(row=2, column=0, padx=10, pady=5, sticky="w")
        
        self.currency = ctk.CTkEntry(shop_frame, width=200)
        self.currency.grid(row=2, column=1, padx=10, pady=5, sticky="ew")
        
        # Backup settings section
        backup_frame = ctk.CTkFrame(main_frame, corner_radius=12)
        backup_frame.grid(row=2, column=0, sticky="ew", pady=10, padx=20)
        backup_frame.grid_columnconfigure(1, weight=1)
        
        ctk.CTkLabel(
            backup_frame, 
            text=self.app.lang.get("backup_settings"),
            font=("Arial", 18, "bold")
        ).grid(row=0, column=0, columnspan=2, pady=15)
        
        # Backup path
        ctk.CTkLabel(
            backup_frame, 
            text=self.app.lang.get("backup_path"),
            font=("Arial", 14)
        ).grid(row=1, column=0, padx=10, pady=5, sticky="w")
        
        backup_path_frame = ctk.CTkFrame(backup_frame, fg_color="transparent")
        backup_path_frame.grid(row=1, column=1, padx=10, pady=5, sticky="ew")
        backup_path_frame.grid_columnconfigure(0, weight=1)
        
        self.backup_path = ctk.CTkEntry(backup_path_frame, width=150)
        self.backup_path.grid(row=0, column=0, padx=(0, 5), sticky="ew")
        
        browse_btn = ctk.CTkButton(
            backup_path_frame,
            text=self.app.lang.get("browse"),
            width=60,
            command=self.browse_backup_path
        )
        browse_btn.grid(row=0, column=1)
        
        # Backup now button
        backup_now_btn = ctk.CTkButton(
            backup_frame,
            text=self.app.lang.get("backup_now"),
            width=150,
            height=35,
            fg_color="#3498db",
            hover_color="#2980b9",
            command=self.create_backup
        )
        backup_now_btn.grid(row=2, column=0, columnspan=2, pady=15)
        
        # Save button
        save_btn = ctk.CTkButton(
            main_frame,
            text=self.app.lang.get("save_settings"),
            width=200,
            height=40,
            font=("Arial", 14, "bold"),
            fg_color="#27ae60",
            hover_color="#219653",
            command=self.save_settings
        )
        save_btn.grid(row=3, column=0, pady=20)
    
    def load_settings(self):
        """Load current settings from database"""
        self.db.cursor.execute("SELECT shop_name, currency, backup_path FROM settings LIMIT 1")
        settings = self.db.cursor.fetchone()
        
        if settings:
            self.shop_name.insert(0, settings[0] or "")
            self.currency.insert(0, settings[1] or "")
            self.backup_path.insert(0, settings[2] or "")
    
    def browse_backup_path(self):
        """Open directory dialog for backup path"""
        path = filedialog.askdirectory(title=self.app.lang.get("select_backup_directory"))
        if path:
            self.backup_path.delete(0, "end")
            self.backup_path.insert(0, path)
    
    def create_backup(self):
        """Create a backup of the database"""
        backup_path = self.backup_path.get()
        
        if not backup_path:
            messagebox.showwarning(
                self.app.lang.get("warning"), 
                self.app.lang.get("backup_path_required")
            )
            return
        
        try:
            # Create backup directory if not exists
            if not os.path.exists(backup_path):
                os.makedirs(backup_path)
            
            # Create backup filename with timestamp
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            backup_filename = f"supermarket_backup_{timestamp}.db"
            backup_full_path = os.path.join(backup_path, backup_filename)
            
            # Copy database file
            db_path = "supermarket.db"
            if os.path.exists(db_path):
                shutil.copy2(db_path, backup_full_path)
                messagebox.showinfo(
                    self.app.lang.get("success"), 
                    f"{self.app.lang.get('backup_created')}\n{backup_full_path}"
                )
            else:
                messagebox.showerror(
                    self.app.lang.get("error"), 
                    "Database file not found"
                )
        except Exception as e:
            messagebox.showerror(
                self.app.lang.get("error"), 
                f"{self.app.lang.get('backup_failed')}: {str(e)}"
            )
    
    def save_settings(self):
        """Save settings to database"""
        shop_name = self.shop_name.get()
        currency = self.currency.get()
        backup_path = self.backup_path.get()
        
        try:
            # Create backup directory if not exists
            if backup_path and not os.path.exists(backup_path):
                os.makedirs(backup_path)
            
            # Check if settings exist, if not insert new record
            self.db.cursor.execute("SELECT COUNT(*) FROM settings")
            count = self.db.cursor.fetchone()[0]
            
            if count == 0:
                # Insert new settings
                self.db.cursor.execute('''
                    INSERT INTO settings (shop_name, currency, backup_path)
                    VALUES (?, ?, ?)
                ''', (shop_name, currency, backup_path))
            else:
                # Update existing settings
                self.db.cursor.execute('''
                    UPDATE settings 
                    SET shop_name=?, currency=?, backup_path=?
                    WHERE id = (SELECT MIN(id) FROM settings)
                ''', (shop_name, currency, backup_path))
            
            # Commit changes immediately
            self.db.conn.commit()
            
            # Update language translations with new shop name
            self.app.lang.update_shop_name(shop_name)
            
            # Update main window title
            self.app.title(f"{shop_name} - {self.app.lang.get('system_title')}")
            
            messagebox.showinfo(
                self.app.lang.get("success"), 
                self.app.lang.get("saved")
            )
        except Exception as e:
            messagebox.showerror(
                self.app.lang.get("error"), 
                f"Failed to save settings: {str(e)}"
            )
    
    def refresh_data(self):
        """Refresh data"""
        # Clear existing data
        self.shop_name.delete(0, "end")
        self.currency.delete(0, "end")
        self.backup_path.delete(0, "end")
        
        # Reload settings
        self.load_settings()
    
    def update_language(self):
        """Update all text elements when language changes"""
        # Recreate the entire page with new translations
        for widget in self.winfo_children():
            widget.destroy()
        self.create_widgets()
        self.load_settings()
