2025-07-23 20:47:05,424 - INFO - Operating System: win32
2025-07-23 20:47:05,424 - INFO - Python Version: 3.12.8 (tags/v3.12.8:2dc476b, Dec  3 2024, 19:30:04) [MSC v.1942 64 bit (AMD64)]
2025-07-23 20:47:05,424 - INFO - Current Directory: C:\Users\<USER>\Desktop\SupermarketSystem
2025-07-23 20:47:05,424 - INFO - Executable Path: C:\Users\<USER>\AppData\Local\Programs\Python\Python312\python.exe
2025-07-23 20:47:05,424 - INFO - Application started
2025-07-23 20:47:05,984 - DEBUG - Database path: c:\Users\<USER>\Desktop\SupermarketSystem\supermarket.db
2025-07-23 20:47:06,016 - ERROR - Unexpected error:
Traceback (most recent call last):
  File "c:\Users\<USER>\Desktop\SupermarketSystem\main.py", line 274, in <module>
    app = SupermarketApp()
          ^^^^^^^^^^^^^^^^
  File "c:\Users\<USER>\Desktop\SupermarketSystem\main.py", line 74, in __init__
    self.db = Database()
              ^^^^^^^^^^
  File "c:\Users\<USER>\Desktop\SupermarketSystem\database.py", line 28, in __init__
    self.create_default_data()
  File "c:\Users\<USER>\Desktop\SupermarketSystem\database.py", line 123, in create_default_data
    self.cursor.execute("SELECT COUNT(*) FROM users WHERE username = 'admin'")
sqlite3.OperationalError: no such table: users

