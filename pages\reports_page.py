import customtkinter as ctk
import matplotlib.pyplot as plt
from matplotlib.backends.backend_tkagg import FigureCanvasTkAgg
from database import Database
import datetime

class ReportsPage(ctk.CTkFrame):
    def __init__(self, parent, app):
        super().__init__(parent, fg_color="transparent")
        self.app = app
        self.db = Database()
        
        # Responsive settings
        self.grid_columnconfigure(0, weight=1)
        self.grid_rowconfigure(0, weight=1)
        
        self.create_widgets()
        self.generate_reports()
    
    def create_widgets(self):
        # Main container with full expansion
        main_frame = ctk.CTkFrame(self, fg_color="transparent")
        main_frame.pack(fill="both", expand=True)
        main_frame.grid_columnconfigure(0, weight=1)
        main_frame.grid_rowconfigure(2, weight=1)  # Make charts area expandable
        
        # Title
        title_label = ctk.CTkLabel(
            main_frame, 
            text=self.app.lang.get("reports"),
            font=("Arial", 24, "bold"),
            text_color="#2c3e50"
        )
        title_label.grid(row=0, column=0, pady=10)
        
        # Control buttons at top
        controls_frame = ctk.CTkFrame(main_frame, height=80)
        controls_frame.grid(row=1, column=0, sticky="ew", padx=10, pady=5)
        controls_frame.grid_columnconfigure((0,1,2,3,4,5,6,7), weight=1)
        
        # Date filters
        ctk.CTkLabel(controls_frame, text=self.app.lang.get("from")).grid(row=0, column=0, padx=5, pady=10)
        self.start_date = ctk.CTkEntry(controls_frame, width=100)
        self.start_date.grid(row=0, column=1, padx=5, pady=10)
        self.start_date.insert(0, "2024-01-01")
        
        ctk.CTkLabel(controls_frame, text=self.app.lang.get("to")).grid(row=0, column=2, padx=5, pady=10)
        self.end_date = ctk.CTkEntry(controls_frame, width=100)
        self.end_date.grid(row=0, column=3, padx=5, pady=10)
        self.end_date.insert(0, datetime.datetime.now().strftime("%Y-%m-%d"))
        
        # Report type buttons
        sales_btn = ctk.CTkButton(
            controls_frame,
            text=self.app.lang.get("sales_report"),
            width=120,
            height=35,
            command=lambda: self.generate_specific_report("sales")
        )
        sales_btn.grid(row=0, column=4, padx=5, pady=10)
        
        inventory_btn = ctk.CTkButton(
            controls_frame,
            text=self.app.lang.get("inventory_report"),
            width=120,
            height=35,
            command=lambda: self.generate_specific_report("inventory")
        )
        inventory_btn.grid(row=0, column=5, padx=5, pady=10)
        
        financial_btn = ctk.CTkButton(
            controls_frame,
            text=self.app.lang.get("financial_report"),
            width=120,
            height=35,
            command=lambda: self.generate_specific_report("financial")
        )
        financial_btn.grid(row=0, column=6, padx=5, pady=10)
        
        # Charts frame with smaller size
        self.charts_frame = ctk.CTkFrame(main_frame)
        self.charts_frame.grid(row=2, column=0, sticky="nsew", padx=10, pady=10)
        self.charts_frame.grid_columnconfigure(0, weight=1)
        self.charts_frame.grid_rowconfigure(0, weight=1)
    
    def generate_reports(self):
        """Generate default sales report"""
        # Clear old charts
        for widget in self.charts_frame.winfo_children():
            widget.destroy()
        
        # Get filter values
        start_date = self.start_date.get()
        end_date = self.end_date.get()
        
        # Generate default sales report
        self.generate_sales_report(start_date, end_date)
    
    def generate_specific_report(self, report_type):
        """Generate specific report based on type"""
        # Clear old charts
        for widget in self.charts_frame.winfo_children():
            widget.destroy()
        
        # Get filter values
        start_date = self.start_date.get()
        end_date = self.end_date.get()
        
        if report_type == "sales":
            self.generate_sales_report(start_date, end_date)
        elif report_type == "inventory":
            self.generate_inventory_report()
        elif report_type == "financial":
            self.generate_financial_report(start_date, end_date)
    
    def generate_sales_report(self, start_date, end_date):
        """Generate sales report with real data"""
        try:
            sales_data = self.db.get_sales_by_date_range(start_date, end_date)
            
            if not sales_data:
                ctk.CTkLabel(
                    self.charts_frame, 
                    text=self.app.lang.get("no_data_available"),
                    font=("Arial", 16)
                ).place(relx=0.5, rely=0.5, anchor="center")
                return
            
            # Create sales chart with smaller size
            fig, ax = plt.subplots(figsize=(10, 5))
            dates = [row[0] for row in sales_data]
            amounts = [row[1] for row in sales_data]
            
            ax.plot(dates, amounts, marker='o', linewidth=2, markersize=6, color='#2196F3')
            ax.set_title(self.app.lang.get("sales_report"), fontsize=16, fontweight='bold')
            ax.set_xlabel(self.app.lang.get("date"), fontsize=12)
            ax.set_ylabel(self.app.lang.get("amount"), fontsize=12)
            ax.grid(True, alpha=0.3)
            ax.tick_params(axis='both', which='major', labelsize=10)
            
            plt.xticks(rotation=45)
            plt.tight_layout()
            
            # Embed chart
            canvas = FigureCanvasTkAgg(fig, self.charts_frame)
            canvas.draw()
            canvas.get_tk_widget().grid(row=0, column=0, sticky="nsew", padx=5, pady=5)
            
        except Exception as e:
            ctk.CTkLabel(
                self.charts_frame, 
                text=f"Error: {str(e)}",
                font=("Arial", 14),
                text_color="red"
            ).place(relx=0.5, rely=0.5, anchor="center")

    def generate_inventory_report(self):
        """Generate inventory report with real data"""
        try:
            products = self.db.get_all_products()
            
            if not products:
                ctk.CTkLabel(
                    self.charts_frame, 
                    text=self.app.lang.get("no_data_available"),
                    font=("Arial", 16)
                ).place(relx=0.5, rely=0.5, anchor="center")
                return
        
            # Create inventory chart with smaller size
            fig, (ax1, ax2) = plt.subplots(1, 2, figsize=(12, 5))
            
            # Stock levels chart
            names = [p[1][:10] + "..." if len(p[1]) > 10 else p[1] for p in products[:8]]
            quantities = [p[3] for p in products[:8]]
            
            ax1.bar(range(len(names)), quantities, color='skyblue')
            ax1.set_title(self.app.lang.get("inventory_report"), fontsize=14)
            ax1.set_xlabel(self.app.lang.get("products"), fontsize=11)
            ax1.set_ylabel(self.app.lang.get("quantity"), fontsize=11)
            ax1.set_xticks(range(len(names)))
            ax1.set_xticklabels(names, rotation=45, ha='right', fontsize=9)
            ax1.tick_params(axis='y', labelsize=10)
            
            # Low stock items
            low_stock = [p for p in products if p[3] < 10]
            if low_stock:
                low_names = [p[1][:10] + "..." if len(p[1]) > 10 else p[1] for p in low_stock[:8]]
                low_quantities = [p[3] for p in low_stock[:8]]
                
                ax2.bar(range(len(low_names)), low_quantities, color='red', alpha=0.7)
                ax2.set_title(self.app.lang.get("low_stock"), fontsize=14)
                ax2.set_xlabel(self.app.lang.get("products"), fontsize=11)
                ax2.set_ylabel(self.app.lang.get("quantity"), fontsize=11)
                ax2.set_xticks(range(len(low_names)))
                ax2.set_xticklabels(low_names, rotation=45, ha='right', fontsize=9)
                ax2.tick_params(axis='y', labelsize=10)
            
            plt.tight_layout()
            
            # Embed chart
            canvas = FigureCanvasTkAgg(fig, self.charts_frame)
            canvas.draw()
            canvas.get_tk_widget().grid(row=0, column=0, sticky="nsew", padx=5, pady=5)
            
        except Exception as e:
            ctk.CTkLabel(
                self.charts_frame, 
                text=f"Error: {str(e)}",
                font=("Arial", 14),
                text_color="red"
            ).place(relx=0.5, rely=0.5, anchor="center")
    
    def generate_financial_report(self, start_date, end_date):
        """Generate financial report with real data"""
        try:
            sales_total = self.db.get_total_sales_by_date_range(start_date, end_date)
            purchases_total = self.db.get_total_purchases_by_date_range(start_date, end_date)
            
            # Create financial chart with smaller size
            fig, ax = plt.subplots(figsize=(10, 5))
            
            categories = [self.app.lang.get("sales"), self.app.lang.get("purchases")]
            values = [sales_total or 0, purchases_total or 0]
            colors = ['#4CAF50', '#F44336']
            
            bars = ax.bar(categories, values, color=colors, alpha=0.7, width=0.6)
            ax.set_title(self.app.lang.get("financial_report"), fontsize=16, fontweight='bold')
            ax.set_ylabel(self.app.lang.get("amount"), fontsize=12)
            ax.tick_params(axis='both', which='major', labelsize=10)
            
            # Add value labels on bars
            for bar, value in zip(bars, values):
                height = bar.get_height()
                ax.text(bar.get_x() + bar.get_width()/2., height + height*0.01,
                       f'{value:.2f}', ha='center', va='bottom', fontweight='bold', fontsize=12)
            
            plt.tight_layout()
            
            # Embed chart
            canvas = FigureCanvasTkAgg(fig, self.charts_frame)
            canvas.draw()
            canvas.get_tk_widget().grid(row=0, column=0, sticky="nsew", padx=5, pady=5)
            
        except Exception as e:
            ctk.CTkLabel(
                self.charts_frame, 
                text=f"Error: {str(e)}",
                font=("Arial", 14),
                text_color="red"
            ).place(relx=0.5, rely=0.5, anchor="center")
    
    def update_language(self):
        """Update all text elements when language changes"""
        # Recreate the entire page with new translations
        for widget in self.winfo_children():
            widget.destroy()
        self.create_widgets()
        self.generate_reports()
