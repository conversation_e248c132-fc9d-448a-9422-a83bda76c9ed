import sqlite3
import os
import sys
import logging
from datetime import datetime

def get_app_path():
    """Get the application path"""
    if getattr(sys, 'frozen', False):
        return os.path.dirname(sys.executable)
    else:
        return os.path.dirname(os.path.abspath(__file__))

class Database:
    def __init__(self, db_name='supermarket.db'):
        app_path = get_app_path()
        db_path = os.path.join(app_path, db_name)
        
        logging.debug(f"Database path: {db_path}")
        
        # Create database file if not exists
        if not os.path.exists(db_path):
            open(db_path, 'a').close()
        
        self.conn = sqlite3.connect(db_path)
        self.cursor = self.conn.cursor()
        self.create_tables()
        self.create_default_data()
    
    def create_tables(self):
        """Create database tables if not exists"""
        # Products table
        self.cursor.execute('''
            CREATE TABLE IF NOT EXISTS products (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                name TEXT NOT NULL UNIQUE,
                price REAL NOT NULL,
                quantity INTEGER DEFAULT 0,
                category TEXT,
                barcode TEXT UNIQUE,
                purchase_price REAL DEFAULT 0
            )
        ''')
        
        # Sales table
        self.cursor.execute('''
            CREATE TABLE IF NOT EXISTS sales (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                product_id INTEGER NOT NULL,
                quantity INTEGER NOT NULL,
                total_price REAL NOT NULL,
                sale_date DATETIME DEFAULT CURRENT_TIMESTAMP,
                FOREIGN KEY (product_id) REFERENCES products(id)
            )
        ''')
        
        # Purchases table
        self.cursor.execute('''
            CREATE TABLE IF NOT EXISTS purchases (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                product_id INTEGER NOT NULL,
                quantity INTEGER NOT NULL,
                purchase_price REAL NOT NULL,
                purchase_date DATETIME DEFAULT CURRENT_TIMESTAMP,
                supplier TEXT,
                FOREIGN KEY (product_id) REFERENCES products(id)
            )
        ''')
        
        # Invoices table
        self.cursor.execute('''
            CREATE TABLE IF NOT EXISTS invoices (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                invoice_type TEXT NOT NULL,
                total_amount REAL NOT NULL,
                invoice_date DATETIME DEFAULT CURRENT_TIMESTAMP,
                details TEXT
            )
        ''')
        
        # Settings table
        self.cursor.execute('''
            CREATE TABLE IF NOT EXISTS settings (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                shop_name TEXT DEFAULT 'Supermarket',
                currency TEXT DEFAULT '$',
                logo_path TEXT,
                backup_path TEXT DEFAULT 'backup'
            )
        ''')
        
        # Users table
        self.cursor.execute('''
            CREATE TABLE IF NOT EXISTS users (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                username TEXT NOT NULL UNIQUE,
                password TEXT NOT NULL,
                role TEXT DEFAULT 'user',
                created_date DATETIME DEFAULT CURRENT_TIMESTAMP
            )
        ''')
        
        # Suppliers table
        self.cursor.execute('''
            CREATE TABLE IF NOT EXISTS suppliers (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                name TEXT NOT NULL,
                phone TEXT,
                email TEXT,
                products TEXT,
                address TEXT,
                last_transaction DATE
            )
        ''')    
        
        # Create indexes
        self.cursor.execute('CREATE INDEX IF NOT EXISTS idx_products_name ON products(name)')
        self.cursor.execute('CREATE INDEX IF NOT EXISTS idx_sales_date ON sales(sale_date)')
        self.cursor.execute('CREATE INDEX IF NOT EXISTS idx_purchases_date ON purchases(purchase_date)')
        self.cursor.execute('CREATE INDEX IF NOT EXISTS idx_users_username ON users(username)')
        
        self.conn.commit()
    
    def create_default_data(self):
        """Create default data if not exists"""
        # Check if settings exist
        self.cursor.execute("SELECT COUNT(*) FROM settings")
        if self.cursor.fetchone()[0] == 0:
            self.cursor.execute('''
                INSERT INTO settings (shop_name, currency, backup_path) 
                VALUES ('سوبر ماركت', '$', 'backup')
            ''')
        
        # Check if admin user exists
        try:
            self.cursor.execute("SELECT COUNT(*) FROM users WHERE username = 'admin'")
            if self.cursor.fetchone()[0] == 0:
                self.cursor.execute('''
                    INSERT INTO users (username, password, role) 
                    VALUES ('admin', 'admin123', 'admin')
                ''')
        except Exception as e:
            logging.error(f"Error creating default user: {str(e)}")
        
        self.conn.commit()
    
    def get_settings(self):
        """Get current settings"""
        self.cursor.execute("SELECT shop_name, currency, backup_path FROM settings LIMIT 1")
        result = self.cursor.fetchone()
        if result:
            return {
                'shop_name': result[0] or 'سوبر ماركت',
                'currency': result[1] or '$',
                'backup_path': result[2] or 'backup'
            }
        return {'shop_name': 'سوبر ماركت', 'currency': '$', 'backup_path': 'backup'}
    
    # ----- Product functions -----
    def add_product(self, name, price, quantity=0, category=None, barcode=None, purchase_price=0):
        """Add a new product"""
        self.cursor.execute('''
            INSERT INTO products (name, price, quantity, category, barcode, purchase_price)
            VALUES (?, ?, ?, ?, ?, ?)
        ''', (name, price, quantity, category, barcode, purchase_price))
        self.conn.commit()
        return self.cursor.lastrowid
    
    def get_products(self, search_query=None):
        """Get products with optional search"""
        if search_query:
            self.cursor.execute('''
                SELECT * FROM products 
                WHERE name LIKE ? OR barcode = ?
            ''', (f'%{search_query}%', search_query))
        else:
            self.cursor.execute('SELECT * FROM products')
        return self.cursor.fetchall()
    
    def get_product_by_id(self, product_id):
        """Get product by ID"""
        self.cursor.execute('SELECT * FROM products WHERE id = ?', (product_id,))
        return self.cursor.fetchone()
    
    def get_product_by_name(self, name):
        """Get product by name"""
        self.cursor.execute('SELECT * FROM products WHERE name = ?', (name,))
        return self.cursor.fetchone()
    
    def get_products_by_name_or_barcode(self, search_term):
        """Search products by name or barcode"""
        self.cursor.execute('''
            SELECT * FROM products 
            WHERE name LIKE ? OR barcode = ?
        ''', (f'%{search_term}%', search_term))
        return self.cursor.fetchall()
    
    def get_low_stock_products(self, threshold=5):
        """Get low stock products"""
        self.cursor.execute('''
            SELECT * FROM products 
            WHERE quantity < ?
        ''', (threshold,))
        return self.cursor.fetchall()
    
    def get_daily_sales(self, date):
        """Get daily sales total"""
        self.cursor.execute('''
            SELECT SUM(total_price) FROM sales 
            WHERE DATE(sale_date) = ?
        ''', (date,))
        result = self.cursor.fetchone()
        return result[0] if result[0] else 0.0
    
    def get_daily_purchases(self, date):
        """Get daily purchases total"""
        self.cursor.execute('''
            SELECT SUM(purchase_price * quantity) FROM purchases 
            WHERE DATE(purchase_date) = ?
        ''', (date,))
        result = self.cursor.fetchone()
        return result[0] if result[0] else 0.0
    
    def update_product_quantity(self, product_id, quantity_change):
        """Update product quantity"""
        self.cursor.execute('''
            UPDATE products SET quantity = quantity + ? WHERE id = ?
        ''', (quantity_change, product_id))
        self.conn.commit()
    
    # ----- Sales functions -----
    def record_sale(self, product_id, quantity):
        """Record a sale"""
        product = self.get_product_by_id(product_id)
        if not product:
            return None
        
        price = product[2]
        total_price = price * quantity
        
        self.cursor.execute('''
            INSERT INTO sales (product_id, quantity, total_price)
            VALUES (?, ?, ?)
        ''', (product_id, quantity, total_price))
        
        self.update_product_quantity(product_id, -quantity)
        
        self.conn.commit()
        return self.cursor.lastrowid
    
    def create_sale_invoice(self, total_amount, details):
        """Create a sale invoice"""
        try:
            self.cursor.execute('''
                INSERT INTO invoices (invoice_type, total_amount, details)
                VALUES ('sale', ?, ?)
            ''', (total_amount, details))
            self.conn.commit()
            return self.cursor.lastrowid
        except Exception as e:
            logging.error(f"Error creating invoice: {str(e)}")
            return None
    
    # ----- Purchases functions -----
    def record_purchase(self, product_id, quantity, purchase_price, supplier=None):
        """Record a purchase"""
        self.cursor.execute('''
            INSERT INTO purchases (product_id, quantity, purchase_price, supplier)
            VALUES (?, ?, ?, ?)
        ''', (product_id, quantity, purchase_price, supplier))
        
        self.update_product_quantity(product_id, quantity)
        
        self.conn.commit()
        return self.cursor.lastrowid
    
    def create_purchase_invoice(self, total_amount, details):
        """Create a purchase invoice"""
        self.cursor.execute('''
            INSERT INTO invoices (invoice_type, total_amount, details)
            VALUES ('purchase', ?, ?)
        ''', (total_amount, details))
        self.conn.commit()
        return self.cursor.lastrowid
    
    # ----- Suppliers functions -----
    def add_supplier(self, name, phone, email, products, address):
        """Add a new supplier"""
        self.cursor.execute('''
            INSERT INTO suppliers (name, phone, email, products, address)
            VALUES (?, ?, ?, ?, ?)
        ''', (name, phone, email, products, address))
        self.conn.commit()
    
    def get_suppliers(self):
        """Get all suppliers"""
        self.cursor.execute('SELECT * FROM suppliers')
        return self.cursor.fetchall()
    
    def update_supplier_last_transaction(self, supplier_name, date):
        """Update supplier's last transaction date"""
        self.cursor.execute('''
            UPDATE suppliers 
            SET last_transaction = ?
            WHERE name = ?
        ''', (date, supplier_name))
        self.conn.commit()

    def close(self):
        """Close the database connection"""
        self.conn.close()

    def get_sales_by_date_range(self, start_date, end_date):
        """Get sales data by date range"""
        self.cursor.execute('''
            SELECT DATE(sale_date) as sale_date, SUM(total_price) as total_amount
            FROM sales 
            WHERE DATE(sale_date) BETWEEN ? AND ?
            GROUP BY DATE(sale_date)
            ORDER BY sale_date
        ''', (start_date, end_date))
        return self.cursor.fetchall()
    
    def get_total_sales_by_date_range(self, start_date, end_date):
        """Get total sales amount by date range"""
        self.cursor.execute('''
            SELECT SUM(total_price) FROM sales 
            WHERE DATE(sale_date) BETWEEN ? AND ?
        ''', (start_date, end_date))
        result = self.cursor.fetchone()
        return result[0] if result[0] else 0.0
    
    def get_total_purchases_by_date_range(self, start_date, end_date):
        """Get total purchases amount by date range"""
        self.cursor.execute('''
            SELECT SUM(purchase_price * quantity) FROM purchases 
            WHERE DATE(purchase_date) BETWEEN ? AND ?
        ''', (start_date, end_date))
        result = self.cursor.fetchone()
        return result[0] if result[0] else 0.0
    
    def get_sales_by_product(self, start_date, end_date):
        """Get sales data by product"""
        self.cursor.execute('''
            SELECT p.name, SUM(s.quantity) as total_qty, SUM(s.total_price) as total_amount
            FROM sales s
            JOIN products p ON s.product_id = p.id
            WHERE DATE(s.sale_date) BETWEEN ? AND ?
            GROUP BY p.id, p.name
            ORDER BY total_amount DESC
        ''', (start_date, end_date))
        return self.cursor.fetchall()
    
    def get_monthly_sales(self, year):
        """Get monthly sales for a specific year"""
        self.cursor.execute('''
            SELECT strftime('%m', sale_date) as month, SUM(total_price) as total_amount
            FROM sales 
            WHERE strftime('%Y', sale_date) = ?
            GROUP BY strftime('%m', sale_date)
            ORDER BY month
        ''', (str(year),))
        return self.cursor.fetchall()
    
    def get_top_selling_products(self, limit=10):
        """Get top selling products"""
        self.cursor.execute('''
            SELECT p.name, SUM(s.quantity) as total_sold, SUM(s.total_price) as total_revenue
            FROM sales s
            JOIN products p ON s.product_id = p.id
            GROUP BY p.id, p.name
            ORDER BY total_sold DESC
            LIMIT ?
        ''', (limit,))
        return self.cursor.fetchall()
    
    def get_profit_by_product(self, start_date, end_date):
        """Get profit by product"""
        self.cursor.execute('''
            SELECT p.name, 
                   SUM(s.quantity) as sold_qty,
                   SUM(s.total_price) as revenue,
                   SUM(s.quantity * p.purchase_price) as cost,
                   SUM(s.total_price - (s.quantity * p.purchase_price)) as profit
            FROM sales s
            JOIN products p ON s.product_id = p.id
            WHERE DATE(s.sale_date) BETWEEN ? AND ?
            GROUP BY p.id, p.name
            ORDER BY profit DESC
        ''', (start_date, end_date))
        return self.cursor.fetchall()
    
    def get_all_products(self):
        """Get all products"""
        self.cursor.execute('SELECT * FROM products')
        return self.cursor.fetchall()
