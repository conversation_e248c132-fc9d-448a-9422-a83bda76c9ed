import customtkinter as ctk
from database import Database
import matplotlib.pyplot as plt
from matplotlib.backends.backend_tkagg import FigureCanvasTkAgg
import datetime

class HomePage(ctk.CTkFrame):
    def __init__(self, parent, app):
        super().__init__(parent, fg_color="transparent")
        self.app = app
        self.db = Database()
        
        # Responsive settings
        self.grid_columnconfigure(0, weight=1)
        self.grid_rowconfigure(0, weight=1)
        
        self.create_widgets()
        self.refresh_data()
    
    def create_widgets(self):
        # Main frame
        main_frame = ctk.CTkFrame(self)
        main_frame.grid(row=0, column=0, sticky="nsew", padx=10, pady=10)
        main_frame.grid_columnconfigure(0, weight=1)
        main_frame.grid_rowconfigure(4, weight=1)
        
        # Title
        title = ctk.CTkLabel(
            main_frame, 
            text="Dashboard",
            font=("Arial", 24, "bold"),
            text_color="#2c3e50"
        )
        title.grid(row=0, column=0, pady=20, sticky="n")
        
        # Stats cards frame
        self.stats_frame = ctk.CTkFrame(main_frame, fg_color="transparent")
        self.stats_frame.grid(row=1, column=0, sticky="nsew", padx=20, pady=10)
        self.stats_frame.grid_columnconfigure(0, weight=1)
        
        # Chart frame
        self.chart_frame = ctk.CTkFrame(main_frame, fg_color="transparent")
        self.chart_frame.grid(row=2, column=0, sticky="nsew", padx=20, pady=10)
        self.chart_frame.grid_columnconfigure(0, weight=1)
        self.chart_frame.grid_rowconfigure(0, weight=1)
        
        # Alerts frame
        self.alerts_frame = ctk.CTkFrame(
            main_frame, 
            corner_radius=12,
            border_width=1,
            border_color="#e0e0e0"
        )
        self.alerts_frame.grid(row=3, column=0, sticky="nsew", padx=20, pady=10)
        self.alerts_frame.grid_columnconfigure(0, weight=1)
        self.alerts_frame.grid_rowconfigure(1, weight=1)
        
        # Create widgets
        self.create_stats_cards()
        self.create_sales_chart()
        self.create_low_stock_alerts()
    
    def create_stats_cards(self):
        """Create statistics cards"""
        # Calculate stats
        total_products = len(self.db.get_products())
        low_stock_count = len(self.db.get_low_stock_products(threshold=5))
        today = datetime.datetime.now().strftime("%Y-%m-%d")
        today_sales = self.db.get_daily_sales(today)
        today_purchases = self.db.get_daily_purchases(today)
        
        cards = [
            (self.app.lang.get("total_products"), str(total_products), "#4CAF50"),
            (self.app.lang.get("today_sales"), f"{today_sales:.2f} $", "#2196F3"),
            (self.app.lang.get("today_purchases"), f"{today_purchases:.2f} $", "#FF9800"),
            (self.app.lang.get("low_stock"), str(low_stock_count), "#F44336")
        ]
        
        # Clear old cards
        for widget in self.stats_frame.winfo_children():
            widget.destroy()
        
        # Create new cards
        for i, (title_text, value, color) in enumerate(cards):
            card = ctk.CTkFrame(
                self.stats_frame, 
                corner_radius=12,
                border_width=1,
                border_color="#e0e0e0"
            )
            card.grid(row=0, column=i, padx=10, pady=10, sticky="nsew")
            self.stats_frame.grid_columnconfigure(i, weight=1)
            
            ctk.CTkLabel(
                card, 
                text=title_text,
                font=("Arial", 14),
                text_color="#555555"
            ).pack(pady=(15, 5))
            
            ctk.CTkLabel(
                card, 
                text=value, 
                font=("Arial", 22, "bold"),
                text_color=color
            ).pack(pady=5)
    
    def create_sales_chart(self):
        """Create sales chart"""
        # Clear old chart
        for widget in self.chart_frame.winfo_children():
            widget.destroy()
        
        fig, ax = plt.subplots(figsize=(8, 4))
        ax.set_title("Last 7 Days Sales", fontname="Arial")
        ax.set_ylabel("Amount ($)", fontname="Arial")
        
        # Example data
        days = ['Mon', 'Tue', 'Wed', 'Thu', 'Fri', 'Sat', 'Sun']
        sales = [4500, 5200, 4800, 6000, 5500, 7000, 6500]
        
        ax.bar(days, sales, color='#2196F3')
        ax.grid(axis='y', linestyle='--', alpha=0.7)
        
        # Embed chart in GUI
        canvas = FigureCanvasTkAgg(fig, master=self.chart_frame)
        canvas.draw()
        canvas.get_tk_widget().pack(fill="both", expand=True)
    
    def create_low_stock_alerts(self):
        """Create low stock alerts"""
        # Clear old alerts
        for widget in self.alerts_frame.winfo_children():
            if widget.winfo_name() != "!ctklabel":
                widget.destroy()
        
        # Title
        ctk.CTkLabel(
            self.alerts_frame, 
            text="Low Stock Alerts",
            font=("Arial", 16, "bold"),
            text_color="#2c3e50"
        ).grid(row=0, column=0, pady=10, sticky="nw")
        
        # Scrollable frame
        scrollable_frame = ctk.CTkScrollableFrame(self.alerts_frame)
        scrollable_frame.grid(row=1, column=0, sticky="nsew", padx=10, pady=10)
        scrollable_frame.grid_columnconfigure(0, weight=1)
        
        # Get low stock items
        low_stock_items = self.db.get_low_stock_products(threshold=5)
        
        if not low_stock_items:
            ctk.CTkLabel(
                scrollable_frame, 
                text="No low stock items",
                font=("Arial", 14),
                text_color="#555555"
            ).pack(pady=20)
            return
        
        # Create table
        table_frame = ctk.CTkFrame(scrollable_frame)
        table_frame.pack(fill="x", expand=True)
        
        # Headers
        headers = ctk.CTkFrame(table_frame, height=40, fg_color="#f5f5f5")
        headers.pack(fill="x", pady=(0, 5))
        
        columns = ["Product", "Available Qty", "Category"]
        for i, col in enumerate(columns):
            ctk.CTkLabel(
                headers, 
                text=col,
                font=("Arial", 14, "bold"),
                text_color="#2c3e50"
            ).place(relx=0.2 + i*0.3, rely=0.5, anchor="center")
        
        # Items
        for item in low_stock_items:
            row = ctk.CTkFrame(table_frame, height=40)
            row.pack(fill="x", pady=2)
            
            product_name = item[1]
            quantity = item[3]
            category = item[4] or "No category"
            
            ctk.CTkLabel(
                row, 
                text=product_name,
                font=("Arial", 13),
                text_color="#555555"
            ).place(relx=0.2, rely=0.5, anchor="center")
            
            ctk.CTkLabel(
                row, 
                text=str(quantity),
                font=("Arial", 13, "bold"),
                text_color="#e74c3c"
            ).place(relx=0.5, rely=0.5, anchor="center")
            
            ctk.CTkLabel(
                row, 
                text=category,
                font=("Arial", 13),
                text_color="#555555"
            ).place(relx=0.8, rely=0.5, anchor="center")
    
    def refresh_data(self):
        """Refresh dashboard data"""
        self.create_stats_cards()
        self.create_sales_chart()
        self.create_low_stock_alerts()
    
    def update_language(self):
        """Update all text elements when language changes"""
        # Recreate the entire page with new translations
        for widget in self.winfo_children():
            widget.destroy()
        self.create_widgets()
        self.refresh_data()
