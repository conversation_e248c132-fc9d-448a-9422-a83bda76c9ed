2025-07-23 20:49:56,433 - INFO - Operating System: win32
2025-07-23 20:49:56,436 - INFO - Python Version: 3.12.8 (tags/v3.12.8:2dc476b, Dec  3 2024, 19:30:04) [MSC v.1942 64 bit (AMD64)]
2025-07-23 20:49:56,436 - INFO - Current Directory: C:\Users\<USER>\Desktop\SupermarketSystem
2025-07-23 20:49:56,444 - INFO - Executable Path: C:\Users\<USER>\AppData\Local\Programs\Python\Python312\python.exe
2025-07-23 20:49:56,445 - INFO - Application started
2025-07-23 20:49:57,479 - DEBUG - Database path: c:\Users\<USER>\Desktop\SupermarketSystem\supermarket.db
2025-07-23 20:49:57,496 - ERROR - Unexpected error:
Traceback (most recent call last):
  File "c:\Users\<USER>\Desktop\SupermarketSystem\main.py", line 274, in <module>
    app = SupermarketApp()
          ^^^^^^^^^^^^^^^^
  File "c:\Users\<USER>\Desktop\SupermarketSystem\main.py", line 74, in __init__
    self.db = Database()
              ^^^^^^^^^^
  File "c:\Users\<USER>\Desktop\SupermarketSystem\database.py", line 28, in __init__
    self.create_default_data()
  File "c:\Users\<USER>\Desktop\SupermarketSystem\database.py", line 123, in create_default_data
    self.cursor.execute("SELECT COUNT(*) FROM users WHERE username = 'admin'")
sqlite3.OperationalError: no such table: users

