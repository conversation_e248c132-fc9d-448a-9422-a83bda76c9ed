import customtkinter as ctk
from tkinter import messagebox
from database import Database
from pdf_utils import PDFGenerator
import os
import subprocess
import sys
from datetime import datetime, timedelta

class InvoicesPage(ctk.CTkFrame):
    def __init__(self, parent, app):
        super().__init__(parent, fg_color="transparent")
        self.app = app
        self.db = Database()
        
        # Responsive settings
        self.grid_columnconfigure(0, weight=1)
        self.grid_rowconfigure(0, weight=1)
        
        self.create_widgets()
        self.refresh_data()
    
    def create_widgets(self):
        # Main frame
        main_frame = ctk.CTkFrame(self)
        main_frame.grid(row=0, column=0, sticky="nsew", padx=10, pady=10)
        main_frame.grid_columnconfigure(0, weight=1)
        main_frame.grid_rowconfigure(1, weight=1)
        
        # Title
        title = ctk.CTkLabel(
            main_frame, 
            text="Invoices Management",
            font=("Arial", 24, "bold"),
            text_color="#2c3e50"
        )
        title.grid(row=0, column=0, pady=20, sticky="n")
        
        # Filters frame
        filters_frame = ctk.CTkFrame(main_frame, fg_color="transparent")
        filters_frame.grid(row=1, column=0, sticky="ew", pady=10)
        
        # Date filters
        ctk.CTkLabel(filters_frame, text="From:").grid(row=0, column=0, padx=5)
        self.start_date = ctk.CTkEntry(filters_frame, width=120)
        self.start_date.grid(row=0, column=1, padx=5)
        self.start_date.insert(0, (datetime.now() - timedelta(days=30)).strftime("%Y-%m-%d"))
        
        ctk.CTkLabel(filters_frame, text="To:").grid(row=0, column=2, padx=5)
        self.end_date = ctk.CTkEntry(filters_frame, width=120)
        self.end_date.grid(row=0, column=3, padx=5)
        self.end_date.insert(0, datetime.now().strftime("%Y-%m-%d"))
        
        # Invoice type filter
        ctk.CTkLabel(filters_frame, text="Type:").grid(row=0, column=4, padx=5)
        self.invoice_type_var = ctk.StringVar(value="all")
        ctk.CTkComboBox(
            filters_frame,
            values=["All", "Sale", "Purchase"],
            variable=self.invoice_type_var,
            width=120
        ).grid(row=0, column=5, padx=5)
        
        # Search button
        search_btn = ctk.CTkButton(
            filters_frame, 
            text="Search", 
            width=80,
            command=self.refresh_data
        )
        search_btn.grid(row=0, column=6, padx=10)
        
        # Invoices table
        table_container = ctk.CTkFrame(main_frame, fg_color="transparent")
        table_container.grid(row=2, column=0, sticky="nsew", pady=10)
        table_container.grid_columnconfigure(0, weight=1)
        table_container.grid_rowconfigure(0, weight=1)
        
        # Table headers
        headers = ctk.CTkFrame(table_container, height=40, fg_color="#f5f5f5")
        headers.pack(fill="x")
        
        columns = ["ID", "Date", "Type", "Amount", "Details"]
        col_widths = [0.1, 0.2, 0.15, 0.15, 0.4]
        for i, col in enumerate(columns):
            ctk.CTkLabel(
                headers, 
                text=col,
                font=("Arial", 14, "bold"),
                text_color="#2c3e50"
            ).place(relx=sum(col_widths[:i]) + col_widths[i]/2, rely=0.5, anchor="center")
        
        # Scrollable table
        self.scrollable_frame = ctk.CTkScrollableFrame(
            table_container, 
            height=300,
            fg_color="transparent"
        )
        self.scrollable_frame.pack(fill="both", expand=True)
        
        # Action buttons
        btn_frame = ctk.CTkFrame(main_frame, fg_color="transparent")
        btn_frame.grid(row=3, column=0, sticky="ew", pady=10)
        btn_frame.grid_columnconfigure(0, weight=1)
        
        view_btn = ctk.CTkButton(
            btn_frame, 
            text="View Invoice",
            width=150,
            height=40,
            corner_radius=8,
            font=("Arial", 14, "bold"),
            command=self.view_invoice
        )
        view_btn.grid(row=0, column=0, padx=5)
        
        delete_btn = ctk.CTkButton(
            btn_frame, 
            text="Delete Invoice",
            width=150,
            height=40,
            corner_radius=8,
            font=("Arial", 14, "bold"),
            fg_color="#e74c3c",
            hover_color="#c0392b",
            command=self.delete_invoice
        )
        delete_btn.grid(row=0, column=1, padx=5)
    
    def refresh_data(self):
        """Refresh invoices data"""
        # Get filter values
        start_date = self.start_date.get()
        end_date = self.end_date.get()
        invoice_type = self.invoice_type_var.get().lower()
        
        # Clear old data
        for widget in self.scrollable_frame.winfo_children():
            widget.destroy()
        
        # Fetch invoices
        query = """
            SELECT * FROM invoices 
            WHERE invoice_date BETWEEN ? AND ?
        """
        params = [start_date, end_date]
        
        if invoice_type != "all":
            query += " AND invoice_type = ?"
            params.append(invoice_type)
            
        query += " ORDER BY invoice_date DESC"
        
        self.db.cursor.execute(query, params)
        invoices = self.db.cursor.fetchall()
        
        # Display invoices
        for invoice in invoices:
            row = ctk.CTkFrame(self.scrollable_frame, height=40)
            row.pack(fill="x", pady=2)
            row.invoice_id = invoice[0]
            
            # Invoice data
            invoice_id = f"#{invoice[0]}"
            date = invoice[3].split()[0]  # Get date part only
            inv_type = "Sale" if invoice[1] == "sale" else "Purchase"
            amount = f"{invoice[2]:.2f} $"
            details = invoice[4][:30] + "..." if len(invoice[4]) > 30 else invoice[4]
            
            data = [invoice_id, date, inv_type, amount, details]
            col_widths = [0.1, 0.2, 0.15, 0.15, 0.4]
            
            for i, value in enumerate(data):
                ctk.CTkLabel(
                    row, 
                    text=value,
                    font=("Arial", 13),
                    text_color="#555555"
                ).place(relx=sum(col_widths[:i]) + col_widths[i]/2, rely=0.5, anchor="center")
            
            # Make row selectable
            row.bind("<Button-1>", lambda e, r=row: self.select_invoice(r))
    
    def select_invoice(self, row):
        """Select an invoice"""
        for widget in self.scrollable_frame.winfo_children():
            if hasattr(widget, "configure"):
                widget.configure(fg_color="transparent")
        row.configure(fg_color="#e3f2fd")
        self.selected_invoice = row.invoice_id
    
    def view_invoice(self):
        """View selected invoice"""
        if not hasattr(self, "selected_invoice"):
            messagebox.showwarning("Warning", "Please select an invoice")
            return
            
        # Get invoice data from database
        self.db.cursor.execute("SELECT * FROM invoices WHERE id = ?", (self.selected_invoice,))
        invoice = self.db.cursor.fetchone()
        
        if not invoice:
            messagebox.showerror("Error", "Invoice not found")
            return
            
        # Get application path
        app_path = os.path.dirname(os.path.abspath(__file__))
        invoices_dir = os.path.join(app_path, "invoices")
        
        # Create directory if it doesn't exist
        if not os.path.exists(invoices_dir):
            os.makedirs(invoices_dir)
            
        # Determine invoice file path
        inv_type = "sale" if invoice[1] == "sale" else "purchase"
        filename = os.path.join(invoices_dir, f"{inv_type}_invoice_{invoice[0]}.pdf")

        
        if not os.path.exists(filename):
            messagebox.showerror("Error", "Invoice file not found")
            return
            
        # Open the invoice
        try:
            if os.name == 'nt':  # Windows
                os.startfile(filename)
            elif os.name == 'posix':  # macOS, Linux
                if sys.platform == 'darwin':
                    subprocess.run(['open', filename])
                else:
                    subprocess.run(['xdg-open', filename])
            else:
                messagebox.showinfo("Open Invoice", f"Invoice saved at: {filename}")
        except Exception as e:
            messagebox.showerror("Error", f"Failed to open invoice: {str(e)}")
    
    def delete_invoice(self):
        """Delete selected invoice"""
        if not hasattr(self, "selected_invoice"):
            messagebox.showwarning("Warning", "Please select an invoice")
            return
            
        if not messagebox.askyesno("Confirm", "Are you sure you want to delete this invoice?"):
            return
            
        try:
            # Delete from database
            self.db.cursor.execute("DELETE FROM invoices WHERE id = ?", (self.selected_invoice,))
            self.db.conn.commit()
            
            # Delete PDF file
            for inv_type in ["sale", "purchase"]:
                filename = os.path.join("invoices", f"{inv_type}_invoice_{self.selected_invoice}.pdf")
                if os.path.exists(filename):
                    os.remove(filename)
            
            messagebox.showinfo("Success", "Invoice deleted successfully")
            self.refresh_data()
        except Exception as e:
            messagebox.showerror("Error", f"Failed to delete invoice: {str(e)}")

    def update_language(self):
        """Update all text elements when language changes"""
        # Recreate the entire page with new translations
        for widget in self.winfo_children():
            widget.destroy()
        self.create_widgets()
        self.refresh_data()
