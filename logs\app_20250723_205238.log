2025-07-23 20:52:38,474 - INFO - Operating System: win32
2025-07-23 20:52:38,474 - INFO - Python Version: 3.12.8 (tags/v3.12.8:2dc476b, Dec  3 2024, 19:30:04) [MSC v.1942 64 bit (AMD64)]
2025-07-23 20:52:38,474 - INFO - Current Directory: C:\Users\<USER>\Desktop\SupermarketSystem
2025-07-23 20:52:38,474 - INFO - Executable Path: C:\Users\<USER>\AppData\Local\Programs\Python\Python312\python.exe
2025-07-23 20:52:38,474 - INFO - Application started
2025-07-23 20:52:38,959 - DEBUG - Database path: c:\Users\<USER>\Desktop\SupermarketSystem\supermarket.db
2025-07-23 20:52:39,104 - ERROR - Unexpected error:
Traceback (most recent call last):
  File "c:\Users\<USER>\Desktop\SupermarketSystem\main.py", line 274, in <module>
    app = SupermarketApp()
          ^^^^^^^^^^^^^^^^
  File "c:\Users\<USER>\Desktop\SupermarketSystem\main.py", line 86, in __init__
    self.lang.update_shop_name(shop_name)
  File "c:\Users\<USER>\Desktop\SupermarketSystem\language.py", line 328, in update_shop_name
    self.notify_all()
  File "c:\Users\<USER>\Desktop\SupermarketSystem\language.py", line 289, in notify_all
    callback()
  File "c:\Users\<USER>\Desktop\SupermarketSystem\main.py", line 138, in update_language
    self.sidebar.destroy()
    ^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\tkinter\__init__.py", line 2433, in __getattr__
    return getattr(self.tk, attr)
           ^^^^^^^^^^^^^^^^^^^^^^
AttributeError: '_tkinter.tkapp' object has no attribute 'sidebar'

