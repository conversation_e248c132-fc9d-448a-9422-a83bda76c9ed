import customtkinter as ctk
from tkinter import messagebox
from database import Database
import tkinter as tk

class SuppliersPage(ctk.CTkFrame):
    def __init__(self, parent, app):
        super().__init__(parent, fg_color="transparent")
        self.app = app
        self.db = Database()
        self.selected_supplier_id = None
        
        # Responsive settings
        self.grid_columnconfigure(0, weight=1)
        self.grid_rowconfigure(0, weight=1)
        
        self.create_widgets()
        self.refresh_data()
    
    def create_widgets(self):
        # Main frame
        main_frame = ctk.CTkFrame(self)
        main_frame.grid(row=0, column=0, sticky="nsew", padx=10, pady=10)
        main_frame.grid_columnconfigure(0, weight=1)
        main_frame.grid_rowconfigure(1, weight=1)
        
        # Title
        title = ctk.CTkLabel(
            main_frame, 
            text="Suppliers Management",
            font=("Arial", 24, "bold"),
            text_color="#2c3e50"
        )
        title.grid(row=0, column=0, pady=20, sticky="n")
        
        # Search and controls
        controls_frame = ctk.CTkFrame(main_frame, fg_color="transparent")
        controls_frame.grid(row=1, column=0, sticky="ew", pady=10)
        
        # Search
        ctk.CTkLabel(controls_frame, text="Search:").grid(row=0, column=0, padx=5)
        self.search_entry = ctk.CTkEntry(controls_frame, width=200)
        self.search_entry.grid(row=0, column=1, padx=5)
        self.search_entry.bind("<Return>", lambda e: self.refresh_data())
        
        search_btn = ctk.CTkButton(
            controls_frame, 
            text="Search", 
            width=80,
            command=self.refresh_data
        )
        search_btn.grid(row=0, column=2, padx=5)
        
        # Action buttons
        add_btn = ctk.CTkButton(
            controls_frame, 
            text="Add Supplier",
            width=120,
            command=self.add_supplier
        )
        add_btn.grid(row=0, column=3, padx=10)
        
        edit_btn = ctk.CTkButton(
            controls_frame, 
            text="Edit Supplier",
            width=120,
            command=self.edit_supplier
        )
        edit_btn.grid(row=0, column=4, padx=5)
        
        delete_btn = ctk.CTkButton(
            controls_frame, 
            text="Delete Supplier",
            width=120,
            fg_color="#e74c3c",
            hover_color="#c0392b",
            command=self.delete_supplier
        )
        delete_btn.grid(row=0, column=5, padx=5)
        
        # Suppliers table
        table_container = ctk.CTkFrame(main_frame, fg_color="transparent")
        table_container.grid(row=2, column=0, sticky="nsew", pady=10)
        table_container.grid_columnconfigure(0, weight=1)
        table_container.grid_rowconfigure(0, weight=1)
        
        # Table headers
        headers = ctk.CTkFrame(table_container, height=40, fg_color="#f5f5f5")
        headers.pack(fill="x")
        
        columns = ["Name", "Phone", "Email", "Products", "Last Transaction"]
        col_widths = [0.25, 0.15, 0.2, 0.25, 0.15]
        for i, col in enumerate(columns):
            ctk.CTkLabel(
                headers, 
                text=col,
                font=("Arial", 14, "bold"),
                text_color="#2c3e50"
            ).place(relx=sum(col_widths[:i]) + col_widths[i]/2, rely=0.5, anchor="center")
        
        # Scrollable table
        self.scrollable_frame = ctk.CTkScrollableFrame(
            table_container, 
            height=300,
            fg_color="transparent"
        )
        self.scrollable_frame.pack(fill="both", expand=True)
    
    def refresh_data(self):
        """Refresh suppliers data"""
        query = self.search_entry.get()
        suppliers = self.db.get_suppliers()
        
        # Filter suppliers by name
        if query:
            suppliers = [s for s in suppliers if query.lower() in s[1].lower()]
        
        # Clear old data
        for widget in self.scrollable_frame.winfo_children():
            widget.destroy()
        
        # Display suppliers
        for supplier in suppliers:
            row = ctk.CTkFrame(self.scrollable_frame, height=40)
            row.pack(fill="x", pady=2)
            row.supplier_id = supplier[0]
            
            # Supplier data
            data = [
                supplier[1],  # name
                supplier[2] or "N/A",  # phone
                supplier[3] or "N/A",  # email
                supplier[4][:20] + "..." if supplier[4] and len(supplier[4]) > 20 else supplier[4] or "N/A",  # products
                supplier[6] or "N/A"  # last transaction
            ]
            
            col_widths = [0.25, 0.15, 0.2, 0.25, 0.15]
            
            for i, value in enumerate(data):
                ctk.CTkLabel(
                    row, 
                    text=value,
                    font=("Arial", 13),
                    text_color="#555555"
                ).place(relx=sum(col_widths[:i]) + col_widths[i]/2, rely=0.5, anchor="center")
            
            # Make row selectable
            row.bind("<Button-1>", lambda e, r=row: self.select_supplier(r))
    
    def select_supplier(self, row):
        """Select a supplier"""
        for widget in self.scrollable_frame.winfo_children():
            if hasattr(widget, "configure"):
                widget.configure(fg_color="transparent")
        row.configure(fg_color="#e3f2fd")
        self.selected_supplier_id = row.supplier_id
    
    def add_supplier(self):
        """Open add supplier dialog"""
        dialog = ctk.CTkToplevel(self)
        dialog.title("Add New Supplier")
        dialog.geometry("500x400")
        dialog.transient(self)
        dialog.grab_set()
        
        # Form fields
        fields = [
            ("Name", "entry"),
            ("Phone", "entry"),
            ("Email", "entry"),
            ("Products", "entry"),
            ("Address", "entry")
        ]
        
        entries = {}
        for i, (label, field_type) in enumerate(fields):
            ctk.CTkLabel(dialog, text=label + ":").grid(row=i, column=0, padx=10, pady=5, sticky="e")
            entry = ctk.CTkEntry(dialog, width=300)
            entry.grid(row=i, column=1, padx=10, pady=5, sticky="w")
            entries[label.lower()] = entry
        
        # Save button
        def save_supplier():
            try:
                name = entries["name"].get()
                phone = entries["phone"].get() or None
                email = entries["email"].get() or None
                products = entries["products"].get() or None
                address = entries["address"].get() or None
                
                if not name:
                    raise ValueError("Supplier name is required")
                
                self.db.add_supplier(name, phone, email, products, address)
                messagebox.showinfo("Success", "Supplier added successfully")
                self.refresh_data()
                dialog.destroy()
            except Exception as e:
                messagebox.showerror("Error", f"Failed to add supplier: {str(e)}")
        
        save_btn = ctk.CTkButton(
            dialog, 
            text="Save Supplier",
            width=150,
            height=40,
            command=save_supplier
        )
        save_btn.grid(row=len(fields), column=0, columnspan=2, pady=20)
    
    def edit_supplier(self):
        """Edit selected supplier"""
        if not self.selected_supplier_id:
            messagebox.showwarning("Warning", "Please select a supplier")
            return
        
        self.db.cursor.execute("SELECT * FROM suppliers WHERE id=?", (self.selected_supplier_id,))
        supplier = self.db.cursor.fetchone()
        
        if not supplier:
            messagebox.showerror("Error", "Supplier not found")
            return
        
        dialog = ctk.CTkToplevel(self)
        dialog.title("Edit Supplier")
        dialog.geometry("500x400")
        dialog.transient(self)
        dialog.grab_set()
        
        # Form fields
        fields = [
            ("Name", "entry", supplier[1]),
            ("Phone", "entry", supplier[2]),
            ("Email", "entry", supplier[3]),
            ("Products", "entry", supplier[4]),
            ("Address", "entry", supplier[5])
        ]
        
        entries = {}
        for i, (label, field_type, value) in enumerate(fields):
            ctk.CTkLabel(dialog, text=label + ":").grid(row=i, column=0, padx=10, pady=5, sticky="e")
            entry = ctk.CTkEntry(dialog, width=300)
            entry.insert(0, str(value) if value is not None else "")
            entry.grid(row=i, column=1, padx=10, pady=5, sticky="w")
            entries[label.lower()] = entry
        
        # Save button
        def update_supplier():
            try:
                name = entries["name"].get()
                phone = entries["phone"].get() or None
                email = entries["email"].get() or None
                products = entries["products"].get() or None
                address = entries["address"].get() or None
                
                if not name:
                    raise ValueError("Supplier name is required")
                
                # Update supplier
                self.db.cursor.execute('''
                    UPDATE suppliers 
                    SET name=?, phone=?, email=?, products=?, address=?
                    WHERE id=?
                ''', (name, phone, email, products, address, self.selected_supplier_id))
                self.db.conn.commit()
                
                messagebox.showinfo("Success", "Supplier updated successfully")
                self.refresh_data()
                dialog.destroy()
            except Exception as e:
                messagebox.showerror("Error", f"Failed to update supplier: {str(e)}")
        
        save_btn = ctk.CTkButton(
            dialog, 
            text="Update Supplier",
            width=150,
            height=40,
            command=update_supplier
        )
        save_btn.grid(row=len(fields), column=0, columnspan=2, pady=20)
    
    def delete_supplier(self):
        """Delete selected supplier"""
        if not self.selected_supplier_id:
            messagebox.showwarning("Warning", "Please select a supplier")
            return
        
        if not messagebox.askyesno("Confirm", "Are you sure you want to delete this supplier?"):
            return
            
        try:
            self.db.cursor.execute("DELETE FROM suppliers WHERE id=?", (self.selected_supplier_id,))
            self.db.conn.commit()
            messagebox.showinfo("Success", "Supplier deleted successfully")
            self.refresh_data()
        except Exception as e:
            messagebox.showerror("Error", f"Failed to delete supplier: {str(e)}")
    
    def update_language(self):
        """Update all text elements when language changes"""
        # Recreate the entire page with new translations
        for widget in self.winfo_children():
            widget.destroy()
        self.create_widgets()
        self.refresh_data()
