import customtkinter as ctk
from database import Database
import datetime

class SalesHistoryPage(ctk.CTkFrame):
    def __init__(self, parent, app):
        super().__init__(parent, fg_color="transparent")
        self.app = app
        self.db = Database()
        
        # Responsive settings
        self.grid_columnconfigure(0, weight=1)
        self.grid_rowconfigure(0, weight=1)
        
        self.create_widgets()
        self.refresh_data()
    
    def create_widgets(self):
        # Main frame
        main_frame = ctk.CTkFrame(self)
        main_frame.grid(row=0, column=0, sticky="nsew", padx=10, pady=10)
        main_frame.grid_columnconfigure(0, weight=1)
        main_frame.grid_rowconfigure(1, weight=1)
        
        # Title
        title = ctk.CTkLabel(
            main_frame, 
            text="Sales History",
            font=("Arial", 24, "bold"),
            text_color="#2c3e50"
        )
        title.grid(row=0, column=0, pady=20, sticky="n")
        
        # Filters frame
        filters_frame = ctk.CTkFrame(main_frame, fg_color="transparent")
        filters_frame.grid(row=1, column=0, sticky="ew", pady=10)
        
        # Date filters
        ctk.CTkLabel(filters_frame, text="From:").grid(row=0, column=0, padx=5)
        self.start_date = ctk.CTkEntry(filters_frame, width=120)
        self.start_date.grid(row=0, column=1, padx=5)
        self.start_date.insert(0, (datetime.datetime.now() - datetime.timedelta(days=7)).strftime("%Y-%m-%d"))
        
        ctk.CTkLabel(filters_frame, text="To:").grid(row=0, column=2, padx=5)
        self.end_date = ctk.CTkEntry(filters_frame, width=120)
        self.end_date.grid(row=0, column=3, padx=5)
        self.end_date.insert(0, datetime.datetime.now().strftime("%Y-%m-%d"))
        
        # Search button
        search_btn = ctk.CTkButton(
            filters_frame, 
            text="Search", 
            width=80,
            command=self.refresh_data
        )
        search_btn.grid(row=0, column=4, padx=10)
        
        # Sales table
        table_container = ctk.CTkFrame(main_frame, fg_color="transparent")
        table_container.grid(row=2, column=0, sticky="nsew", pady=10)
        table_container.grid_columnconfigure(0, weight=1)
        table_container.grid_rowconfigure(0, weight=1)
        
        # Table headers
        headers = ctk.CTkFrame(table_container, height=40, fg_color="#f5f5f5")
        headers.pack(fill="x")
        
        columns = ["Date", "Product", "Qty", "Price", "Total"]
        col_widths = [0.2, 0.4, 0.1, 0.15, 0.15]
        for i, col in enumerate(columns):
            ctk.CTkLabel(
                headers, 
                text=col,
                font=("Arial", 14, "bold"),
                text_color="#2c3e50"
            ).place(relx=sum(col_widths[:i]) + col_widths[i]/2, rely=0.5, anchor="center")
        
        # Scrollable table
        self.scrollable_frame = ctk.CTkScrollableFrame(
            table_container, 
            height=400,
            fg_color="transparent"
        )
        self.scrollable_frame.pack(fill="both", expand=True)
    
    def refresh_data(self):
        """Refresh sales data"""
        start_date = self.start_date.get()
        end_date = self.end_date.get()
        
        # Clear old data
        for widget in self.scrollable_frame.winfo_children():
            widget.destroy()
        
        # Fetch sales
        self.db.cursor.execute('''
            SELECT s.sale_date, p.name, s.quantity, p.price, s.total_price
            FROM sales s
            JOIN products p ON s.product_id = p.id
            WHERE s.sale_date BETWEEN ? AND ?
            ORDER BY s.sale_date DESC
        ''', (start_date, end_date))
        sales = self.db.cursor.fetchall()
        
        # Display sales
        for sale in sales:
            row = ctk.CTkFrame(self.scrollable_frame, height=40)
            row.pack(fill="x", pady=2)
            
            # Format date
            sale_date = sale[0].split()[0] if isinstance(sale[0], str) else sale[0].strftime("%Y-%m-%d")
            
            # Sale data
            data = [
                sale_date,
                sale[1],
                str(sale[2]),
                f"{sale[3]:.2f} $",
                f"{sale[4]:.2f} $"
            ]
            
            col_widths = [0.2, 0.4, 0.1, 0.15, 0.15]
            
            for i, value in enumerate(data):
                ctk.CTkLabel(
                    row, 
                    text=value,
                    font=("Arial", 13),
                    text_color="#555555"
                ).place(relx=sum(col_widths[:i]) + col_widths[i]/2, rely=0.5, anchor="center")
    
    def update_language(self):
        """Update all text elements when language changes"""
        # إعادة إنشاء الصفحة بالكامل مع الترجمة الجديدة
        for widget in self.winfo_children():
            widget.destroy()
        self.create_widgets()
        self.refresh_data()
