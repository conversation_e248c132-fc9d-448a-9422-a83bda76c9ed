import customtkinter as ctk
from tkinter import messagebox
from database import Database
import tkinter as tk

class InventoryPage(ctk.CTkFrame):
    def __init__(self, parent, app):
        super().__init__(parent, fg_color="transparent")
        self.app = app
        self.db = Database()
        self.selected_product_id = None
        
        # Responsive settings
        self.grid_columnconfigure(0, weight=1)
        self.grid_rowconfigure(0, weight=1)
        
        self.create_widgets()
        self.refresh_data()
    
    def create_widgets(self):
        # Main frame
        main_frame = ctk.CTkFrame(self)
        main_frame.grid(row=0, column=0, sticky="nsew", padx=10, pady=10)
        main_frame.grid_columnconfigure(0, weight=1)
        main_frame.grid_rowconfigure(1, weight=1)
        
        # Title
        title = ctk.CTkLabel(
            main_frame, 
            text="Inventory Management",
            font=("Arial", 24, "bold"),
            text_color="#2c3e50"
        )
        title.grid(row=0, column=0, pady=20, sticky="n")
        
        # Search and controls
        controls_frame = ctk.CTkFrame(main_frame, fg_color="transparent")
        controls_frame.grid(row=1, column=0, sticky="ew", pady=10)
        
        # Search
        ctk.CTkLabel(controls_frame, text="Search:").grid(row=0, column=0, padx=5)
        self.search_entry = ctk.CTkEntry(controls_frame, width=200)
        self.search_entry.grid(row=0, column=1, padx=5)
        self.search_entry.bind("<Return>", lambda e: self.refresh_data())
        
        search_btn = ctk.CTkButton(
            controls_frame, 
            text="Search", 
            width=80,
            command=self.refresh_data
        )
        search_btn.grid(row=0, column=2, padx=5)
        
        # Action buttons
        add_btn = ctk.CTkButton(
            controls_frame, 
            text="Add Product",
            width=120,
            command=self.add_product
        )
        add_btn.grid(row=0, column=3, padx=10)
        
        edit_btn = ctk.CTkButton(
            controls_frame, 
            text="Edit Product",
            width=120,
            command=self.edit_product
        )
        edit_btn.grid(row=0, column=4, padx=5)
        
        delete_btn = ctk.CTkButton(
            controls_frame, 
            text="Delete Product",
            width=120,
            fg_color="#e74c3c",
            hover_color="#c0392b",
            command=self.delete_product
        )
        delete_btn.grid(row=0, column=5, padx=5)
        
        # Products table
        table_container = ctk.CTkFrame(main_frame, fg_color="transparent")
        table_container.grid(row=2, column=0, sticky="nsew", pady=10)
        table_container.grid_columnconfigure(0, weight=1)
        table_container.grid_rowconfigure(0, weight=1)
        
        # Table headers
        headers = ctk.CTkFrame(table_container, height=40, fg_color="#f5f5f5")
        headers.pack(fill="x")
        
        columns = ["ID", "Product", "Price", "Qty", "Category", "Barcode"]
        col_widths = [0.05, 0.3, 0.15, 0.1, 0.2, 0.2]
        for i, col in enumerate(columns):
            ctk.CTkLabel(
                headers, 
                text=col,
                font=("Arial", 14, "bold"),
                text_color="#2c3e50"
            ).place(relx=sum(col_widths[:i]) + col_widths[i]/2, rely=0.5, anchor="center")
        
        # Scrollable table
        self.scrollable_frame = ctk.CTkScrollableFrame(
            table_container, 
            height=300,
            fg_color="transparent"
        )
        self.scrollable_frame.pack(fill="both", expand=True)
    
    def refresh_data(self):
        """Refresh products data"""
        query = self.search_entry.get()
        products = self.db.get_products(query)
        
        # Clear old data
        for widget in self.scrollable_frame.winfo_children():
            widget.destroy()
        
        # Display products
        for product in products:
            row = ctk.CTkFrame(self.scrollable_frame, height=40)
            row.pack(fill="x", pady=2)
            row.product_id = product[0]
            
            # Product data
            data = [
                str(product[0]),
                product[1],
                f"{product[2]:.2f} $",
                str(product[3]),
                product[4] or "N/A",
                product[5] or "N/A"
            ]
            
            col_widths = [0.05, 0.3, 0.15, 0.1, 0.2, 0.2]
            
            for i, value in enumerate(data):
                ctk.CTkLabel(
                    row, 
                    text=value,
                    font=("Arial", 13),
                    text_color="#555555"
                ).place(relx=sum(col_widths[:i]) + col_widths[i]/2, rely=0.5, anchor="center")
            
            # Make row selectable
            row.bind("<Button-1>", lambda e, r=row: self.select_product(r))
    
    def select_product(self, row):
        """Select a product"""
        for widget in self.scrollable_frame.winfo_children():
            if hasattr(widget, "configure"):
                widget.configure(fg_color="transparent")
        row.configure(fg_color="#e3f2fd")
        self.selected_product_id = row.product_id
    
    def add_product(self):
        """Open add product dialog"""
        dialog = ctk.CTkToplevel(self)
        dialog.title("Add New Product")
        dialog.geometry("500x400")
        dialog.transient(self)
        dialog.grab_set()
        
        # Form fields
        fields = [
            ("Name", "entry"),
            ("Price", "entry"),
            ("Quantity", "entry"),
            ("Category", "entry"),
            ("Barcode", "entry"),
            ("Purchase Price", "entry")
        ]
        
        entries = {}
        for i, (label, field_type) in enumerate(fields):
            ctk.CTkLabel(dialog, text=label + ":").grid(row=i, column=0, padx=10, pady=5, sticky="e")
            entry = ctk.CTkEntry(dialog, width=300)
            entry.grid(row=i, column=1, padx=10, pady=5, sticky="w")
            entries[label.lower().replace(" ", "_")] = entry
        
        # Set default quantity
        entries["quantity"].insert(0, "0")
        
        # Save button
        def save_product():
            try:
                name = entries["name"].get()
                price = float(entries["price"].get())
                quantity = int(entries["quantity"].get())
                category = entries["category"].get() or None
                barcode = entries["barcode"].get() or None
                purchase_price = float(entries["purchase_price"].get() or 0)
                
                if not name:
                    raise ValueError("Product name is required")
                
                self.db.add_product(name, price, quantity, category, barcode, purchase_price)
                messagebox.showinfo("Success", "Product added successfully")
                self.refresh_data()
                dialog.destroy()
            except Exception as e:
                messagebox.showerror("Error", f"Failed to add product: {str(e)}")
        
        save_btn = ctk.CTkButton(
            dialog, 
            text="Save Product",
            width=150,
            height=40,
            command=save_product
        )
        save_btn.grid(row=len(fields), column=0, columnspan=2, pady=20)
    
    def edit_product(self):
        """Edit selected product"""
        if not self.selected_product_id:
            messagebox.showwarning("Warning", "Please select a product")
            return
        
        product = self.db.get_product_by_id(self.selected_product_id)
        if not product:
            messagebox.showerror("Error", "Product not found")
            return
        
        dialog = ctk.CTkToplevel(self)
        dialog.title("Edit Product")
        dialog.geometry("500x400")
        dialog.transient(self)
        dialog.grab_set()
        
        # Form fields
        fields = [
            ("Name", "entry", product[1]),
            ("Price", "entry", product[2]),
            ("Quantity", "entry", product[3]),
            ("Category", "entry", product[4]),
            ("Barcode", "entry", product[5]),
            ("Purchase Price", "entry", product[6])
        ]
        
        entries = {}
        for i, (label, field_type, value) in enumerate(fields):
            ctk.CTkLabel(dialog, text=label + ":").grid(row=i, column=0, padx=10, pady=5, sticky="e")
            entry = ctk.CTkEntry(dialog, width=300)
            entry.insert(0, str(value) if value is not None else "")
            entry.grid(row=i, column=1, padx=10, pady=5, sticky="w")
            entries[label.lower().replace(" ", "_")] = entry
        
        # Save button
        def update_product():
            try:
                name = entries["name"].get()
                price = float(entries["price"].get())
                quantity = int(entries["quantity"].get())
                category = entries["category"].get() or None
                barcode = entries["barcode"].get() or None
                purchase_price = float(entries["purchase_price"].get() or 0)
                
                if not name:
                    raise ValueError("Product name is required")
                
                # Update product
                self.db.cursor.execute('''
                    UPDATE products 
                    SET name=?, price=?, quantity=?, category=?, barcode=?, purchase_price=?
                    WHERE id=?
                ''', (name, price, quantity, category, barcode, purchase_price, self.selected_product_id))
                self.db.conn.commit()
                
                messagebox.showinfo("Success", "Product updated successfully")
                self.refresh_data()
                dialog.destroy()
            except Exception as e:
                messagebox.showerror("Error", f"Failed to update product: {str(e)}")
        
        save_btn = ctk.CTkButton(
            dialog, 
            text="Update Product",
            width=150,
            height=40,
            command=update_product
        )
        save_btn.grid(row=len(fields), column=0, columnspan=2, pady=20)
    
    def delete_product(self):
        """Delete selected product"""
        if not self.selected_product_id:
            messagebox.showwarning("Warning", "Please select a product")
            return
        
        if not messagebox.askyesno("Confirm", "Are you sure you want to delete this product?"):
            return
            
        try:
            self.db.cursor.execute("DELETE FROM products WHERE id=?", (self.selected_product_id,))
            self.db.conn.commit()
            messagebox.showinfo("Success", "Product deleted successfully")
            self.refresh_data()
        except Exception as e:
            messagebox.showerror("Error", f"Failed to delete product: {str(e)}")
    
    def update_language(self):
        """Update all text elements when language changes"""
        # Update title
        if hasattr(self, 'title_label'):
            self.title_label.configure(text=self.app.lang.get("inventory_management"))
        
        # Update button texts
        if hasattr(self, 'add_btn'):
            self.add_btn.configure(text=self.app.lang.get("add_product"))
        if hasattr(self, 'edit_btn'):
            self.edit_btn.configure(text=self.app.lang.get("edit_product"))
        if hasattr(self, 'delete_btn'):
            self.delete_btn.configure(text=self.app.lang.get("delete_product"))
        
        # Refresh the entire page to update table headers
        self.refresh_data()
