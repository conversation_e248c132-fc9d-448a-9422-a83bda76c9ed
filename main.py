import os
import sys

# تعيين متغيرات البيئة قبل استيراد أي مكتبات Qt
os.environ['QT_AUTO_SCREEN_SCALE_FACTOR'] = '1'
os.environ['QT_SCALE_FACTOR'] = '1'
os.environ['QT_SCREEN_SCALE_FACTORS'] = '1'
os.environ['QT_DEVICE_PIXEL_RATIO'] = '1'

import customtkinter as ctk
import tkinter as tk
from tkinter import messagebox
from pages.home_page import HomePage
from pages.sales_page import SalesPage
from pages.purchases_page import PurchasesPage
from pages.invoices_page import InvoicesPage
from pages.reports_page import ReportsPage
from pages.settings_page import SettingsPage
from pages.suppliers_page import SuppliersPage
from pages.inventory_page import InventoryPage
from pages.sales_history_page import SalesHistoryPage
from database import Database
import os
import ctypes
import sys
import logging
import traceback
from datetime import datetime
from language import lang

# إنشاء مجلد السجلات إذا لم يكن موجود
if not os.path.exists('logs'):
    os.makedirs('logs')

# إعداد نظام التسجيل
log_file = f"logs/app_{datetime.now().strftime('%Y%m%d_%H%M%S')}.log"
logging.basicConfig(
    filename=log_file,
    level=logging.DEBUG,
    format='%(asctime)s - %(levelname)s - %(message)s'
)

logging.info(f"Operating System: {sys.platform}")
logging.info(f"Python Version: {sys.version}")
logging.info(f"Current Directory: {os.getcwd()}")
logging.info(f"Executable Path: {sys.executable}")
logging.info("Application started")

def log_unhandled_exception(exc_type, exc_value, exc_traceback):
    """Log unhandled exceptions"""
    error_msg = "".join(traceback.format_exception(exc_type, exc_value, exc_traceback))
    logging.error(f"Unhandled exception:\n{error_msg}")
    
    root = tk.Tk()
    root.withdraw()
    messagebox.showerror(
        "Fatal Error", 
        f"An unexpected error occurred:\n\n{str(exc_value)}\n\nSee log file: {log_file}"
    )
    root.destroy()
    sys.exit(1)

sys.excepthook = log_unhandled_exception

# إعدادات المظهر
ctk.set_appearance_mode("light")
ctk.set_default_color_theme("blue")

class SupermarketApp(ctk.CTk):
    def __init__(self):
        super().__init__()
        
        # Initialize database first
        self.db = Database()
        
        # Load settings and apply them
        settings = self.db.get_settings()
        shop_name = settings['shop_name']
        
        self.title(f"{shop_name} - {lang.get('system_title')}")
        self.geometry("1200x700")
        self.lang = lang
        
        # Update language with shop name BEFORE registering callback
        self.lang.update_shop_name(shop_name)
        
        # Register callback AFTER creating widgets
        self.create_widgets()
        
        # Register callback at the end
        self.lang.register_callback(self.update_language)
        
        # Handle window close event
        self.protocol("WM_DELETE_WINDOW", self.on_closing)
    
    def on_closing(self):
        """Handle application closing"""
        try:
            # Save any pending changes
            if hasattr(self, 'db') and self.db:
                self.db.conn.commit()
                self.db.conn.close()
            
            logging.info("Application closed normally")
        except Exception as e:
            logging.error(f"Error during closing: {str(e)}")
        finally:
            self.destroy()
    
    def create_widgets(self):
        # إنشاء الشريط الجانبي
        self.create_sidebar()
        
        # إنشاء منطقة المحتوى الرئيسي
        self.content_frame = ctk.CTkFrame(self, corner_radius=0)
        self.content_frame.pack(side="left", fill="both", expand=True, padx=10, pady=10)
        
        # إنشاء الصفحات
        self.pages = {}
        self.current_page = None
        
        self.create_page("home", HomePage)
        self.create_page("sales", SalesPage)
        self.create_page("purchases", PurchasesPage)
        self.create_page("invoices", InvoicesPage)
        self.create_page("reports", ReportsPage)
        self.create_page("settings", SettingsPage)
        self.create_page("suppliers", SuppliersPage)
        self.create_page("inventory", InventoryPage)
        self.create_page("sales_history", SalesHistoryPage)

        # عرض الصفحة الرئيسية
        self.show_page("home")
    
    def update_language(self):
        """تحديث الواجهة عند تغيير اللغة"""
        # تحديث عنوان النافذة
        settings = self.db.get_settings()
        shop_name = settings['shop_name']
        self.title(f"{shop_name} - {self.lang.get('system_title')}")
        
        # إعادة بناء الشريط الجانبي فقط إذا كان موجود
        if hasattr(self, 'sidebar') and self.sidebar:
            self.sidebar.destroy()
            self.create_sidebar()
        
        # تحديث جميع الصفحات
        for page_name, page in self.pages.items():
            if hasattr(page, 'update_language'):
                try:
                    page.update_language()
                except Exception as e:
                    logging.error(f"Error updating language for page {page_name}: {str(e)}")
        
        # تحديث الصفحة الحالية
        if self.current_page and hasattr(self.pages[self.current_page], 'refresh_data'):
            try:
                self.pages[self.current_page].refresh_data()
            except Exception as e:
                logging.error(f"Error refreshing current page {self.current_page}: {str(e)}")


    def create_sidebar(self):
        """Create the sidebar navigation"""
        self.sidebar = ctk.CTkFrame(self, width=220, corner_radius=0)
        self.sidebar.pack(side="right", fill="y")
        
        # شعار التطبيق
        logo_frame = ctk.CTkFrame(self.sidebar, fg_color="transparent")
        logo_frame.pack(pady=20)
        
        logo_label = ctk.CTkLabel(
            logo_frame, 
            text="🛒 " + self.lang.get("shop_name"),
            font=("Arial", 22, "bold"),
            text_color="#2c3e50"
        )
        logo_label.pack(pady=10)
        
        buttons = [
            ("🏠 " + self.lang.get("dashboard"), "home"),
            ("💰 " + self.lang.get("sales"), "sales"),
            ("📦 " + self.lang.get("purchases"), "purchases"),
            ("🧾 " + self.lang.get("invoices"), "invoices"),
            ("📊 " + self.lang.get("reports"), "reports"),
            ("👥 " + self.lang.get("suppliers"), "suppliers"),
            ("📦 " + self.lang.get("inventory"), "inventory"),
            ("📝 " + self.lang.get("sales_history"), "sales_history"),
            ("⚙️ " + self.lang.get("settings"), "settings"),
        ]
        
        for text, page in buttons:
            btn = ctk.CTkButton(
                self.sidebar, 
                text=text,
                width=200,
                height=45,
                corner_radius=8,
                font=("Arial", 16),
                anchor="right",
                command=lambda p=page: self.show_page(p)
            )
            btn.pack(pady=8, padx=10)
        
        # معلومات المستخدم
        user_frame = ctk.CTkFrame(self.sidebar, fg_color="transparent")
        user_frame.pack(side="bottom", fill="x", pady=20)
        
        lang_btn = ctk.CTkButton(
            self.sidebar,
            text=self.lang.get("change_language"),
            width=200,
            height=45,
            corner_radius=8,
            font=("Arial", 16),
            anchor="right",
            command=self.toggle_language
        )
        lang_btn.pack(pady=8, padx=10)

        ctk.CTkLabel(
            user_frame, 
            text=self.lang.get("user") + ": " + self.lang.get("admin"),
            font=("Arial", 14),
            text_color="#555555"
        ).pack(pady=5)
        
        logout_btn = ctk.CTkButton(
            user_frame, 
            text=self.lang.get("logout"),
            width=180,
            height=35,
            corner_radius=8,
            font=("Arial", 14),
            fg_color="#e74c3c",
            hover_color="#c0392b",
            command=self.logout
        )
        logout_btn.pack(pady=10)
    
    def toggle_language(self):
        """Toggle between languages"""
        self.lang.toggle()
    
    def logout(self):
        """Handle logout process"""
        if messagebox.askyesno("Logout", "Are you sure you want to logout?"):
            self.destroy()
            sys.exit(0)
    
    def create_page(self, name, page_class):
        """Create a page in the content area"""
        try:
            self.pages[name] = page_class(self.content_frame, self)
            self.pages[name].pack(fill="both", expand=True, padx=10, pady=10)
            self.pages[name].pack_forget()
        except Exception as e:
            logging.error(f"Error creating page {name}: {str(e)}")
            messagebox.showerror("Error", f"Error creating page {name}:\n{str(e)}")
    
    def show_page(self, name):
        """Show the specified page"""
        try:
            if self.current_page:
                self.pages[self.current_page].pack_forget()
            
            self.pages[name].pack(fill="both", expand=True, padx=10, pady=10)
            self.current_page = name
            
            # Refresh data when opening the page
            if hasattr(self.pages[name], 'refresh_data'):
                try:
                    self.pages[name].refresh_data()
                except Exception as e:
                    logging.error(f"Error refreshing page {name}: {str(e)}")
        except Exception as e:
            logging.error(f"Error showing page {name}: {str(e)}")
            messagebox.showerror("Error", f"Error showing page {name}:\n{str(e)}")
    
    def run(self):
        """Run the application"""
        self.mainloop()

if __name__ == "__main__":
    try:
        app = SupermarketApp()
        app.run()
    except Exception as e:
        error_msg = "".join(traceback.format_exception(type(e), e, e.__traceback__))
        logging.error(f"Unexpected error:\n{error_msg}")
        
        root = tk.Tk()
        root.withdraw()
        messagebox.showerror(
            "Fatal Error", 
            f"An unexpected error occurred:\n\n{str(e)}\n\nSee log file: {log_file}"
        )
        root.destroy()
        sys.exit(1)
