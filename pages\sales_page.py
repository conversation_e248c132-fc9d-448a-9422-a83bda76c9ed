import customtkinter as ctk
import tkinter as tk
from tkinter import messagebox
from database import Database
from pdf_utils import PDFGenerator
from datetime import datetime
import os 
import subprocess
import sys

class SalesPage(ctk.CTkFrame):
    def __init__(self, parent, app):
        super().__init__(parent, fg_color="transparent")
        self.app = app
        self.db = Database()
        self.cart = []
        self.selected_product_id = None
        
        # Responsive settings
        self.grid_columnconfigure(0, weight=1)
        self.grid_rowconfigure(0, weight=1)
        
        self.create_widgets()
    
    def create_widgets(self):
        # Main container
        main_frame = ctk.CTkFrame(self, corner_radius=15)
        main_frame.pack(fill="both", expand=True, padx=20, pady=20)
        main_frame.grid_columnconfigure(0, weight=1)
        main_frame.grid_rowconfigure(2, weight=1)
        
        # Title
        title_label = ctk.CTkLabel(
            main_frame, 
            text=self.app.lang.get("point_of_sale"),
            font=("Arial", 24, "bold"),
            text_color="#2c3e50"
        )
        title_label.grid(row=0, column=0, pady=20)
        
        # Product search section
        search_frame = ctk.CTkFrame(main_frame, corner_radius=12)
        search_frame.grid(row=1, column=0, sticky="ew", pady=10, padx=20)
        search_frame.grid_columnconfigure(1, weight=1)
        
        ctk.CTkLabel(
            search_frame, 
            text=self.app.lang.get("product_search"),
            font=("Arial", 16, "bold")
        ).grid(row=0, column=0, padx=10, pady=10, sticky="w")
        
        self.search_entry = ctk.CTkEntry(
            search_frame, 
            placeholder_text=self.app.lang.get("search"),
            width=300
        )
        self.search_entry.grid(row=0, column=1, padx=10, pady=10, sticky="ew")
        self.search_entry.bind("<KeyRelease>", lambda e: self.search_products())
        
        search_btn = ctk.CTkButton(
            search_frame, 
            text=self.app.lang.get("search"),
            width=100,
            command=self.search_products
        )
        search_btn.grid(row=0, column=2, padx=10, pady=10)
        
        # Search results
        self.search_results_frame = ctk.CTkScrollableFrame(
            search_frame, 
            height=150,
            fg_color="transparent"
        )
        self.search_results_frame.grid(row=1, column=0, columnspan=3, sticky="ew", padx=10, pady=5)
        
        # Cart section
        cart_frame = ctk.CTkFrame(main_frame, corner_radius=12)
        cart_frame.grid(row=2, column=0, sticky="nsew", pady=10, padx=20)
        cart_frame.grid_columnconfigure(0, weight=1)
        cart_frame.grid_rowconfigure(1, weight=1)
        
        ctk.CTkLabel(
            cart_frame, 
            text=self.app.lang.get("shopping_cart"),
            font=("Arial", 16, "bold")
        ).grid(row=0, column=0, pady=10)
        
        # Cart table
        self.cart_table = ctk.CTkScrollableFrame(
            cart_frame, 
            height=200,
            fg_color="transparent"
        )
        self.cart_table.grid(row=1, column=0, sticky="nsew", padx=10, pady=5)
        
        # Cart controls
        cart_controls = ctk.CTkFrame(cart_frame, fg_color="transparent")
        cart_controls.grid(row=2, column=0, sticky="ew", pady=10, padx=10)
        
        ctk.CTkLabel(cart_controls, text=self.app.lang.get("quantity")).grid(row=0, column=0, padx=5)
        self.quantity_entry = ctk.CTkEntry(cart_controls, width=60)
        self.quantity_entry.grid(row=0, column=1, padx=5)
        self.quantity_entry.insert(0, "1")
        
        add_btn = ctk.CTkButton(
            cart_controls, 
            text=self.app.lang.get("add_to_cart"), 
            width=120,
            command=self.add_to_cart
        )
        add_btn.grid(row=0, column=2, padx=10)
        
        remove_btn = ctk.CTkButton(
            cart_controls, 
            text=self.app.lang.get("remove_from_cart"), 
            width=120,
            fg_color="#e74c3c",
            hover_color="#c0392b",
            command=self.remove_from_cart
        )
        remove_btn.grid(row=0, column=3, padx=5)
        
        # Total section
        total_frame = ctk.CTkFrame(cart_frame, fg_color="transparent")
        total_frame.grid(row=3, column=0, sticky="ew", pady=10, padx=10)
        
        ctk.CTkLabel(
            total_frame, 
            text=self.app.lang.get("invoice_total"),
            font=("Arial", 16, "bold")
        ).grid(row=0, column=0, padx=5)
        
        self.total_label = ctk.CTkLabel(
            total_frame, 
            text="0.00 $",
            font=("Arial", 18, "bold"),
            text_color="#27ae60"
        )
        self.total_label.grid(row=0, column=1, padx=5)
        
        # Customer info section
        customer_frame = ctk.CTkFrame(main_frame, corner_radius=12)
        customer_frame.grid(row=3, column=0, sticky="ew", pady=10, padx=20)
        customer_frame.grid_columnconfigure(1, weight=1)
        
        ctk.CTkLabel(
            customer_frame, 
            text=self.app.lang.get("customer_name"),
            font=("Arial", 14, "bold")
        ).grid(row=0, column=0, padx=10, pady=10, sticky="w")
        
        self.customer_name = ctk.CTkEntry(customer_frame, width=200)
        self.customer_name.grid(row=0, column=1, padx=10, pady=10, sticky="ew")
        
        # Action buttons
        btn_frame = ctk.CTkFrame(main_frame, fg_color="transparent")
        btn_frame.grid(row=4, column=0, sticky="ew", pady=10, padx=20)
        btn_frame.grid_columnconfigure(0, weight=1)
        btn_frame.grid_columnconfigure(1, weight=1)
        btn_frame.grid_columnconfigure(2, weight=1)
        
        clear_btn = ctk.CTkButton(
            btn_frame, 
            text=self.app.lang.get("clear_cart"), 
            width=150,
            height=40,
            corner_radius=8,
            font=("Arial", 14, "bold"),
            fg_color="#e67e22",
            hover_color="#d35400",
            command=self.clear_cart
        )
        clear_btn.grid(row=0, column=0, padx=5)
        
        print_btn = ctk.CTkButton(
            btn_frame, 
            text=self.app.lang.get("print_invoice"), 
            width=150,
            height=40,
            corner_radius=8,
            font=("Arial", 14, "bold"),
            command=self.print_invoice
        )
        print_btn.grid(row=0, column=1, padx=5)
        
        save_btn = ctk.CTkButton(
            btn_frame, 
            text=self.app.lang.get("complete_sale"), 
            width=150,
            height=40,
            corner_radius=8,
            font=("Arial", 14, "bold"),
            fg_color="#27ae60",
            hover_color="#219653",
            command=self.save_invoice
        )
        save_btn.grid(row=0, column=2, padx=5)
        
        # Load initial products
        self.search_products()
    
    def search_products(self):
        """Search for products"""
        query = self.search_entry.get()
        products = self.db.get_products_by_name_or_barcode(query)
        
        # Clear previous results
        for widget in self.search_results_frame.winfo_children():
            widget.destroy()
        
        # Display results
        for product in products:
            row = ctk.CTkFrame(self.search_results_frame, height=40)
            row.pack(fill="x", pady=2)
            
            # Store product ID
            row.product_id = product[0]
            
            # Make row selectable
            row.bind("<Button-1>", lambda e, r=row: self.select_product(r))
            
            # Product details
            values = (product[1], f"{product[2]:.2f} $", str(product[3]))
            for i, value in enumerate(values):
                ctk.CTkLabel(
                    row, 
                    text=value,
                    font=("Arial", 13),
                    text_color="#555555"
                ).place(relx=0.2 + i*0.3, rely=0.5, anchor="center")
            
            # Add to cart button
            add_btn = ctk.CTkButton(
                row, 
                text="+",
                width=30,
                height=30,
                corner_radius=15,
                fg_color="#27ae60",
                hover_color="#219653",
                command=lambda r=row: self.add_product_to_cart(r.product_id)
            )
            add_btn.place(relx=0.95, rely=0.5, anchor="center")
    
    def select_product(self, row):
        """Select a product"""
        for widget in self.search_results_frame.winfo_children():
            widget.configure(fg_color="transparent")
        row.configure(fg_color="#e3f2fd")
        self.selected_product_id = row.product_id
    
    def add_product_to_cart(self, product_id):
        """Add product to cart"""
        try:
            quantity = int(self.quantity_entry.get())
            if quantity <= 0:
                raise ValueError
        except:
            messagebox.showerror("Error", "Quantity must be a positive integer")
            return
        
        product = self.db.get_product_by_id(product_id)
        if not product:
            return
        
        # Check stock
        if quantity > product[3]:
            messagebox.showerror("Error", f"Requested quantity ({quantity}) not available. Available: {product[3]}")
            return
        
        # Check if product already in cart
        for item in self.cart:
            if item["id"] == product_id:
                item["quantity"] += quantity
                item["total"] = item["price"] * item["quantity"]
                self.update_cart_table()
                return
        
        # Add new item to cart
        self.cart.append({
            "id": product_id,
            "name": product[1],
            "price": product[2],
            "quantity": quantity,
            "total": product[2] * quantity
        })
        
        self.update_cart_table()
    
    def add_to_cart(self):
        """Add selected product to cart"""
        if not self.selected_product_id:
            messagebox.showwarning("Warning", "Please select a product")
            return
        
        self.add_product_to_cart(self.selected_product_id)
    
    def update_cart_table(self):
        """Update cart display"""
        for widget in self.cart_table.winfo_children():
            widget.destroy()
        
        total_invoice = 0
        for item in self.cart:
            row = ctk.CTkFrame(self.cart_table, height=40)
            row.pack(fill="x", pady=2)
            
            # Store item ID
            row.item_id = item['id']
            
            # Display item details
            values = (item['name'], item['quantity'], f"{item['price']:.2f} $", f"{item['total']:.2f} $")
            for i, value in enumerate(values):
                ctk.CTkLabel(
                    row, 
                    text=value,
                    font=("Arial", 13),
                    text_color="#555555"
                ).place(relx=0.15 + i*0.25, rely=0.5, anchor="center")
            
            # Remove button
            remove_btn = ctk.CTkButton(
                row, 
                text="x",
                width=30,
                height=30,
                corner_radius=15,
                fg_color="#e74c3c",
                hover_color="#c0392b",
                command=lambda r=row: self.remove_item_from_cart(r.item_id)
            )
            remove_btn.place(relx=0.95, rely=0.5, anchor="center")
            
            total_invoice += item['total']
        
        self.total_label.configure(text=f"{total_invoice:.2f} $")
    
    def remove_item_from_cart(self, product_id):
        """Remove item from cart"""
        for i, item in enumerate(self.cart):
            if item['id'] == product_id:
                del self.cart[i]
                break
        
        self.update_cart_table()
    
    def remove_from_cart(self):
        """Remove selected item from cart"""
        if not self.cart:
            return
        
        if self.selected_product_id:
            self.remove_item_from_cart(self.selected_product_id)
    
    def clear_cart(self):
        """Clear the cart"""
        self.cart = []
        self.update_cart_table()
        messagebox.showinfo("Success", "Cart cleared")
    
    def save_invoice(self):
        """Save the invoice"""
        if not self.cart:
            messagebox.showwarning("Warning", "Cart is empty")
            return
        
        # Create invoices directory
        try:
            os.makedirs("invoices", exist_ok=True)
        except Exception as e:
            messagebox.showerror("Error", f"Failed to create invoices directory: {str(e)}")
            return
        
        # Calculate total
        total = sum(item['total'] for item in self.cart)
        
        # Payment method
        payment_method = self.payment_var.get()
        method_name = {
            "cash": "Cash",
            "card": "Card",
            "wallet": "Wallet"
        }.get(payment_method, "Cash")
        
        # Customer name
        customer_name = self.customer_name.get() or "Cash Customer"
        
        try:
            # Record sales
            for item in self.cart:
                self.db.record_sale(item['id'], item['quantity'])
            
            # Create invoice if requested
            if self.invoice_option.get():
                invoice_id = self.db.create_sale_invoice(total, f"Sale invoice for {customer_name}")
                if invoice_id:
                    messagebox.showinfo("Success", f"Invoice #{invoice_id} saved successfully")
                else:
                    messagebox.showerror("Error", "Failed to create invoice")
            else:
                messagebox.showinfo("Success", "Sale completed without invoice")
            
            # Reset form
            self.clear_cart()
            self.customer_name.delete(0, tk.END)
        except Exception as e:
            messagebox.showerror("Error", f"Failed to save invoice: {str(e)}")
    
    def print_invoice(self):
        """Print the invoice"""
        if not self.cart:
            messagebox.showwarning("Warning", "Cart is empty")
            return
    
        # Create invoices directory
        try:
            os.makedirs("invoices", exist_ok=True)
        except Exception as e:
            messagebox.showerror("Error", f"Failed to create invoices directory: {str(e)}")
            return
    
        # Calculate total
        total = sum(item['total'] for item in self.cart)

        # Payment method
        payment_method = self.payment_var.get()
        method_name = {
            "cash": "Cash",
            "card": "Card",
            "wallet": "Wallet"
        }.get(payment_method, "Cash")
    
        # Customer name
        customer_name = self.customer_name.get() or "Cash Customer"
    
        try:
            # Create PDF invoice
            invoice_id = datetime.now().strftime("%Y%m%d%H%M%S")
            filename = PDFGenerator.create_sale_invoice(
                invoice_id,
                datetime.now().strftime("%Y-%m-%d %H:%M:%S"),
                customer_name,
                self.cart,
                total,
                method_name,
                self.db
            )
        
            if os.path.exists(filename):
                # Open the invoice automatically
                if os.name == 'nt':  # Windows
                    os.startfile(filename)
                elif os.name == 'posix':  # macOS, Linux
                    if sys.platform == 'darwin':
                        subprocess.run(['open', filename])
                    else:
                        subprocess.run(['xdg-open', filename])
                else:
                    messagebox.showinfo("Open Invoice", f"Invoice saved at: {filename}")
                
                messagebox.showinfo("Success", "Invoice created and opened")
            else:
                messagebox.showerror("Error", f"Failed to create invoice file: {filename}")
        except Exception as e:
            messagebox.showerror("Error", f"Failed to create invoice: {str(e)}")            
    
    def refresh_data(self):
        """Refresh data"""
        self.clear_cart()
        self.search_products()
    
    def update_language(self):
        """Update all text elements when language changes"""
        # Recreate the entire page with new translations
        for widget in self.winfo_children():
            widget.destroy()
        self.create_widgets()
