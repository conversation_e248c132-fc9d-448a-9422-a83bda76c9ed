import PyInstaller.__main__
import os
import shutil
import sys

def get_app_path():
    """Get the application path"""
    if getattr(sys, 'frozen', False):
        return os.path.dirname(sys.executable)
    else:
        return os.path.dirname(os.path.abspath(__file__))

# Build settings
build_options = [
    'main.py',
    '--onefile',
    '--windowed',
    '--name=SupermarketSystem',
    '--icon=supermarket.ico',
    '--add-data=amiri-regular.ttf;.',
    '--add-data=supermarket.db;.',
    '--add-data=invoices;invoices',
    '--add-data=qt.conf;.',
    '--hidden-import=arabic_reshaper',
    '--hidden-import=bidi.algorithm',
    '--hidden-import=reportlab',
    '--hidden-import=matplotlib',
    '--hidden-import=PIL',
    '--hidden-import=PIL._tkinter_finder',
    '--collect-data=customtkinter',
    '--collect-data=tkinter',
    '--exclude-module=PyQt5',
    '--exclude-module=PyQt6',
    '--exclude-module=PySide2',
    '--exclude-module=PySide6'
]

print("Starting build process...")

# Build the application
PyInstaller.__main__.run(build_options)

# Copy necessary files
app_path = get_app_path()
dist_path = os.path.join(app_path, 'dist')

print("Copying additional files...")

# Create invoices directory
invoices_path = os.path.join(dist_path, 'invoices')
if not os.path.exists(invoices_path):
    os.makedirs(invoices_path)

# Copy additional files
files_to_copy = ['amiri-regular.ttf', 'supermarket.db', 'qt.conf']
for file in files_to_copy:
    src = os.path.join(app_path, file)
    dst = os.path.join(dist_path, file)
    if os.path.exists(src) and not os.path.exists(dst):
        shutil.copy(src, dst)
        print(f"Copied {file}")

print("Build completed successfully! Run SupermarketSystem.exe")
