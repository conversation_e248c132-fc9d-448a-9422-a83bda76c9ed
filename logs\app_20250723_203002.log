2025-07-23 20:30:02,119 - INFO - Operating System: win32
2025-07-23 20:30:02,119 - INFO - Python Version: 3.12.8 (tags/v3.12.8:2dc476b, Dec  3 2024, 19:30:04) [MSC v.1942 64 bit (AMD64)]
2025-07-23 20:30:02,119 - INFO - Current Directory: C:\Users\<USER>\Desktop\SupermarketSystem
2025-07-23 20:30:02,119 - INFO - Executable Path: C:\Users\<USER>\AppData\Local\Programs\Python\Python312\python.exe
2025-07-23 20:30:02,119 - INFO - Application started
2025-07-23 20:30:14,299 - DEBUG - Database path: c:\Users\<USER>\Desktop\SupermarketSystem\supermarket.db
2025-07-23 20:30:15,041 - DEBUG - Loaded backend qtagg version 6.9.0.
2025-07-23 20:30:15,676 - DEBUG - findfont: Matching sans\-serif:style=normal:variant=normal:weight=normal:stretch=normal:size=10.0.
2025-07-23 20:30:15,691 - DEBUG - findfont: score(FontEntry(fname='C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\site-packages\\matplotlib\\mpl-data\\fonts\\ttf\\cmss10.ttf', name='cmss10', style='normal', variant='normal', weight=400, stretch='normal', size='scalable')) = 10.05
2025-07-23 20:30:15,691 - DEBUG - findfont: score(FontEntry(fname='C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\site-packages\\matplotlib\\mpl-data\\fonts\\ttf\\DejaVuSansMono.ttf', name='DejaVu Sans Mono', style='normal', variant='normal', weight=400, stretch='normal', size='scalable')) = 10.05
2025-07-23 20:30:15,691 - DEBUG - findfont: score(FontEntry(fname='C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\site-packages\\matplotlib\\mpl-data\\fonts\\ttf\\STIXGeneral.ttf', name='STIXGeneral', style='normal', variant='normal', weight=400, stretch='normal', size='scalable')) = 10.05
2025-07-23 20:30:15,691 - DEBUG - findfont: score(FontEntry(fname='C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\site-packages\\matplotlib\\mpl-data\\fonts\\ttf\\STIXGeneralItalic.ttf', name='STIXGeneral', style='italic', variant='normal', weight=400, stretch='normal', size='scalable')) = 11.05
2025-07-23 20:30:15,691 - DEBUG - findfont: score(FontEntry(fname='C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\site-packages\\matplotlib\\mpl-data\\fonts\\ttf\\STIXNonUniBol.ttf', name='STIXNonUnicode', style='normal', variant='normal', weight=700, stretch='normal', size='scalable')) = 10.335
2025-07-23 20:30:15,691 - DEBUG - findfont: score(FontEntry(fname='C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\site-packages\\matplotlib\\mpl-data\\fonts\\ttf\\cmsy10.ttf', name='cmsy10', style='normal', variant='normal', weight=400, stretch='normal', size='scalable')) = 10.05
2025-07-23 20:30:15,691 - DEBUG - findfont: score(FontEntry(fname='C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\site-packages\\matplotlib\\mpl-data\\fonts\\ttf\\DejaVuSansMono-Oblique.ttf', name='DejaVu Sans Mono', style='oblique', variant='normal', weight=400, stretch='normal', size='scalable')) = 11.05
2025-07-23 20:30:15,691 - DEBUG - findfont: score(FontEntry(fname='C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\site-packages\\matplotlib\\mpl-data\\fonts\\ttf\\DejaVuSansMono-Bold.ttf', name='DejaVu Sans Mono', style='normal', variant='normal', weight=700, stretch='normal', size='scalable')) = 10.335
2025-07-23 20:30:15,691 - DEBUG - findfont: score(FontEntry(fname='C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\site-packages\\matplotlib\\mpl-data\\fonts\\ttf\\DejaVuSerif-Italic.ttf', name='DejaVu Serif', style='italic', variant='normal', weight=400, stretch='normal', size='scalable')) = 11.05
2025-07-23 20:30:15,691 - DEBUG - findfont: score(FontEntry(fname='C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\site-packages\\matplotlib\\mpl-data\\fonts\\ttf\\cmtt10.ttf', name='cmtt10', style='normal', variant='normal', weight=400, stretch='normal', size='scalable')) = 10.05
2025-07-23 20:30:15,691 - DEBUG - findfont: score(FontEntry(fname='C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\site-packages\\matplotlib\\mpl-data\\fonts\\ttf\\STIXSizThreeSymReg.ttf', name='STIXSizeThreeSym', style='normal', variant='normal', weight=400, stretch='normal', size='scalable')) = 10.05
2025-07-23 20:30:15,691 - DEBUG - findfont: score(FontEntry(fname='C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\site-packages\\matplotlib\\mpl-data\\fonts\\ttf\\cmex10.ttf', name='cmex10', style='normal', variant='normal', weight=400, stretch='normal', size='scalable')) = 10.05
2025-07-23 20:30:15,691 - DEBUG - findfont: score(FontEntry(fname='C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\site-packages\\matplotlib\\mpl-data\\fonts\\ttf\\STIXSizFourSymBol.ttf', name='STIXSizeFourSym', style='normal', variant='normal', weight=700, stretch='normal', size='scalable')) = 10.335
2025-07-23 20:30:15,691 - DEBUG - findfont: score(FontEntry(fname='C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\site-packages\\matplotlib\\mpl-data\\fonts\\ttf\\STIXSizFourSymReg.ttf', name='STIXSizeFourSym', style='normal', variant='normal', weight=400, stretch='normal', size='scalable')) = 10.05
2025-07-23 20:30:15,691 - DEBUG - findfont: score(FontEntry(fname='C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\site-packages\\matplotlib\\mpl-data\\fonts\\ttf\\STIXSizTwoSymBol.ttf', name='STIXSizeTwoSym', style='normal', variant='normal', weight=700, stretch='normal', size='scalable')) = 10.335
2025-07-23 20:30:15,691 - DEBUG - findfont: score(FontEntry(fname='C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\site-packages\\matplotlib\\mpl-data\\fonts\\ttf\\STIXSizTwoSymReg.ttf', name='STIXSizeTwoSym', style='normal', variant='normal', weight=400, stretch='normal', size='scalable')) = 10.05
2025-07-23 20:30:15,691 - DEBUG - findfont: score(FontEntry(fname='C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\site-packages\\matplotlib\\mpl-data\\fonts\\ttf\\STIXSizFiveSymReg.ttf', name='STIXSizeFiveSym', style='normal', variant='normal', weight=400, stretch='normal', size='scalable')) = 10.05
2025-07-23 20:30:15,691 - DEBUG - findfont: score(FontEntry(fname='C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\site-packages\\matplotlib\\mpl-data\\fonts\\ttf\\cmmi10.ttf', name='cmmi10', style='normal', variant='normal', weight=400, stretch='normal', size='scalable')) = 10.05
2025-07-23 20:30:15,691 - DEBUG - findfont: score(FontEntry(fname='C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\site-packages\\matplotlib\\mpl-data\\fonts\\ttf\\STIXSizOneSymReg.ttf', name='STIXSizeOneSym', style='normal', variant='normal', weight=400, stretch='normal', size='scalable')) = 10.05
2025-07-23 20:30:15,691 - DEBUG - findfont: score(FontEntry(fname='C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\site-packages\\matplotlib\\mpl-data\\fonts\\ttf\\DejaVuSansMono-BoldOblique.ttf', name='DejaVu Sans Mono', style='oblique', variant='normal', weight=700, stretch='normal', size='scalable')) = 11.335
2025-07-23 20:30:15,691 - DEBUG - findfont: score(FontEntry(fname='C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\site-packages\\matplotlib\\mpl-data\\fonts\\ttf\\STIXNonUni.ttf', name='STIXNonUnicode', style='normal', variant='normal', weight=400, stretch='normal', size='scalable')) = 10.05
2025-07-23 20:30:15,691 - DEBUG - findfont: score(FontEntry(fname='C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\site-packages\\matplotlib\\mpl-data\\fonts\\ttf\\DejaVuSerif-BoldItalic.ttf', name='DejaVu Serif', style='italic', variant='normal', weight=700, stretch='normal', size='scalable')) = 11.335
2025-07-23 20:30:15,691 - DEBUG - findfont: score(FontEntry(fname='C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\site-packages\\matplotlib\\mpl-data\\fonts\\ttf\\STIXGeneralBol.ttf', name='STIXGeneral', style='normal', variant='normal', weight=700, stretch='normal', size='scalable')) = 10.335
2025-07-23 20:30:15,691 - DEBUG - findfont: score(FontEntry(fname='C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\site-packages\\matplotlib\\mpl-data\\fonts\\ttf\\DejaVuSerif.ttf', name='DejaVu Serif', style='normal', variant='normal', weight=400, stretch='normal', size='scalable')) = 10.05
2025-07-23 20:30:15,691 - DEBUG - findfont: score(FontEntry(fname='C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\site-packages\\matplotlib\\mpl-data\\fonts\\ttf\\STIXNonUniBolIta.ttf', name='STIXNonUnicode', style='italic', variant='normal', weight=700, stretch='normal', size='scalable')) = 11.335
2025-07-23 20:30:15,691 - DEBUG - findfont: score(FontEntry(fname='C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\site-packages\\matplotlib\\mpl-data\\fonts\\ttf\\STIXSizOneSymBol.ttf', name='STIXSizeOneSym', style='normal', variant='normal', weight=700, stretch='normal', size='scalable')) = 10.335
2025-07-23 20:30:15,691 - DEBUG - findfont: score(FontEntry(fname='C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\site-packages\\matplotlib\\mpl-data\\fonts\\ttf\\DejaVuSerif-Bold.ttf', name='DejaVu Serif', style='normal', variant='normal', weight=700, stretch='normal', size='scalable')) = 10.335
2025-07-23 20:30:15,691 - DEBUG - findfont: score(FontEntry(fname='C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\site-packages\\matplotlib\\mpl-data\\fonts\\ttf\\DejaVuSans-Oblique.ttf', name='DejaVu Sans', style='oblique', variant='normal', weight=400, stretch='normal', size='scalable')) = 1.05
2025-07-23 20:30:15,691 - DEBUG - findfont: score(FontEntry(fname='C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\site-packages\\matplotlib\\mpl-data\\fonts\\ttf\\DejaVuSans-Bold.ttf', name='DejaVu Sans', style='normal', variant='normal', weight=700, stretch='normal', size='scalable')) = 0.33499999999999996
2025-07-23 20:30:15,691 - DEBUG - findfont: score(FontEntry(fname='C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\site-packages\\matplotlib\\mpl-data\\fonts\\ttf\\cmr10.ttf', name='cmr10', style='normal', variant='normal', weight=400, stretch='normal', size='scalable')) = 10.05
2025-07-23 20:30:15,691 - DEBUG - findfont: score(FontEntry(fname='C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\site-packages\\matplotlib\\mpl-data\\fonts\\ttf\\cmb10.ttf', name='cmb10', style='normal', variant='normal', weight=400, stretch='normal', size='scalable')) = 10.05
2025-07-23 20:30:15,691 - DEBUG - findfont: score(FontEntry(fname='C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\site-packages\\matplotlib\\mpl-data\\fonts\\ttf\\DejaVuSansDisplay.ttf', name='DejaVu Sans Display', style='normal', variant='normal', weight=400, stretch='normal', size='scalable')) = 10.05
2025-07-23 20:30:15,691 - DEBUG - findfont: score(FontEntry(fname='C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\site-packages\\matplotlib\\mpl-data\\fonts\\ttf\\DejaVuSans.ttf', name='DejaVu Sans', style='normal', variant='normal', weight=400, stretch='normal', size='scalable')) = 0.05
2025-07-23 20:30:15,691 - DEBUG - findfont: score(FontEntry(fname='C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\site-packages\\matplotlib\\mpl-data\\fonts\\ttf\\STIXSizThreeSymBol.ttf', name='STIXSizeThreeSym', style='normal', variant='normal', weight=700, stretch='normal', size='scalable')) = 10.335
2025-07-23 20:30:15,691 - DEBUG - findfont: score(FontEntry(fname='C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\site-packages\\matplotlib\\mpl-data\\fonts\\ttf\\STIXGeneralBolIta.ttf', name='STIXGeneral', style='italic', variant='normal', weight=700, stretch='normal', size='scalable')) = 11.335
2025-07-23 20:30:15,691 - DEBUG - findfont: score(FontEntry(fname='C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\site-packages\\matplotlib\\mpl-data\\fonts\\ttf\\DejaVuSerifDisplay.ttf', name='DejaVu Serif Display', style='normal', variant='normal', weight=400, stretch='normal', size='scalable')) = 10.05
2025-07-23 20:30:15,691 - DEBUG - findfont: score(FontEntry(fname='C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\site-packages\\matplotlib\\mpl-data\\fonts\\ttf\\DejaVuSans-BoldOblique.ttf', name='DejaVu Sans', style='oblique', variant='normal', weight=700, stretch='normal', size='scalable')) = 1.335
2025-07-23 20:30:15,691 - DEBUG - findfont: score(FontEntry(fname='C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\site-packages\\matplotlib\\mpl-data\\fonts\\ttf\\STIXNonUniIta.ttf', name='STIXNonUnicode', style='italic', variant='normal', weight=400, stretch='normal', size='scalable')) = 11.05
2025-07-23 20:30:15,691 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\VerdanaPro-Regular.ttf', name='Verdana Pro', style='normal', variant='normal', weight=400, stretch='normal', size='scalable')) = 10.05
2025-07-23 20:30:15,691 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\ArialNova.ttf', name='Arial Nova', style='normal', variant='normal', weight=400, stretch='normal', size='scalable')) = 10.05
2025-07-23 20:30:15,691 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\dokchamp.ttf', name='DokChampa', style='normal', variant='normal', weight=400, stretch='normal', size='scalable')) = 10.05
2025-07-23 20:30:15,691 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\ROCKI.TTF', name='Rockwell', style='italic', variant='normal', weight=400, stretch='normal', size='scalable')) = 11.05
2025-07-23 20:30:15,691 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\upcebi.ttf', name='EucrosiaUPC', style='italic', variant='normal', weight=700, stretch='normal', size='scalable')) = 11.335
2025-07-23 20:30:15,691 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\LFAXDI.TTF', name='Lucida Fax', style='italic', variant='normal', weight=600, stretch='normal', size='scalable')) = 11.24
2025-07-23 20:30:15,691 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\BOD_I.TTF', name='Bodoni MT', style='italic', variant='normal', weight=400, stretch='normal', size='scalable')) = 11.05
2025-07-23 20:30:15,691 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\comicz.ttf', name='Comic Sans MS', style='italic', variant='normal', weight=700, stretch='normal', size='scalable')) = 11.335
2025-07-23 20:30:15,691 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\VerdanaPro-CondRegular.ttf', name='Verdana Pro', style='normal', variant='normal', weight=400, stretch='condensed', size='scalable')) = 10.25
2025-07-23 20:30:15,691 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\GillSansLightItNova.ttf', name='Gill Sans Nova', style='italic', variant='normal', weight=300, stretch='normal', size='scalable')) = 11.145
2025-07-23 20:30:15,691 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\NotoSansTC-VF.ttf', name='Noto Sans TC', style='normal', variant='normal', weight=100, stretch='normal', size='scalable')) = 10.335
2025-07-23 20:30:15,691 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\upcll.ttf', name='LilyUPC', style='normal', variant='normal', weight=400, stretch='normal', size='scalable')) = 10.05
2025-07-23 20:30:15,691 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\REFSAN.TTF', name='MS Reference Sans Serif', style='normal', variant='normal', weight=400, stretch='normal', size='scalable')) = 10.05
2025-07-23 20:30:15,691 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\consolai.ttf', name='Consolas', style='italic', variant='normal', weight=400, stretch='normal', size='scalable')) = 11.05
2025-07-23 20:30:15,691 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\verdana.ttf', name='Verdana', style='normal', variant='normal', weight=400, stretch='normal', size='scalable')) = 3.6863636363636365
2025-07-23 20:30:15,691 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\SCHLBKB.TTF', name='Century Schoolbook', style='normal', variant='normal', weight=700, stretch='normal', size='scalable')) = 10.335
2025-07-23 20:30:15,691 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\SNAP____.TTF', name='Snap ITC', style='normal', variant='normal', weight=400, stretch='normal', size='scalable')) = 10.05
2025-07-23 20:30:15,691 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\upckbi.ttf', name='KodchiangUPC', style='italic', variant='normal', weight=700, stretch='normal', size='scalable')) = 11.335
2025-07-23 20:30:15,691 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\latha.ttf', name='Latha', style='normal', variant='normal', weight=400, stretch='normal', size='scalable')) = 10.05
2025-07-23 20:30:15,691 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\monbaiti.ttf', name='Mongolian Baiti', style='normal', variant='normal', weight=400, stretch='normal', size='scalable')) = 10.05
2025-07-23 20:30:15,691 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\JOKERMAN.TTF', name='Jokerman', style='normal', variant='normal', weight=400, stretch='normal', size='scalable')) = 10.05
2025-07-23 20:30:15,691 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\mangalb.ttf', name='Mangal', style='normal', variant='normal', weight=700, stretch='normal', size='scalable')) = 10.335
2025-07-23 20:30:15,691 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\GeorgiaPro-CondItalic.ttf', name='Georgia Pro', style='italic', variant='normal', weight=400, stretch='condensed', size='scalable')) = 11.25
2025-07-23 20:30:15,691 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\arialni.ttf', name='Arial', style='italic', variant='normal', weight=400, stretch='condensed', size='scalable')) = 7.613636363636363
2025-07-23 20:30:15,691 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\arialnb.ttf', name='Arial', style='normal', variant='normal', weight=700, stretch='condensed', size='scalable')) = 6.8986363636363635
2025-07-23 20:30:15,691 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\ANTQUABI.TTF', name='Book Antiqua', style='italic', variant='normal', weight=700, stretch='normal', size='scalable')) = 11.335
2025-07-23 20:30:15,691 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\FTLTLT.TTF', name='Footlight MT Light', style='normal', variant='normal', weight=300, stretch='normal', size='scalable')) = 10.145
2025-07-23 20:30:15,691 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\Shonarb.ttf', name='Shonar Bangla', style='normal', variant='normal', weight=700, stretch='normal', size='scalable')) = 10.335
2025-07-23 20:30:15,691 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\msyhbd.ttc', name='Microsoft YaHei', style='normal', variant='normal', weight=700, stretch='normal', size='scalable')) = 10.335
2025-07-23 20:30:15,691 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\GIL_____.TTF', name='Gill Sans MT', style='normal', variant='normal', weight=400, stretch='normal', size='scalable')) = 10.05
2025-07-23 20:30:15,691 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\FORTE.TTF', name='Forte', style='normal', variant='normal', weight=400, stretch='normal', size='scalable')) = 10.05
2025-07-23 20:30:15,691 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\cambriai.ttf', name='Cambria', style='italic', variant='normal', weight=400, stretch='normal', size='scalable')) = 11.05
2025-07-23 20:30:15,691 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\arial.ttf', name='Arial', style='normal', variant='normal', weight=400, stretch='normal', size='scalable')) = 6.413636363636363
2025-07-23 20:30:15,691 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\GillSansItNova.ttf', name='Gill Sans Nova', style='italic', variant='normal', weight=500, stretch='normal', size='scalable')) = 11.145
2025-07-23 20:30:15,691 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\kokilai.ttf', name='Kokila', style='italic', variant='normal', weight=400, stretch='normal', size='scalable')) = 11.05
2025-07-23 20:30:15,691 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\tunga.ttf', name='Tunga', style='normal', variant='normal', weight=400, stretch='normal', size='scalable')) = 10.05
2025-07-23 20:30:15,691 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\nyala.ttf', name='Nyala', style='normal', variant='normal', weight=400, stretch='normal', size='scalable')) = 10.05
2025-07-23 20:30:15,691 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\AGENCYR.TTF', name='Agency FB', style='normal', variant='normal', weight=400, stretch='normal', size='scalable')) = 10.05
2025-07-23 20:30:15,691 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\framdit.ttf', name='Franklin Gothic Medium', style='italic', variant='normal', weight=400, stretch='normal', size='scalable')) = 11.05
2025-07-23 20:30:15,691 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\bahnschrift.ttf', name='Bahnschrift', style='normal', variant='normal', weight=400, stretch='normal', size='scalable')) = 10.05
2025-07-23 20:30:15,691 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\GeorgiaPro-CondBlack.ttf', name='Georgia Pro', style='normal', variant='normal', weight=900, stretch='condensed', size='scalable')) = 10.725
2025-07-23 20:30:15,691 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\GillSansCondNova.ttf', name='Gill Sans Nova', style='normal', variant='normal', weight=500, stretch='condensed', size='scalable')) = 10.344999999999999
2025-07-23 20:30:15,691 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\GillSansNova.ttf', name='Gill Sans Nova', style='normal', variant='normal', weight=500, stretch='normal', size='scalable')) = 10.145
2025-07-23 20:30:15,707 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\msyhl.ttc', name='Microsoft YaHei', style='normal', variant='normal', weight=290, stretch='normal', size='scalable')) = 10.1545
2025-07-23 20:30:15,707 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\RockwellNova-Italic.ttf', name='Rockwell Nova', style='italic', variant='normal', weight=400, stretch='normal', size='scalable')) = 11.05
2025-07-23 20:30:15,707 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\BOD_BI.TTF', name='Bodoni MT', style='italic', variant='normal', weight=700, stretch='normal', size='scalable')) = 11.335
2025-07-23 20:30:15,707 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\COOPBL.TTF', name='Cooper Black', style='normal', variant='normal', weight=400, stretch='normal', size='scalable')) = 10.05
2025-07-23 20:30:15,707 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\CALIFI.TTF', name='Californian FB', style='italic', variant='normal', weight=400, stretch='normal', size='scalable')) = 11.05
2025-07-23 20:30:15,707 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\upclbi.ttf', name='LilyUPC', style='italic', variant='normal', weight=700, stretch='normal', size='scalable')) = 11.335
2025-07-23 20:30:15,707 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\RockwellNovaCond.ttf', name='Rockwell Nova', style='normal', variant='normal', weight=400, stretch='condensed', size='scalable')) = 10.25
2025-07-23 20:30:15,707 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\VerdanaPro-SemiBoldItalic.ttf', name='Verdana Pro', style='italic', variant='normal', weight=600, stretch='normal', size='scalable')) = 11.24
2025-07-23 20:30:15,707 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\GOUDOSI.TTF', name='Goudy Old Style', style='italic', variant='normal', weight=400, stretch='normal', size='scalable')) = 11.05
2025-07-23 20:30:15,707 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\andlso.ttf', name='Andalus', style='normal', variant='normal', weight=400, stretch='normal', size='scalable')) = 10.05
2025-07-23 20:30:15,707 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\SimsunExtG.ttf', name='SimSun-ExtG', style='normal', variant='normal', weight=400, stretch='normal', size='scalable')) = 10.05
2025-07-23 20:30:15,707 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\GILB____.TTF', name='Gill Sans MT', style='normal', variant='normal', weight=700, stretch='normal', size='scalable')) = 10.335
2025-07-23 20:30:15,707 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\kalingab.ttf', name='Kalinga', style='normal', variant='normal', weight=700, stretch='normal', size='scalable')) = 10.335
2025-07-23 20:30:15,707 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\Inkfree.ttf', name='Ink Free', style='normal', variant='normal', weight=400, stretch='normal', size='scalable')) = 10.05
2025-07-23 20:30:15,707 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\BSSYM7.TTF', name='Bookshelf Symbol 7', style='normal', variant='normal', weight=400, stretch='normal', size='scalable')) = 10.05
2025-07-23 20:30:15,707 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\INFROMAN.TTF', name='Informal Roman', style='normal', variant='normal', weight=400, stretch='normal', size='scalable')) = 10.05
2025-07-23 20:30:15,707 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\GillSansCondExtraItNova.ttf', name='Gill Sans Nova', style='italic', variant='normal', weight=800, stretch='condensed', size='scalable')) = 11.629999999999999
2025-07-23 20:30:15,707 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\BOD_CR.TTF', name='Bodoni MT', style='normal', variant='normal', weight=400, stretch='condensed', size='scalable')) = 10.25
2025-07-23 20:30:15,707 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\ITCBLKAD.TTF', name='Blackadder ITC', style='normal', variant='normal', weight=400, stretch='normal', size='scalable')) = 10.05
2025-07-23 20:30:15,707 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\ahronbd.ttf', name='Aharoni', style='normal', variant='normal', weight=700, stretch='normal', size='scalable')) = 10.335
2025-07-23 20:30:15,707 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\UDDigiKyokashoN-R.ttc', name='UD Digi Kyokasho N-R', style='normal', variant='normal', weight=400, stretch='normal', size='scalable')) = 10.05
2025-07-23 20:30:15,707 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\vijaya.ttf', name='Vijaya', style='normal', variant='normal', weight=400, stretch='normal', size='scalable')) = 10.05
2025-07-23 20:30:15,707 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\LaoUI.ttf', name='Lao UI', style='normal', variant='normal', weight=400, stretch='normal', size='scalable')) = 10.05
2025-07-23 20:30:15,707 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\GillSansUltraBoNova.ttf', name='Gill Sans Nova', style='normal', variant='normal', weight=900, stretch='normal', size='scalable')) = 10.525
2025-07-23 20:30:15,707 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\vijayab.ttf', name='Vijaya', style='normal', variant='normal', weight=700, stretch='normal', size='scalable')) = 10.335
2025-07-23 20:30:15,707 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\PRISTINA.TTF', name='Pristina', style='normal', variant='normal', weight=400, stretch='normal', size='scalable')) = 10.05
2025-07-23 20:30:15,707 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\msjh.ttc', name='Microsoft JhengHei', style='normal', variant='normal', weight=400, stretch='normal', size='scalable')) = 10.05
2025-07-23 20:30:15,707 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\mriamc.ttf', name='Miriam Fixed', style='normal', variant='normal', weight=400, stretch='normal', size='scalable')) = 10.05
2025-07-23 20:30:15,707 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\segoeuib.ttf', name='Segoe UI', style='normal', variant='normal', weight=700, stretch='normal', size='scalable')) = 10.335
2025-07-23 20:30:15,707 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\frank.ttf', name='FrankRuehl', style='normal', variant='normal', weight=400, stretch='normal', size='scalable')) = 10.05
2025-07-23 20:30:15,707 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\Dengb.ttf', name='DengXian', style='normal', variant='normal', weight=700, stretch='normal', size='scalable')) = 10.335
2025-07-23 20:30:15,707 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\LSANSDI.TTF', name='Lucida Sans', style='italic', variant='normal', weight=600, stretch='normal', size='scalable')) = 11.24
2025-07-23 20:30:15,707 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\CALISTBI.TTF', name='Calisto MT', style='italic', variant='normal', weight=700, stretch='normal', size='scalable')) = 11.335
2025-07-23 20:30:15,707 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\CALISTB.TTF', name='Calisto MT', style='normal', variant='normal', weight=700, stretch='normal', size='scalable')) = 10.335
2025-07-23 20:30:15,707 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\rod.ttf', name='Rod', style='normal', variant='normal', weight=400, stretch='normal', size='scalable')) = 10.05
2025-07-23 20:30:15,707 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\BROADW.TTF', name='Broadway', style='normal', variant='normal', weight=400, stretch='normal', size='scalable')) = 10.05
2025-07-23 20:30:15,707 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\lvnmbd.ttf', name='Levenim MT', style='normal', variant='normal', weight=700, stretch='normal', size='scalable')) = 10.335
2025-07-23 20:30:15,707 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\GILSANUB.TTF', name='Gill Sans Ultra Bold', style='normal', variant='normal', weight=400, stretch='normal', size='scalable')) = 10.05
2025-07-23 20:30:15,707 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\VerdanaPro-SemiBold.ttf', name='Verdana Pro', style='normal', variant='normal', weight=600, stretch='normal', size='scalable')) = 10.24
2025-07-23 20:30:15,707 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\GOTHICBI.TTF', name='Century Gothic', style='italic', variant='normal', weight=700, stretch='normal', size='scalable')) = 11.335
2025-07-23 20:30:15,707 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\simpo.ttf', name='Simplified Arabic', style='normal', variant='normal', weight=400, stretch='normal', size='scalable')) = 10.05
2025-07-23 20:30:15,707 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\RAVIE.TTF', name='Ravie', style='normal', variant='normal', weight=400, stretch='normal', size='scalable')) = 10.05
2025-07-23 20:30:15,707 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\majalla.ttf', name='Sakkal Majalla', style='normal', variant='normal', weight=400, stretch='normal', size='scalable')) = 10.05
2025-07-23 20:30:15,707 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\NIAGSOL.TTF', name='Niagara Solid', style='normal', variant='normal', weight=400, stretch='normal', size='scalable')) = 10.05
2025-07-23 20:30:15,707 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\CHILLER.TTF', name='Chiller', style='normal', variant='normal', weight=400, stretch='normal', size='scalable')) = 10.05
2025-07-23 20:30:15,707 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\consola.ttf', name='Consolas', style='normal', variant='normal', weight=400, stretch='normal', size='scalable')) = 10.05
2025-07-23 20:30:15,707 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\LBRITED.TTF', name='Lucida Bright', style='normal', variant='normal', weight=600, stretch='normal', size='scalable')) = 10.24
2025-07-23 20:30:15,707 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\BRLNSR.TTF', name='Berlin Sans FB', style='normal', variant='normal', weight=400, stretch='normal', size='scalable')) = 10.05
2025-07-23 20:30:15,707 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\calibrili.ttf', name='Calibri', style='italic', variant='normal', weight=300, stretch='normal', size='scalable')) = 11.145
2025-07-23 20:30:15,707 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\COPRGTB.TTF', name='Copperplate Gothic Bold', style='normal', variant='normal', weight=400, stretch='normal', size='scalable')) = 10.05
2025-07-23 20:30:15,707 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\simsun.ttc', name='SimSun', style='normal', variant='normal', weight=400, stretch='normal', size='scalable')) = 10.05
2025-07-23 20:30:15,707 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\MTCORSVA.TTF', name='Monotype Corsiva', style='normal', variant='normal', weight=400, stretch='normal', size='scalable')) = 10.05
2025-07-23 20:30:15,707 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\RockwellNovaCond-Light.ttf', name='Rockwell Nova', style='normal', variant='normal', weight=300, stretch='condensed', size='scalable')) = 10.344999999999999
2025-07-23 20:30:15,707 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\Candaral.ttf', name='Candara', style='normal', variant='normal', weight=400, stretch='normal', size='scalable')) = 10.05
2025-07-23 20:30:15,707 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\Candara.ttf', name='Candara', style='normal', variant='normal', weight=400, stretch='normal', size='scalable')) = 10.05
2025-07-23 20:30:15,707 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\seguibli.ttf', name='Segoe UI', style='italic', variant='normal', weight=900, stretch='normal', size='scalable')) = 11.525
2025-07-23 20:30:15,707 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\cambriaz.ttf', name='Cambria', style='italic', variant='normal', weight=700, stretch='normal', size='scalable')) = 11.335
2025-07-23 20:30:15,707 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\upcjl.ttf', name='JasmineUPC', style='normal', variant='normal', weight=400, stretch='normal', size='scalable')) = 10.05
2025-07-23 20:30:15,707 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\BOOKOSB.TTF', name='Bookman Old Style', style='normal', variant='normal', weight=600, stretch='normal', size='scalable')) = 10.24
2025-07-23 20:30:15,707 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\JUICE___.TTF', name='Juice ITC', style='normal', variant='normal', weight=400, stretch='normal', size='scalable')) = 10.05
2025-07-23 20:30:15,707 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\NotoSansHK-VF.ttf', name='Noto Sans HK', style='normal', variant='normal', weight=100, stretch='normal', size='scalable')) = 10.335
2025-07-23 20:30:15,707 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\Shonar.ttf', name='Shonar Bangla', style='normal', variant='normal', weight=400, stretch='normal', size='scalable')) = 10.05
2025-07-23 20:30:15,707 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\nrkis.ttf', name='Narkisim', style='normal', variant='normal', weight=400, stretch='normal', size='scalable')) = 10.05
2025-07-23 20:30:15,707 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\mingliub.ttc', name='MingLiU-ExtB', style='normal', variant='normal', weight=400, stretch='normal', size='scalable')) = 10.05
2025-07-23 20:30:15,707 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\GLSNECB.TTF', name='Gill Sans MT Ext Condensed Bold', style='normal', variant='normal', weight=400, stretch='condensed', size='scalable')) = 10.25
2025-07-23 20:30:15,707 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\BOOKOSI.TTF', name='Bookman Old Style', style='italic', variant='normal', weight=300, stretch='normal', size='scalable')) = 11.145
2025-07-23 20:30:15,707 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\HARLOWSI.TTF', name='Harlow Solid Italic', style='italic', variant='normal', weight=400, stretch='normal', size='scalable')) = 11.05
2025-07-23 20:30:15,707 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\VerdanaPro-CondBoldItalic.ttf', name='Verdana Pro', style='italic', variant='normal', weight=700, stretch='condensed', size='scalable')) = 11.535
2025-07-23 20:30:15,707 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\GillSansBoItNova.ttf', name='Gill Sans Nova', style='italic', variant='normal', weight=700, stretch='normal', size='scalable')) = 11.335
2025-07-23 20:30:15,707 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\l_10646.ttf', name='Lucida Sans Unicode', style='normal', variant='normal', weight=400, stretch='normal', size='scalable')) = 10.05
2025-07-23 20:30:15,707 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\gishabd.ttf', name='Gisha', style='normal', variant='normal', weight=700, stretch='normal', size='scalable')) = 10.335
2025-07-23 20:30:15,707 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\BOD_B.TTF', name='Bodoni MT', style='normal', variant='normal', weight=700, stretch='normal', size='scalable')) = 10.335
2025-07-23 20:30:15,707 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\utsaah.ttf', name='Utsaah', style='normal', variant='normal', weight=400, stretch='normal', size='scalable')) = 10.05
2025-07-23 20:30:15,707 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\utsaahb.ttf', name='Utsaah', style='normal', variant='normal', weight=700, stretch='normal', size='scalable')) = 10.335
2025-07-23 20:30:15,707 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\TCCEB.TTF', name='Tw Cen MT Condensed Extra Bold', style='normal', variant='normal', weight=400, stretch='condensed', size='scalable')) = 10.25
2025-07-23 20:30:15,707 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\GeorgiaPro-CondLightItalic.ttf', name='Georgia Pro', style='italic', variant='normal', weight=300, stretch='condensed', size='scalable')) = 11.344999999999999
2025-07-23 20:30:15,707 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\verdanai.ttf', name='Verdana', style='italic', variant='normal', weight=400, stretch='normal', size='scalable')) = 4.6863636363636365
2025-07-23 20:30:15,707 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\ntailu.ttf', name='Microsoft New Tai Lue', style='normal', variant='normal', weight=400, stretch='normal', size='scalable')) = 10.05
2025-07-23 20:30:15,707 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\SitkaZ.ttc', name='Sitka Small', style='italic', variant='normal', weight=700, stretch='normal', size='scalable')) = 11.335
2025-07-23 20:30:15,707 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\LATINWD.TTF', name='Wide Latin', style='normal', variant='normal', weight=400, stretch='expanded', size='scalable')) = 10.25
2025-07-23 20:30:15,707 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\segoeprb.ttf', name='Segoe Print', style='normal', variant='normal', weight=700, stretch='normal', size='scalable')) = 10.335
2025-07-23 20:30:15,707 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\segoeui.ttf', name='Segoe UI', style='normal', variant='normal', weight=400, stretch='normal', size='scalable')) = 10.05
2025-07-23 20:30:15,707 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\COLONNA.TTF', name='Colonna MT', style='normal', variant='normal', weight=400, stretch='normal', size='scalable')) = 10.05
2025-07-23 20:30:15,707 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\Gabriola.ttf', name='Gabriola', style='normal', variant='normal', weight=400, stretch='normal', size='scalable')) = 10.05
2025-07-23 20:30:15,707 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\UDDigiKyokashoN-B.ttc', name='UD Digi Kyokasho N-B', style='normal', variant='normal', weight=700, stretch='normal', size='scalable')) = 10.335
2025-07-23 20:30:15,707 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\phagspa.ttf', name='Microsoft PhagsPa', style='normal', variant='normal', weight=400, stretch='normal', size='scalable')) = 10.05
2025-07-23 20:30:15,707 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\FRSCRIPT.TTF', name='French Script MT', style='normal', variant='normal', weight=400, stretch='normal', size='scalable')) = 10.05
2025-07-23 20:30:15,707 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\NotoSansJP-VF.ttf', name='Noto Sans JP', style='normal', variant='normal', weight=100, stretch='normal', size='scalable')) = 10.335
2025-07-23 20:30:15,707 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\GeorgiaPro-CondSemiBoldItalic.ttf', name='Georgia Pro', style='italic', variant='normal', weight=600, stretch='condensed', size='scalable')) = 11.44
2025-07-23 20:30:15,707 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\ArialNova-Light.ttf', name='Arial Nova', style='normal', variant='normal', weight=300, stretch='normal', size='scalable')) = 10.145
2025-07-23 20:30:15,707 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\davidbd.ttf', name='David', style='normal', variant='normal', weight=700, stretch='normal', size='scalable')) = 10.335
2025-07-23 20:30:15,723 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\GeorgiaPro-CondLight.ttf', name='Georgia Pro', style='normal', variant='normal', weight=300, stretch='condensed', size='scalable')) = 10.344999999999999
2025-07-23 20:30:15,723 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\GOTHICI.TTF', name='Century Gothic', style='italic', variant='normal', weight=400, stretch='normal', size='scalable')) = 11.05
2025-07-23 20:30:15,723 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\VLADIMIR.TTF', name='Vladimir Script', style='normal', variant='normal', weight=400, stretch='normal', size='scalable')) = 10.05
2025-07-23 20:30:15,723 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\upcjbi.ttf', name='JasmineUPC', style='italic', variant='normal', weight=700, stretch='normal', size='scalable')) = 11.335
2025-07-23 20:30:15,723 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\HPSimplified_It.ttf', name='HP Simplified', style='italic', variant='normal', weight=400, stretch='normal', size='scalable')) = 11.05
2025-07-23 20:30:15,723 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\GeorgiaPro-Black.ttf', name='Georgia Pro', style='normal', variant='normal', weight=900, stretch='normal', size='scalable')) = 10.525
2025-07-23 20:30:15,723 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\ERASMD.TTF', name='Eras Medium ITC', style='normal', variant='normal', weight=400, stretch='normal', size='scalable')) = 10.05
2025-07-23 20:30:15,723 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\VerdanaPro-LightItalic.ttf', name='Verdana Pro', style='italic', variant='normal', weight=300, stretch='normal', size='scalable')) = 11.145
2025-07-23 20:30:15,723 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\yumindb.ttf', name='Yu Mincho', style='normal', variant='normal', weight=600, stretch='normal', size='scalable')) = 10.24
2025-07-23 20:30:15,723 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\BAUHS93.TTF', name='Bauhaus 93', style='normal', variant='normal', weight=400, stretch='normal', size='scalable')) = 10.05
2025-07-23 20:30:15,723 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\mmrtextb.ttf', name='Myanmar Text', style='normal', variant='normal', weight=700, stretch='normal', size='scalable')) = 10.335
2025-07-23 20:30:15,723 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\OCRAEXT.TTF', name='OCR A Extended', style='normal', variant='normal', weight=400, stretch='expanded', size='scalable')) = 10.25
2025-07-23 20:30:15,723 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\kalinga.ttf', name='Kalinga', style='normal', variant='normal', weight=400, stretch='normal', size='scalable')) = 10.05
2025-07-23 20:30:15,723 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\MISTRAL.TTF', name='Mistral', style='normal', variant='normal', weight=400, stretch='normal', size='scalable')) = 10.05
2025-07-23 20:30:15,723 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\simfang.ttf', name='FangSong', style='normal', variant='normal', weight=400, stretch='normal', size='scalable')) = 10.05
2025-07-23 20:30:15,723 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\VerdanaPro-Bold.ttf', name='Verdana Pro', style='normal', variant='normal', weight=700, stretch='normal', size='scalable')) = 10.335
2025-07-23 20:30:15,723 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\upcki.ttf', name='KodchiangUPC', style='italic', variant='normal', weight=400, stretch='normal', size='scalable')) = 11.05
2025-07-23 20:30:15,723 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\ntailub.ttf', name='Microsoft New Tai Lue', style='normal', variant='normal', weight=700, stretch='normal', size='scalable')) = 10.335
2025-07-23 20:30:15,723 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\ALGER.TTF', name='Algerian', style='normal', variant='normal', weight=400, stretch='normal', size='scalable')) = 10.05
2025-07-23 20:30:15,723 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\RockwellNova-LightItalic.ttf', name='Rockwell Nova', style='italic', variant='normal', weight=300, stretch='normal', size='scalable')) = 11.145
2025-07-23 20:30:15,723 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\upcdi.ttf', name='DilleniaUPC', style='italic', variant='normal', weight=400, stretch='normal', size='scalable')) = 11.05
2025-07-23 20:30:15,723 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\GillSansCondExtraNova.ttf', name='Gill Sans Nova', style='normal', variant='normal', weight=800, stretch='condensed', size='scalable')) = 10.629999999999999
2025-07-23 20:30:15,723 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\ARIALUNI.TTF', name='Arial Unicode MS', style='normal', variant='normal', weight=400, stretch='normal', size='scalable')) = 10.05
2025-07-23 20:30:15,723 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\palai.ttf', name='Palatino Linotype', style='italic', variant='normal', weight=400, stretch='normal', size='scalable')) = 11.05
2025-07-23 20:30:15,723 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\ROCC____.TTF', name='Rockwell Condensed', style='normal', variant='normal', weight=400, stretch='condensed', size='scalable')) = 10.25
2025-07-23 20:30:15,723 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\GeorgiaPro-CondRegular.ttf', name='Georgia Pro', style='normal', variant='normal', weight=400, stretch='condensed', size='scalable')) = 10.25
2025-07-23 20:30:15,723 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\LFAX.TTF', name='Lucida Fax', style='normal', variant='normal', weight=400, stretch='normal', size='scalable')) = 10.05
2025-07-23 20:30:15,723 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\TCCB____.TTF', name='Tw Cen MT Condensed', style='normal', variant='normal', weight=700, stretch='condensed', size='scalable')) = 10.535
2025-07-23 20:30:15,723 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\tungab.ttf', name='Tunga', style='normal', variant='normal', weight=700, stretch='normal', size='scalable')) = 10.335
2025-07-23 20:30:15,723 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\FRAMDCN.TTF', name='Franklin Gothic Medium Cond', style='normal', variant='normal', weight=400, stretch='condensed', size='scalable')) = 10.25
2025-07-23 20:30:15,723 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\kartika.ttf', name='Kartika', style='normal', variant='normal', weight=400, stretch='normal', size='scalable')) = 10.05
2025-07-23 20:30:15,723 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\PAPYRUS.TTF', name='Papyrus', style='normal', variant='normal', weight=400, stretch='normal', size='scalable')) = 10.05
2025-07-23 20:30:15,723 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\LFAXI.TTF', name='Lucida Fax', style='italic', variant='normal', weight=400, stretch='normal', size='scalable')) = 11.05
2025-07-23 20:30:15,723 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\BERNHC.TTF', name='Bernard MT Condensed', style='normal', variant='normal', weight=400, stretch='condensed', size='scalable')) = 10.25
2025-07-23 20:30:15,723 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\GeorgiaPro-SemiBold.ttf', name='Georgia Pro', style='normal', variant='normal', weight=600, stretch='normal', size='scalable')) = 10.24
2025-07-23 20:30:15,723 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\simsunb.ttf', name='SimSun-ExtB', style='normal', variant='normal', weight=400, stretch='normal', size='scalable')) = 10.05
2025-07-23 20:30:15,723 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\cambriab.ttf', name='Cambria', style='normal', variant='normal', weight=700, stretch='normal', size='scalable')) = 10.335
2025-07-23 20:30:15,723 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\ArialNovaCond-Light.ttf', name='Arial Nova', style='normal', variant='normal', weight=300, stretch='condensed', size='scalable')) = 10.344999999999999
2025-07-23 20:30:15,723 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\ArialNova-BoldItalic.ttf', name='Arial Nova', style='italic', variant='normal', weight=700, stretch='normal', size='scalable')) = 11.335
2025-07-23 20:30:15,723 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\yuminl.ttf', name='Yu Mincho', style='normal', variant='normal', weight=300, stretch='normal', size='scalable')) = 10.145
2025-07-23 20:30:15,723 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\BRLNSDB.TTF', name='Berlin Sans FB Demi', style='normal', variant='normal', weight=700, stretch='normal', size='scalable')) = 10.335
2025-07-23 20:30:15,723 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\taile.ttf', name='Microsoft Tai Le', style='normal', variant='normal', weight=400, stretch='normal', size='scalable')) = 10.05
2025-07-23 20:30:15,723 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\courbd.ttf', name='Courier New', style='normal', variant='normal', weight=700, stretch='normal', size='scalable')) = 10.335
2025-07-23 20:30:15,723 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\gadugib.ttf', name='Gadugi', style='normal', variant='normal', weight=700, stretch='normal', size='scalable')) = 10.335
2025-07-23 20:30:15,723 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\mvboli.ttf', name='MV Boli', style='normal', variant='normal', weight=400, stretch='normal', size='scalable')) = 10.05
2025-07-23 20:30:15,723 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\upcfi.ttf', name='FreesiaUPC', style='italic', variant='normal', weight=400, stretch='normal', size='scalable')) = 11.05
2025-07-23 20:30:15,723 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\VerdanaPro-CondBlackItalic.ttf', name='Verdana Pro', style='italic', variant='normal', weight=900, stretch='condensed', size='scalable')) = 11.725
2025-07-23 20:30:15,723 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\consolaz.ttf', name='Consolas', style='italic', variant='normal', weight=700, stretch='normal', size='scalable')) = 11.335
2025-07-23 20:30:15,723 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\GeorgiaPro-Italic.ttf', name='Georgia Pro', style='italic', variant='normal', weight=400, stretch='normal', size='scalable')) = 11.05
2025-07-23 20:30:15,723 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\PALSCRI.TTF', name='Palace Script MT', style='normal', variant='normal', weight=400, stretch='normal', size='scalable')) = 10.05
2025-07-23 20:30:15,723 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\PERTIBD.TTF', name='Perpetua Titling MT', style='normal', variant='normal', weight=700, stretch='normal', size='scalable')) = 10.335
2025-07-23 20:30:15,723 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\segoepr.ttf', name='Segoe Print', style='normal', variant='normal', weight=400, stretch='normal', size='scalable')) = 10.05
2025-07-23 20:30:15,723 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\ROCK.TTF', name='Rockwell', style='normal', variant='normal', weight=400, stretch='normal', size='scalable')) = 10.05
2025-07-23 20:30:15,723 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\GILI____.TTF', name='Gill Sans MT', style='italic', variant='normal', weight=400, stretch='normal', size='scalable')) = 11.05
2025-07-23 20:30:15,723 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\upcib.ttf', name='IrisUPC', style='normal', variant='normal', weight=700, stretch='normal', size='scalable')) = 10.335
2025-07-23 20:30:15,723 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\GillSansCondBoItNova.ttf', name='Gill Sans Nova', style='italic', variant='normal', weight=700, stretch='condensed', size='scalable')) = 11.535
2025-07-23 20:30:15,723 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\upcli.ttf', name='LilyUPC', style='italic', variant='normal', weight=400, stretch='normal', size='scalable')) = 11.05
2025-07-23 20:30:15,723 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\NirmalaB.ttf', name='Nirmala UI', style='normal', variant='normal', weight=700, stretch='normal', size='scalable')) = 10.335
2025-07-23 20:30:15,723 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\PARCHM.TTF', name='Parchment', style='normal', variant='normal', weight=400, stretch='normal', size='scalable')) = 10.05
2025-07-23 20:30:15,723 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\FRADMIT.TTF', name='Franklin Gothic Demi', style='italic', variant='normal', weight=400, stretch='normal', size='scalable')) = 11.05
2025-07-23 20:30:15,723 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\arabtype.ttf', name='Arabic Typesetting', style='normal', variant='normal', weight=400, stretch='normal', size='scalable')) = 10.05
2025-07-23 20:30:15,723 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\TEMPSITC.TTF', name='Tempus Sans ITC', style='normal', variant='normal', weight=400, stretch='normal', size='scalable')) = 10.05
2025-07-23 20:30:15,723 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\cordia.ttc', name='Cordia New', style='normal', variant='normal', weight=400, stretch='normal', size='scalable')) = 10.05
2025-07-23 20:30:15,723 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\ITCEDSCR.TTF', name='Edwardian Script ITC', style='normal', variant='normal', weight=400, stretch='normal', size='scalable')) = 10.05
2025-07-23 20:30:15,723 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\MAGNETOB.TTF', name='Magneto', style='normal', variant='normal', weight=700, stretch='normal', size='scalable')) = 10.335
2025-07-23 20:30:15,723 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\seguibl.ttf', name='Segoe UI', style='normal', variant='normal', weight=900, stretch='normal', size='scalable')) = 10.525
2025-07-23 20:30:15,723 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\YuGothB.ttc', name='Yu Gothic', style='normal', variant='normal', weight=700, stretch='normal', size='scalable')) = 10.335
2025-07-23 20:30:15,723 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\VerdanaPro-Light.ttf', name='Verdana Pro', style='normal', variant='normal', weight=300, stretch='normal', size='scalable')) = 10.145
2025-07-23 20:30:15,723 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\NotoSansSC-VF.ttf', name='Noto Sans SC', style='normal', variant='normal', weight=100, stretch='normal', size='scalable')) = 10.335
2025-07-23 20:30:15,723 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\micross.ttf', name='Microsoft Sans Serif', style='normal', variant='normal', weight=400, stretch='normal', size='scalable')) = 10.05
2025-07-23 20:30:15,723 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\FRADMCN.TTF', name='Franklin Gothic Demi Cond', style='normal', variant='normal', weight=400, stretch='condensed', size='scalable')) = 10.25
2025-07-23 20:30:15,723 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\upcel.ttf', name='EucrosiaUPC', style='normal', variant='normal', weight=400, stretch='normal', size='scalable')) = 10.05
2025-07-23 20:30:15,723 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\BOD_BLAI.TTF', name='Bodoni MT', style='italic', variant='normal', weight=900, stretch='normal', size='scalable')) = 11.525
2025-07-23 20:30:15,723 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\CALIFR.TTF', name='Californian FB', style='normal', variant='normal', weight=400, stretch='normal', size='scalable')) = 10.05
2025-07-23 20:30:15,723 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\VIVALDII.TTF', name='Vivaldi', style='italic', variant='normal', weight=400, stretch='normal', size='scalable')) = 11.05
2025-07-23 20:30:15,723 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\GARABD.TTF', name='Garamond', style='normal', variant='normal', weight=700, stretch='normal', size='scalable')) = 10.335
2025-07-23 20:30:15,723 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\upckb.ttf', name='KodchiangUPC', style='normal', variant='normal', weight=700, stretch='normal', size='scalable')) = 10.335
2025-07-23 20:30:15,723 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\FRABK.TTF', name='Franklin Gothic Book', style='normal', variant='normal', weight=400, stretch='normal', size='scalable')) = 10.05
2025-07-23 20:30:15,723 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\georgiai.ttf', name='Georgia', style='italic', variant='normal', weight=400, stretch='normal', size='scalable')) = 11.05
2025-07-23 20:30:15,723 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\COPRGTL.TTF', name='Copperplate Gothic Light', style='normal', variant='normal', weight=400, stretch='normal', size='scalable')) = 10.05
2025-07-23 20:30:15,723 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\VerdanaPro-BlackItalic.ttf', name='Verdana Pro', style='italic', variant='normal', weight=900, stretch='normal', size='scalable')) = 11.525
2025-07-23 20:30:15,723 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\upcji.ttf', name='JasmineUPC', style='italic', variant='normal', weight=400, stretch='normal', size='scalable')) = 11.05
2025-07-23 20:30:15,723 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\GillSansCondItNova.ttf', name='Gill Sans Nova', style='italic', variant='normal', weight=500, stretch='condensed', size='scalable')) = 11.344999999999999
2025-07-23 20:30:15,723 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\GeorgiaPro-Bold.ttf', name='Georgia Pro', style='normal', variant='normal', weight=700, stretch='normal', size='scalable')) = 10.335
2025-07-23 20:30:15,723 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\BASKVILL.TTF', name='Baskerville Old Face', style='normal', variant='normal', weight=400, stretch='normal', size='scalable')) = 10.05
2025-07-23 20:30:15,723 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\segoeuil.ttf', name='Segoe UI', style='normal', variant='normal', weight=300, stretch='normal', size='scalable')) = 10.145
2025-07-23 20:30:15,723 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\GIGI.TTF', name='Gigi', style='normal', variant='normal', weight=400, stretch='normal', size='scalable')) = 10.05
2025-07-23 20:30:15,723 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\IMPRISHA.TTF', name='Imprint MT Shadow', style='normal', variant='normal', weight=400, stretch='normal', size='scalable')) = 10.05
2025-07-23 20:30:15,723 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\GLECB.TTF', name='Gloucester MT Extra Condensed', style='normal', variant='normal', weight=400, stretch='condensed', size='scalable')) = 10.25
2025-07-23 20:30:15,723 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\KUNSTLER.TTF', name='Kunstler Script', style='normal', variant='normal', weight=400, stretch='normal', size='scalable')) = 10.05
2025-07-23 20:30:15,723 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\GillSansCondUltraBoNova.ttf', name='Gill Sans Nova', style='normal', variant='normal', weight=900, stretch='condensed', size='scalable')) = 10.725
2025-07-23 20:30:15,738 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\CENSCBK.TTF', name='Century Schoolbook', style='normal', variant='normal', weight=400, stretch='normal', size='scalable')) = 10.05
2025-07-23 20:30:15,738 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\MOD20.TTF', name='Modern No. 20', style='normal', variant='normal', weight=400, stretch='normal', size='scalable')) = 10.05
2025-07-23 20:30:15,738 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\VerdanaPro-BoldItalic.ttf', name='Verdana Pro', style='italic', variant='normal', weight=700, stretch='normal', size='scalable')) = 11.335
2025-07-23 20:30:15,738 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\LBRITEDI.TTF', name='Lucida Bright', style='italic', variant='normal', weight=600, stretch='normal', size='scalable')) = 11.24
2025-07-23 20:30:15,738 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\POORICH.TTF', name='Poor Richard', style='normal', variant='normal', weight=400, stretch='normal', size='scalable')) = 10.05
2025-07-23 20:30:15,738 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\UrdType.ttf', name='Urdu Typesetting', style='normal', variant='normal', weight=400, stretch='normal', size='scalable')) = 10.05
2025-07-23 20:30:15,738 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\aparajbi.ttf', name='Aparajita', style='italic', variant='normal', weight=700, stretch='normal', size='scalable')) = 11.335
2025-07-23 20:30:15,738 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\ROCKEB.TTF', name='Rockwell Extra Bold', style='normal', variant='normal', weight=800, stretch='normal', size='scalable')) = 10.43
2025-07-23 20:30:15,738 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\euphemia.ttf', name='Euphemia', style='normal', variant='normal', weight=400, stretch='normal', size='scalable')) = 10.05
2025-07-23 20:30:15,738 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\RockwellNovaCond-BoldItalic.ttf', name='Rockwell Nova', style='italic', variant='normal', weight=700, stretch='condensed', size='scalable')) = 11.535
2025-07-23 20:30:15,738 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\TCB_____.TTF', name='Tw Cen MT', style='normal', variant='normal', weight=700, stretch='normal', size='scalable')) = 10.335
2025-07-23 20:30:15,738 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\aparaji.ttf', name='Aparajita', style='italic', variant='normal', weight=400, stretch='normal', size='scalable')) = 11.05
2025-07-23 20:30:15,738 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\upcfl.ttf', name='FreesiaUPC', style='normal', variant='normal', weight=400, stretch='normal', size='scalable')) = 10.05
2025-07-23 20:30:15,738 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\upceb.ttf', name='EucrosiaUPC', style='normal', variant='normal', weight=700, stretch='normal', size='scalable')) = 10.335
2025-07-23 20:30:15,738 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\calibri.ttf', name='Calibri', style='normal', variant='normal', weight=400, stretch='normal', size='scalable')) = 10.05
2025-07-23 20:30:15,738 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\seguisli.ttf', name='Segoe UI', style='italic', variant='normal', weight=350, stretch='normal', size='scalable')) = 11.0975
2025-07-23 20:30:15,738 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\TCM_____.TTF', name='Tw Cen MT', style='normal', variant='normal', weight=400, stretch='normal', size='scalable')) = 10.05
2025-07-23 20:30:15,738 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\corbelz.ttf', name='Corbel', style='italic', variant='normal', weight=700, stretch='normal', size='scalable')) = 11.335
2025-07-23 20:30:15,738 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\ariblk.ttf', name='Arial', style='normal', variant='normal', weight=900, stretch='normal', size='scalable')) = 6.888636363636364
2025-07-23 20:30:15,738 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\mmrtext.ttf', name='Myanmar Text', style='normal', variant='normal', weight=400, stretch='normal', size='scalable')) = 10.05
2025-07-23 20:30:15,738 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\georgia.ttf', name='Georgia', style='normal', variant='normal', weight=400, stretch='normal', size='scalable')) = 10.05
2025-07-23 20:30:15,738 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\msjhl.ttc', name='Microsoft JhengHei', style='normal', variant='normal', weight=290, stretch='normal', size='scalable')) = 10.1545
2025-07-23 20:30:15,738 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\tradbdo.ttf', name='Traditional Arabic', style='normal', variant='normal', weight=700, stretch='normal', size='scalable')) = 10.335
2025-07-23 20:30:15,738 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\ArialNovaCond-Italic.ttf', name='Arial Nova', style='italic', variant='normal', weight=400, stretch='condensed', size='scalable')) = 11.25
2025-07-23 20:30:15,738 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\msjhbd.ttc', name='Microsoft JhengHei', style='normal', variant='normal', weight=700, stretch='normal', size='scalable')) = 10.335
2025-07-23 20:30:15,738 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\FRAHVIT.TTF', name='Franklin Gothic Heavy', style='italic', variant='normal', weight=400, stretch='normal', size='scalable')) = 11.05
2025-07-23 20:30:15,738 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\georgiab.ttf', name='Georgia', style='normal', variant='normal', weight=700, stretch='normal', size='scalable')) = 10.335
2025-07-23 20:30:15,738 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\daunpenh.ttf', name='DaunPenh', style='normal', variant='normal', weight=400, stretch='normal', size='scalable')) = 10.05
2025-07-23 20:30:15,738 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\HTOWERT.TTF', name='High Tower Text', style='normal', variant='normal', weight=400, stretch='normal', size='scalable')) = 10.05
2025-07-23 20:30:15,738 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\david.ttf', name='David', style='normal', variant='normal', weight=400, stretch='normal', size='scalable')) = 10.05
2025-07-23 20:30:15,738 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\NotoSansKR-VF.ttf', name='Noto Sans KR', style='normal', variant='normal', weight=100, stretch='normal', size='scalable')) = 10.335
2025-07-23 20:30:15,738 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\ANTQUAI.TTF', name='Book Antiqua', style='italic', variant='normal', weight=400, stretch='normal', size='scalable')) = 11.05
2025-07-23 20:30:15,738 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\holomdl2.ttf', name='HoloLens MDL2 Assets', style='normal', variant='normal', weight=400, stretch='normal', size='scalable')) = 10.05
2025-07-23 20:30:15,738 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\corbeli.ttf', name='Corbel', style='italic', variant='normal', weight=400, stretch='normal', size='scalable')) = 11.05
2025-07-23 20:30:15,738 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\aparajb.ttf', name='Aparajita', style='normal', variant='normal', weight=700, stretch='normal', size='scalable')) = 10.335
2025-07-23 20:30:15,738 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\constanb.ttf', name='Constantia', style='normal', variant='normal', weight=700, stretch='normal', size='scalable')) = 10.335
2025-07-23 20:30:15,738 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\GeorgiaPro-LightItalic.ttf', name='Georgia Pro', style='italic', variant='normal', weight=300, stretch='normal', size='scalable')) = 11.145
2025-07-23 20:30:15,738 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\plantc.ttf', name='Plantagenet Cherokee', style='normal', variant='normal', weight=400, stretch='normal', size='scalable')) = 10.05
2025-07-23 20:30:15,738 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\HARNGTON.TTF', name='Harrington', style='normal', variant='normal', weight=400, stretch='normal', size='scalable')) = 10.05
2025-07-23 20:30:15,738 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\himalaya.ttf', name='Microsoft Himalaya', style='normal', variant='normal', weight=400, stretch='normal', size='scalable')) = 10.05
2025-07-23 20:30:15,738 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\TCBI____.TTF', name='Tw Cen MT', style='italic', variant='normal', weight=700, stretch='normal', size='scalable')) = 11.335
2025-07-23 20:30:15,738 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\leelawdb.ttf', name='Leelawadee', style='normal', variant='normal', weight=700, stretch='normal', size='scalable')) = 10.335
2025-07-23 20:30:15,738 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\ariali.ttf', name='Arial', style='italic', variant='normal', weight=400, stretch='normal', size='scalable')) = 7.413636363636363
2025-07-23 20:30:15,738 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\LSANS.TTF', name='Lucida Sans', style='normal', variant='normal', weight=400, stretch='normal', size='scalable')) = 10.05
2025-07-23 20:30:15,738 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\iskpota.ttf', name='Iskoola Pota', style='normal', variant='normal', weight=400, stretch='normal', size='scalable')) = 10.05
2025-07-23 20:30:15,738 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\raavi.ttf', name='Raavi', style='normal', variant='normal', weight=400, stretch='normal', size='scalable')) = 10.05
2025-07-23 20:30:15,738 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\ArialNovaCond-LightItalic.ttf', name='Arial Nova', style='italic', variant='normal', weight=300, stretch='condensed', size='scalable')) = 11.344999999999999
2025-07-23 20:30:15,738 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\PLAYBILL.TTF', name='Playbill', style='normal', variant='normal', weight=400, stretch='normal', size='scalable')) = 10.05
2025-07-23 20:30:15,738 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\vrindab.ttf', name='Vrinda', style='normal', variant='normal', weight=700, stretch='normal', size='scalable')) = 10.335
2025-07-23 20:30:15,738 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\LSANSD.TTF', name='Lucida Sans', style='normal', variant='normal', weight=600, stretch='normal', size='scalable')) = 10.24
2025-07-23 20:30:15,738 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\seguili.ttf', name='Segoe UI', style='italic', variant='normal', weight=300, stretch='normal', size='scalable')) = 11.145
2025-07-23 20:30:15,738 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\estre.ttf', name='Estrangelo Edessa', style='normal', variant='normal', weight=400, stretch='normal', size='scalable')) = 10.05
2025-07-23 20:30:15,738 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\upckl.ttf', name='KodchiangUPC', style='normal', variant='normal', weight=400, stretch='normal', size='scalable')) = 10.05
2025-07-23 20:30:15,738 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\arialbd.ttf', name='Arial', style='normal', variant='normal', weight=700, stretch='normal', size='scalable')) = 6.698636363636363
2025-07-23 20:30:15,738 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\verdanab.ttf', name='Verdana', style='normal', variant='normal', weight=700, stretch='normal', size='scalable')) = 3.9713636363636367
2025-07-23 20:30:15,738 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\Dengl.ttf', name='DengXian', style='normal', variant='normal', weight=300, stretch='normal', size='scalable')) = 10.145
2025-07-23 20:30:15,738 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\KhmerUIb.ttf', name='Khmer UI', style='normal', variant='normal', weight=700, stretch='normal', size='scalable')) = 10.335
2025-07-23 20:30:15,738 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\ArialNovaCond-BoldItalic.ttf', name='Arial Nova', style='italic', variant='normal', weight=700, stretch='condensed', size='scalable')) = 11.535
2025-07-23 20:30:15,738 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\meiryob.ttc', name='Meiryo', style='normal', variant='normal', weight=700, stretch='normal', size='scalable')) = 10.335
2025-07-23 20:30:15,738 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\BRLNSB.TTF', name='Berlin Sans FB', style='normal', variant='normal', weight=700, stretch='normal', size='scalable')) = 10.335
2025-07-23 20:30:15,738 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\verdanaz.ttf', name='Verdana', style='italic', variant='normal', weight=700, stretch='normal', size='scalable')) = 4.971363636363637
2025-07-23 20:30:15,738 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\GOUDOS.TTF', name='Goudy Old Style', style='normal', variant='normal', weight=400, stretch='normal', size='scalable')) = 10.05
2025-07-23 20:30:15,738 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\upcdbi.ttf', name='DilleniaUPC', style='italic', variant='normal', weight=700, stretch='normal', size='scalable')) = 11.335
2025-07-23 20:30:15,738 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\SitkaB.ttc', name='Sitka Small', style='normal', variant='normal', weight=700, stretch='normal', size='scalable')) = 10.335
2025-07-23 20:30:15,738 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\ARLRDBD.TTF', name='Arial Rounded MT Bold', style='normal', variant='normal', weight=400, stretch='normal', size='scalable')) = 10.05
2025-07-23 20:30:15,738 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\kartikab.ttf', name='Kartika', style='normal', variant='normal', weight=700, stretch='normal', size='scalable')) = 10.335
2025-07-23 20:30:15,738 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\BELLB.TTF', name='Bell MT', style='normal', variant='normal', weight=700, stretch='normal', size='scalable')) = 10.335
2025-07-23 20:30:15,738 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\wingding.ttf', name='Wingdings', style='normal', variant='normal', weight=400, stretch='normal', size='scalable')) = 10.05
2025-07-23 20:30:15,738 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\mangal.ttf', name='Mangal', style='normal', variant='normal', weight=400, stretch='normal', size='scalable')) = 10.05
2025-07-23 20:30:15,738 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\WINGDNG3.TTF', name='Wingdings 3', style='normal', variant='normal', weight=400, stretch='normal', size='scalable')) = 10.05
2025-07-23 20:30:15,738 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\RockwellNovaCond-LightItalic.ttf', name='Rockwell Nova', style='italic', variant='normal', weight=300, stretch='condensed', size='scalable')) = 11.344999999999999
2025-07-23 20:30:15,738 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\HPSimplified_LtIt.ttf', name='HP Simplified', style='italic', variant='normal', weight=300, stretch='normal', size='scalable')) = 11.145
2025-07-23 20:30:15,738 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\VerdanaPro-CondLight.ttf', name='Verdana Pro', style='normal', variant='normal', weight=300, stretch='condensed', size='scalable')) = 10.344999999999999
2025-07-23 20:30:15,738 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\cambria.ttc', name='Cambria', style='normal', variant='normal', weight=400, stretch='normal', size='scalable')) = 10.05
2025-07-23 20:30:15,738 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\Candarai.ttf', name='Candara', style='italic', variant='normal', weight=400, stretch='normal', size='scalable')) = 11.05
2025-07-23 20:30:15,738 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\CALIST.TTF', name='Calisto MT', style='normal', variant='normal', weight=400, stretch='normal', size='scalable')) = 10.05
2025-07-23 20:30:15,738 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\ebrimabd.ttf', name='Ebrima', style='normal', variant='normal', weight=700, stretch='normal', size='scalable')) = 10.335
2025-07-23 20:30:15,738 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\CALIFB.TTF', name='Californian FB', style='normal', variant='normal', weight=700, stretch='normal', size='scalable')) = 10.335
2025-07-23 20:30:15,738 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\framd.ttf', name='Franklin Gothic Medium', style='normal', variant='normal', weight=400, stretch='normal', size='scalable')) = 10.05
2025-07-23 20:30:15,738 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\constani.ttf', name='Constantia', style='italic', variant='normal', weight=400, stretch='normal', size='scalable')) = 11.05
2025-07-23 20:30:15,738 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\LTYPE.TTF', name='Lucida Sans Typewriter', style='normal', variant='normal', weight=400, stretch='normal', size='scalable')) = 10.05
2025-07-23 20:30:15,738 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\LFAXD.TTF', name='Lucida Fax', style='normal', variant='normal', weight=600, stretch='normal', size='scalable')) = 10.24
2025-07-23 20:30:15,738 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\symbol.ttf', name='Symbol', style='normal', variant='normal', weight=400, stretch='normal', size='scalable')) = 10.05
2025-07-23 20:30:15,738 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\BIZ-UDGothicB.ttc', name='BIZ UDGothic', style='normal', variant='normal', weight=700, stretch='normal', size='scalable')) = 10.335
2025-07-23 20:30:15,738 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\VerdanaPro-Italic.ttf', name='Verdana Pro', style='italic', variant='normal', weight=400, stretch='normal', size='scalable')) = 11.05
2025-07-23 20:30:15,738 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\corbell.ttf', name='Corbel', style='normal', variant='normal', weight=300, stretch='normal', size='scalable')) = 10.145
2025-07-23 20:30:15,738 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\simpbdo.ttf', name='Simplified Arabic', style='normal', variant='normal', weight=700, stretch='normal', size='scalable')) = 10.335
2025-07-23 20:30:15,738 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\PER_____.TTF', name='Perpetua', style='normal', variant='normal', weight=400, stretch='normal', size='scalable')) = 10.05
2025-07-23 20:30:15,738 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\LaoUIb.ttf', name='Lao UI', style='normal', variant='normal', weight=700, stretch='normal', size='scalable')) = 10.335
2025-07-23 20:30:15,738 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\FELIXTI.TTF', name='Felix Titling', style='normal', variant='normal', weight=400, stretch='normal', size='scalable')) = 10.05
2025-07-23 20:30:15,738 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\trebuc.ttf', name='Trebuchet MS', style='normal', variant='normal', weight=400, stretch='normal', size='scalable')) = 10.05
2025-07-23 20:30:15,738 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\HTOWERTI.TTF', name='High Tower Text', style='italic', variant='normal', weight=400, stretch='normal', size='scalable')) = 11.05
2025-07-23 20:30:15,738 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\BIZ-UDMinchoM.ttc', name='BIZ UDMincho', style='normal', variant='normal', weight=500, stretch='normal', size='scalable')) = 10.145
2025-07-23 20:30:15,738 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\UrdTypeb.ttf', name='Urdu Typesetting', style='normal', variant='normal', weight=700, stretch='normal', size='scalable')) = 10.335
2025-07-23 20:30:15,738 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\VerdanaPro-CondBlack.ttf', name='Verdana Pro', style='normal', variant='normal', weight=900, stretch='condensed', size='scalable')) = 10.725
2025-07-23 20:30:15,754 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\Candarali.ttf', name='Candara', style='italic', variant='normal', weight=400, stretch='normal', size='scalable')) = 11.05
2025-07-23 20:30:15,754 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\ROCKB.TTF', name='Rockwell', style='normal', variant='normal', weight=700, stretch='normal', size='scalable')) = 10.335
2025-07-23 20:30:15,754 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\msyi.ttf', name='Microsoft Yi Baiti', style='normal', variant='normal', weight=400, stretch='normal', size='scalable')) = 10.05
2025-07-23 20:30:15,754 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\comici.ttf', name='Comic Sans MS', style='italic', variant='normal', weight=400, stretch='normal', size='scalable')) = 11.05
2025-07-23 20:30:15,754 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\cour.ttf', name='Courier New', style='normal', variant='normal', weight=400, stretch='normal', size='scalable')) = 10.05
2025-07-23 20:30:15,754 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\CENTAUR.TTF', name='Centaur', style='normal', variant='normal', weight=400, stretch='normal', size='scalable')) = 10.05
2025-07-23 20:30:15,754 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\VerdanaPro-CondBold.ttf', name='Verdana Pro', style='normal', variant='normal', weight=700, stretch='condensed', size='scalable')) = 10.535
2025-07-23 20:30:15,754 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\GOUDOSB.TTF', name='Goudy Old Style', style='normal', variant='normal', weight=700, stretch='normal', size='scalable')) = 10.335
2025-07-23 20:30:15,754 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\MATURASC.TTF', name='Matura MT Script Capitals', style='normal', variant='normal', weight=400, stretch='normal', size='scalable')) = 10.05
2025-07-23 20:30:15,754 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\FRAHV.TTF', name='Franklin Gothic Heavy', style='normal', variant='normal', weight=400, stretch='normal', size='scalable')) = 10.05
2025-07-23 20:30:15,754 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\corbelb.ttf', name='Corbel', style='normal', variant='normal', weight=700, stretch='normal', size='scalable')) = 10.335
2025-07-23 20:30:15,754 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\ArialNova-Bold.ttf', name='Arial Nova', style='normal', variant='normal', weight=700, stretch='normal', size='scalable')) = 10.335
2025-07-23 20:30:15,754 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\timesbd.ttf', name='Times New Roman', style='normal', variant='normal', weight=700, stretch='normal', size='scalable')) = 10.335
2025-07-23 20:30:15,754 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\LSANSI.TTF', name='Lucida Sans', style='italic', variant='normal', weight=400, stretch='normal', size='scalable')) = 11.05
2025-07-23 20:30:15,754 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\utsaahi.ttf', name='Utsaah', style='italic', variant='normal', weight=400, stretch='normal', size='scalable')) = 11.05
2025-07-23 20:30:15,754 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\georgiaz.ttf', name='Georgia', style='italic', variant='normal', weight=700, stretch='normal', size='scalable')) = 11.335
2025-07-23 20:30:15,754 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\GillSansCondBoNova.ttf', name='Gill Sans Nova', style='normal', variant='normal', weight=700, stretch='condensed', size='scalable')) = 10.535
2025-07-23 20:30:15,754 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\lvnm.ttf', name='Levenim MT', style='normal', variant='normal', weight=400, stretch='normal', size='scalable')) = 10.05
2025-07-23 20:30:15,754 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\FRABKIT.TTF', name='Franklin Gothic Book', style='italic', variant='normal', weight=400, stretch='normal', size='scalable')) = 11.05
2025-07-23 20:30:15,754 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\PERBI___.TTF', name='Perpetua', style='italic', variant='normal', weight=700, stretch='normal', size='scalable')) = 11.335
2025-07-23 20:30:15,754 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\Vanib.ttf', name='Vani', style='normal', variant='normal', weight=700, stretch='normal', size='scalable')) = 10.335
2025-07-23 20:30:15,754 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\segoesc.ttf', name='Segoe Script', style='normal', variant='normal', weight=400, stretch='normal', size='scalable')) = 10.05
2025-07-23 20:30:15,754 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\TCMI____.TTF', name='Tw Cen MT', style='italic', variant='normal', weight=400, stretch='normal', size='scalable')) = 11.05
2025-07-23 20:30:15,754 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\BELLI.TTF', name='Bell MT', style='italic', variant='normal', weight=400, stretch='normal', size='scalable')) = 11.05
2025-07-23 20:30:15,754 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\javatext.ttf', name='Javanese Text', style='normal', variant='normal', weight=400, stretch='normal', size='scalable')) = 10.05
2025-07-23 20:30:15,754 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\HPSimplified.ttf', name='HP Simplified', style='normal', variant='normal', weight=700, stretch='normal', size='scalable')) = 10.335
2025-07-23 20:30:15,754 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\HPSimplified_Lt.ttf', name='HP Simplified', style='normal', variant='normal', weight=300, stretch='normal', size='scalable')) = 10.145
2025-07-23 20:30:15,754 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\FRADM.TTF', name='Franklin Gothic Demi', style='normal', variant='normal', weight=400, stretch='normal', size='scalable')) = 10.05
2025-07-23 20:30:15,754 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\TCCM____.TTF', name='Tw Cen MT Condensed', style='normal', variant='normal', weight=400, stretch='condensed', size='scalable')) = 10.25
2025-07-23 20:30:15,754 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\constan.ttf', name='Constantia', style='normal', variant='normal', weight=400, stretch='normal', size='scalable')) = 10.05
2025-07-23 20:30:15,754 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\HPSimplified_BdIt.ttf', name='HP Simplified', style='italic', variant='normal', weight=700, stretch='normal', size='scalable')) = 11.335
2025-07-23 20:30:15,754 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\Candaraz.ttf', name='Candara', style='italic', variant='normal', weight=700, stretch='normal', size='scalable')) = 11.335
2025-07-23 20:30:15,754 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\ArialNovaCond.ttf', name='Arial Nova', style='normal', variant='normal', weight=400, stretch='condensed', size='scalable')) = 10.25
2025-07-23 20:30:15,754 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\Sitka.ttc', name='Sitka Small', style='normal', variant='normal', weight=400, stretch='normal', size='scalable')) = 10.05
2025-07-23 20:30:15,754 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\consolab.ttf', name='Consolas', style='normal', variant='normal', weight=700, stretch='normal', size='scalable')) = 10.335
2025-07-23 20:30:15,754 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\NHaasGroteskTXPro-66MdIt.ttf', name='Neue Haas Grotesk Text Pro', style='italic', variant='normal', weight=500, stretch='normal', size='scalable')) = 11.145
2025-07-23 20:30:15,754 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\PERTILI.TTF', name='Perpetua Titling MT', style='normal', variant='normal', weight=300, stretch='normal', size='scalable')) = 10.145
2025-07-23 20:30:15,754 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\leelawad.ttf', name='Leelawadee', style='normal', variant='normal', weight=400, stretch='normal', size='scalable')) = 10.05
2025-07-23 20:30:15,754 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\VerdanaPro-CondLightItalic.ttf', name='Verdana Pro', style='italic', variant='normal', weight=300, stretch='condensed', size='scalable')) = 11.344999999999999
2025-07-23 20:30:15,754 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\VerdanaPro-CondItalic.ttf', name='Verdana Pro', style='italic', variant='normal', weight=400, stretch='condensed', size='scalable')) = 11.25
2025-07-23 20:30:15,754 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\calibrib.ttf', name='Calibri', style='normal', variant='normal', weight=700, stretch='normal', size='scalable')) = 10.335
2025-07-23 20:30:15,754 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\vrinda.ttf', name='Vrinda', style='normal', variant='normal', weight=400, stretch='normal', size='scalable')) = 10.05
2025-07-23 20:30:15,754 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\BOD_PSTC.TTF', name='Bodoni MT', style='normal', variant='normal', weight=300, stretch='normal', size='scalable')) = 10.145
2025-07-23 20:30:15,754 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\segoescb.ttf', name='Segoe Script', style='normal', variant='normal', weight=700, stretch='normal', size='scalable')) = 10.335
2025-07-23 20:30:15,754 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\trebucbd.ttf', name='Trebuchet MS', style='normal', variant='normal', weight=700, stretch='normal', size='scalable')) = 10.335
2025-07-23 20:30:15,754 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\KhmerUI.ttf', name='Khmer UI', style='normal', variant='normal', weight=400, stretch='normal', size='scalable')) = 10.05
2025-07-23 20:30:15,754 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\AGENCYB.TTF', name='Agency FB', style='normal', variant='normal', weight=700, stretch='normal', size='scalable')) = 10.335
2025-07-23 20:30:15,754 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\BOD_BLAR.TTF', name='Bodoni MT', style='normal', variant='normal', weight=900, stretch='normal', size='scalable')) = 10.525
2025-07-23 20:30:15,754 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\upclb.ttf', name='LilyUPC', style='normal', variant='normal', weight=700, stretch='normal', size='scalable')) = 10.335
2025-07-23 20:30:15,754 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\seguisym.ttf', name='Segoe UI Symbol', style='normal', variant='normal', weight=400, stretch='normal', size='scalable')) = 10.05
2025-07-23 20:30:15,754 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\couri.ttf', name='Courier New', style='italic', variant='normal', weight=400, stretch='normal', size='scalable')) = 11.05
2025-07-23 20:30:15,754 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\angsana.ttc', name='Angsana New', style='normal', variant='normal', weight=400, stretch='normal', size='scalable')) = 10.05
2025-07-23 20:30:15,754 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\lathab.ttf', name='Latha', style='normal', variant='normal', weight=700, stretch='normal', size='scalable')) = 10.335
2025-07-23 20:30:15,754 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\GillSansBoNova.ttf', name='Gill Sans Nova', style='normal', variant='normal', weight=700, stretch='normal', size='scalable')) = 10.335
2025-07-23 20:30:15,754 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\GeorgiaPro-Light.ttf', name='Georgia Pro', style='normal', variant='normal', weight=300, stretch='normal', size='scalable')) = 10.145
2025-07-23 20:30:15,754 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\ELEPHNTI.TTF', name='Elephant', style='italic', variant='normal', weight=400, stretch='normal', size='scalable')) = 11.05
2025-07-23 20:30:15,754 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\simhei.ttf', name='SimHei', style='normal', variant='normal', weight=400, stretch='normal', size='scalable')) = 10.05
2025-07-23 20:30:15,754 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\LeelUIsl.ttf', name='Leelawadee UI', style='normal', variant='normal', weight=350, stretch='normal', size='scalable')) = 10.0975
2025-07-23 20:30:15,754 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\CURLZ___.TTF', name='Curlz MT', style='normal', variant='normal', weight=400, stretch='normal', size='scalable')) = 10.05
2025-07-23 20:30:15,754 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\upcil.ttf', name='IrisUPC', style='normal', variant='normal', weight=400, stretch='normal', size='scalable')) = 10.05
2025-07-23 20:30:15,754 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\BRITANIC.TTF', name='Britannic Bold', style='normal', variant='normal', weight=400, stretch='normal', size='scalable')) = 10.05
2025-07-23 20:30:15,754 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\aparaj.ttf', name='Aparajita', style='normal', variant='normal', weight=400, stretch='normal', size='scalable')) = 10.05
2025-07-23 20:30:15,754 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\LeelawUI.ttf', name='Leelawadee UI', style='normal', variant='normal', weight=400, stretch='normal', size='scalable')) = 10.05
2025-07-23 20:30:15,754 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\upcei.ttf', name='EucrosiaUPC', style='italic', variant='normal', weight=400, stretch='normal', size='scalable')) = 11.05
2025-07-23 20:30:15,754 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\seguiemj.ttf', name='Segoe UI Emoji', style='normal', variant='normal', weight=400, stretch='normal', size='scalable')) = 10.05
2025-07-23 20:30:15,754 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\GeorgiaPro-CondBoldItalic.ttf', name='Georgia Pro', style='italic', variant='normal', weight=700, stretch='condensed', size='scalable')) = 11.535
2025-07-23 20:30:15,754 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\iskpotab.ttf', name='Iskoola Pota', style='normal', variant='normal', weight=700, stretch='normal', size='scalable')) = 10.335
2025-07-23 20:30:15,754 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\LeelaUIb.ttf', name='Leelawadee UI', style='normal', variant='normal', weight=700, stretch='normal', size='scalable')) = 10.335
2025-07-23 20:30:15,754 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\GARAIT.TTF', name='Garamond', style='italic', variant='normal', weight=400, stretch='normal', size='scalable')) = 11.05
2025-07-23 20:30:15,754 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\mriam.ttf', name='Miriam', style='normal', variant='normal', weight=400, stretch='normal', size='scalable')) = 10.05
2025-07-23 20:30:15,754 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\LHANDW.TTF', name='Lucida Handwriting', style='italic', variant='normal', weight=400, stretch='normal', size='scalable')) = 11.05
2025-07-23 20:30:15,754 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\timesbi.ttf', name='Times New Roman', style='italic', variant='normal', weight=700, stretch='normal', size='scalable')) = 11.335
2025-07-23 20:30:15,754 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\sylfaen.ttf', name='Sylfaen', style='normal', variant='normal', weight=400, stretch='normal', size='scalable')) = 10.05
2025-07-23 20:30:15,754 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\ROCCB___.TTF', name='Rockwell Condensed', style='normal', variant='normal', weight=700, stretch='condensed', size='scalable')) = 10.535
2025-07-23 20:30:15,754 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\RockwellNovaCond-Italic.ttf', name='Rockwell Nova', style='italic', variant='normal', weight=400, stretch='condensed', size='scalable')) = 11.25
2025-07-23 20:30:15,754 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\upcjb.ttf', name='JasmineUPC', style='normal', variant='normal', weight=700, stretch='normal', size='scalable')) = 10.335
2025-07-23 20:30:15,754 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\HPSimplified_Rg.ttf', name='HP Simplified', style='normal', variant='normal', weight=400, stretch='normal', size='scalable')) = 10.05
2025-07-23 20:30:15,754 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\timesi.ttf', name='Times New Roman', style='italic', variant='normal', weight=400, stretch='normal', size='scalable')) = 11.05
2025-07-23 20:30:15,754 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\gautamib.ttf', name='Gautami', style='normal', variant='normal', weight=700, stretch='normal', size='scalable')) = 10.335
2025-07-23 20:30:15,754 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\comicbd.ttf', name='Comic Sans MS', style='normal', variant='normal', weight=700, stretch='normal', size='scalable')) = 10.335
2025-07-23 20:30:15,754 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\yumin.ttf', name='Yu Mincho', style='normal', variant='normal', weight=400, stretch='normal', size='scalable')) = 10.05
2025-07-23 20:30:15,754 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\taileb.ttf', name='Microsoft Tai Le', style='normal', variant='normal', weight=700, stretch='normal', size='scalable')) = 10.335
2025-07-23 20:30:15,754 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\ENGR.TTF', name='Engravers MT', style='normal', variant='normal', weight=500, stretch='normal', size='scalable')) = 10.145
2025-07-23 20:30:15,754 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\lucon.ttf', name='Lucida Console', style='normal', variant='normal', weight=400, stretch='normal', size='scalable')) = 10.05
2025-07-23 20:30:15,754 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\GILBI___.TTF', name='Gill Sans MT', style='italic', variant='normal', weight=700, stretch='normal', size='scalable')) = 11.335
2025-07-23 20:30:15,754 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\msgothic.ttc', name='MS Gothic', style='normal', variant='normal', weight=400, stretch='normal', size='scalable')) = 10.05
2025-07-23 20:30:15,754 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\ArialNova-Italic.ttf', name='Arial Nova', style='italic', variant='normal', weight=400, stretch='normal', size='scalable')) = 11.05
2025-07-23 20:30:15,754 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\trebucit.ttf', name='Trebuchet MS', style='italic', variant='normal', weight=400, stretch='normal', size='scalable')) = 11.05
2025-07-23 20:30:15,754 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\segmdl2.ttf', name='Segoe MDL2 Assets', style='normal', variant='normal', weight=400, stretch='normal', size='scalable')) = 10.05
2025-07-23 20:30:15,754 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\Candarab.ttf', name='Candara', style='normal', variant='normal', weight=700, stretch='normal', size='scalable')) = 10.335
2025-07-23 20:30:15,754 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\kaiu.ttf', name='DFKai-SB', style='normal', variant='normal', weight=400, stretch='normal', size='scalable')) = 10.05
2025-07-23 20:30:15,754 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\GeorgiaPro-CondBlackItalic.ttf', name='Georgia Pro', style='italic', variant='normal', weight=900, stretch='condensed', size='scalable')) = 11.725
2025-07-23 20:30:15,754 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\malgunsl.ttf', name='Malgun Gothic', style='normal', variant='normal', weight=300, stretch='normal', size='scalable')) = 10.145
2025-07-23 20:30:15,754 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\RockwellNova-Light.ttf', name='Rockwell Nova', style='normal', variant='normal', weight=300, stretch='normal', size='scalable')) = 10.145
2025-07-23 20:30:15,754 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\utsaahbi.ttf', name='Utsaah', style='italic', variant='normal', weight=700, stretch='normal', size='scalable')) = 11.335
2025-07-23 20:30:15,754 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\moolbor.ttf', name='MoolBoran', style='normal', variant='normal', weight=400, stretch='normal', size='scalable')) = 10.05
2025-07-23 20:30:15,754 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\GeorgiaPro-BlackItalic.ttf', name='Georgia Pro', style='italic', variant='normal', weight=900, stretch='normal', size='scalable')) = 11.525
2025-07-23 20:30:15,770 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\palabi.ttf', name='Palatino Linotype', style='italic', variant='normal', weight=700, stretch='normal', size='scalable')) = 11.335
2025-07-23 20:30:15,770 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\pala.ttf', name='Palatino Linotype', style='normal', variant='normal', weight=400, stretch='normal', size='scalable')) = 10.05
2025-07-23 20:30:15,770 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\NHaasGroteskTXPro-56It.ttf', name='Neue Haas Grotesk Text Pro', style='italic', variant='normal', weight=400, stretch='normal', size='scalable')) = 11.05
2025-07-23 20:30:15,770 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\HATTEN.TTF', name='Haettenschweiler', style='normal', variant='normal', weight=400, stretch='normal', size='scalable')) = 10.05
2025-07-23 20:30:15,770 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\BOD_CB.TTF', name='Bodoni MT', style='normal', variant='normal', weight=700, stretch='condensed', size='scalable')) = 10.535
2025-07-23 20:30:15,770 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\CALISTI.TTF', name='Calisto MT', style='italic', variant='normal', weight=400, stretch='normal', size='scalable')) = 11.05
2025-07-23 20:30:15,770 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\NHaasGroteskTXPro-55Rg.ttf', name='Neue Haas Grotesk Text Pro', style='normal', variant='normal', weight=400, stretch='normal', size='scalable')) = 10.05
2025-07-23 20:30:15,770 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\kokilab.ttf', name='Kokila', style='normal', variant='normal', weight=700, stretch='normal', size='scalable')) = 10.335
2025-07-23 20:30:15,770 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\BKANT.TTF', name='Book Antiqua', style='normal', variant='normal', weight=400, stretch='normal', size='scalable')) = 10.05
2025-07-23 20:30:15,770 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\ROCKBI.TTF', name='Rockwell', style='italic', variant='normal', weight=700, stretch='normal', size='scalable')) = 11.335
2025-07-23 20:30:15,770 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\BOD_CBI.TTF', name='Bodoni MT', style='italic', variant='normal', weight=700, stretch='condensed', size='scalable')) = 11.535
2025-07-23 20:30:15,770 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\Nirmala.ttf', name='Nirmala UI', style='normal', variant='normal', weight=400, stretch='normal', size='scalable')) = 10.05
2025-07-23 20:30:15,770 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\malgunbd.ttf', name='Malgun Gothic', style='normal', variant='normal', weight=700, stretch='normal', size='scalable')) = 10.335
2025-07-23 20:30:15,770 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\SitkaI.ttc', name='Sitka Small', style='italic', variant='normal', weight=400, stretch='normal', size='scalable')) = 11.05
2025-07-23 20:30:15,770 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\ERASBD.TTF', name='Eras Bold ITC', style='normal', variant='normal', weight=400, stretch='normal', size='scalable')) = 10.05
2025-07-23 20:30:15,770 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\seguisb.ttf', name='Segoe UI', style='normal', variant='normal', weight=600, stretch='normal', size='scalable')) = 10.24
2025-07-23 20:30:15,770 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\simpfxo.ttf', name='Simplified Arabic Fixed', style='normal', variant='normal', weight=400, stretch='normal', size='scalable')) = 10.05
2025-07-23 20:30:15,770 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\SCRIPTBL.TTF', name='Script MT Bold', style='normal', variant='normal', weight=700, stretch='normal', size='scalable')) = 10.335
2025-07-23 20:30:15,770 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\Deng.ttf', name='DengXian', style='normal', variant='normal', weight=400, stretch='normal', size='scalable')) = 10.05
2025-07-23 20:30:15,770 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\Vani.ttf', name='Vani', style='normal', variant='normal', weight=400, stretch='normal', size='scalable')) = 10.05
2025-07-23 20:30:15,770 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\comic.ttf', name='Comic Sans MS', style='normal', variant='normal', weight=400, stretch='normal', size='scalable')) = 10.05
2025-07-23 20:30:15,770 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\msyh.ttc', name='Microsoft YaHei', style='normal', variant='normal', weight=400, stretch='normal', size='scalable')) = 10.05
2025-07-23 20:30:15,770 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\BRUSHSCI.TTF', name='Brush Script MT', style='italic', variant='normal', weight=400, stretch='normal', size='scalable')) = 11.05
2025-07-23 20:30:15,770 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\GOTHIC.TTF', name='Century Gothic', style='normal', variant='normal', weight=400, stretch='normal', size='scalable')) = 10.05
2025-07-23 20:30:15,770 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\GOTHICB.TTF', name='Century Gothic', style='normal', variant='normal', weight=700, stretch='normal', size='scalable')) = 10.335
2025-07-23 20:30:15,770 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\msuighub.ttf', name='Microsoft Uighur', style='normal', variant='normal', weight=700, stretch='normal', size='scalable')) = 10.335
2025-07-23 20:30:15,770 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\kokila.ttf', name='Kokila', style='normal', variant='normal', weight=400, stretch='normal', size='scalable')) = 10.05
2025-07-23 20:30:15,770 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\VerdanaPro-CondSemiBoldItalic.ttf', name='Verdana Pro', style='italic', variant='normal', weight=600, stretch='condensed', size='scalable')) = 11.44
2025-07-23 20:30:15,770 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\browalia.ttc', name='Browallia New', style='normal', variant='normal', weight=400, stretch='normal', size='scalable')) = 10.05
2025-07-23 20:30:15,770 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\SCHLBKI.TTF', name='Century Schoolbook', style='italic', variant='normal', weight=400, stretch='normal', size='scalable')) = 11.05
2025-07-23 20:30:15,770 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\PERB____.TTF', name='Perpetua', style='normal', variant='normal', weight=700, stretch='normal', size='scalable')) = 10.335
2025-07-23 20:30:15,770 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\RockwellNovaCond-Bold.ttf', name='Rockwell Nova', style='normal', variant='normal', weight=700, stretch='condensed', size='scalable')) = 10.535
2025-07-23 20:30:15,770 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\ELEPHNT.TTF', name='Elephant', style='normal', variant='normal', weight=400, stretch='normal', size='scalable')) = 10.05
2025-07-23 20:30:15,770 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\NIAGENG.TTF', name='Niagara Engraved', style='normal', variant='normal', weight=400, stretch='normal', size='scalable')) = 10.05
2025-07-23 20:30:15,770 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\courbi.ttf', name='Courier New', style='italic', variant='normal', weight=700, stretch='normal', size='scalable')) = 11.335
2025-07-23 20:30:15,770 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\impact.ttf', name='Impact', style='normal', variant='normal', weight=400, stretch='normal', size='scalable')) = 10.05
2025-07-23 20:30:15,770 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\trebucbi.ttf', name='Trebuchet MS', style='italic', variant='normal', weight=700, stretch='normal', size='scalable')) = 11.335
2025-07-23 20:30:15,770 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\upcdb.ttf', name='DilleniaUPC', style='normal', variant='normal', weight=700, stretch='normal', size='scalable')) = 10.335
2025-07-23 20:30:15,770 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\LTYPEB.TTF', name='Lucida Sans Typewriter', style='normal', variant='normal', weight=600, stretch='normal', size='scalable')) = 10.24
2025-07-23 20:30:15,770 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\NHaasGroteskTXPro-75Bd.ttf', name='Neue Haas Grotesk Text Pro', style='normal', variant='normal', weight=700, stretch='normal', size='scalable')) = 10.335
2025-07-23 20:30:15,770 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\BRADHITC.TTF', name='Bradley Hand ITC', style='normal', variant='normal', weight=400, stretch='normal', size='scalable')) = 10.05
2025-07-23 20:30:15,770 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\GeorgiaPro-BoldItalic.ttf', name='Georgia Pro', style='italic', variant='normal', weight=700, stretch='normal', size='scalable')) = 11.335
2025-07-23 20:30:15,770 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\palab.ttf', name='Palatino Linotype', style='normal', variant='normal', weight=700, stretch='normal', size='scalable')) = 10.335
2025-07-23 20:30:15,770 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\malgun.ttf', name='Malgun Gothic', style='normal', variant='normal', weight=400, stretch='normal', size='scalable')) = 10.05
2025-07-23 20:30:15,770 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\NirmalaS.ttf', name='Nirmala UI', style='normal', variant='normal', weight=350, stretch='normal', size='scalable')) = 10.0975
2025-07-23 20:30:15,770 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\upcii.ttf', name='IrisUPC', style='italic', variant='normal', weight=400, stretch='normal', size='scalable')) = 11.05
2025-07-23 20:30:15,770 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\calibriz.ttf', name='Calibri', style='italic', variant='normal', weight=700, stretch='normal', size='scalable')) = 11.335
2025-07-23 20:30:15,770 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\tahomabd.ttf', name='Tahoma', style='normal', variant='normal', weight=700, stretch='normal', size='scalable')) = 10.335
2025-07-23 20:30:15,770 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\msmincho.ttc', name='MS Mincho', style='normal', variant='normal', weight=400, stretch='normal', size='scalable')) = 10.05
2025-07-23 20:30:15,770 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\segoeuii.ttf', name='Segoe UI', style='italic', variant='normal', weight=400, stretch='normal', size='scalable')) = 11.05
2025-07-23 20:30:15,770 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\GARA.TTF', name='Garamond', style='normal', variant='normal', weight=400, stretch='normal', size='scalable')) = 10.05
2025-07-23 20:30:15,770 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\STENCIL.TTF', name='Stencil', style='normal', variant='normal', weight=400, stretch='normal', size='scalable')) = 10.05
2025-07-23 20:30:15,770 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\ebrima.ttf', name='Ebrima', style='normal', variant='normal', weight=400, stretch='normal', size='scalable')) = 10.05
2025-07-23 20:30:15,770 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\tahoma.ttf', name='Tahoma', style='normal', variant='normal', weight=400, stretch='normal', size='scalable')) = 10.05
2025-07-23 20:30:15,770 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\CASTELAR.TTF', name='Castellar', style='normal', variant='normal', weight=400, stretch='normal', size='scalable')) = 10.05
2025-07-23 20:30:15,770 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\CENTURY.TTF', name='Century', style='normal', variant='normal', weight=400, stretch='normal', size='scalable')) = 10.05
2025-07-23 20:30:15,770 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\segoeuiz.ttf', name='Segoe UI', style='italic', variant='normal', weight=700, stretch='normal', size='scalable')) = 11.335
2025-07-23 20:30:15,770 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\GeorgiaPro-SemiBoldItalic.ttf', name='Georgia Pro', style='italic', variant='normal', weight=600, stretch='normal', size='scalable')) = 11.24
2025-07-23 20:30:15,770 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\GILLUBCD.TTF', name='Gill Sans Ultra Bold Condensed', style='normal', variant='normal', weight=400, stretch='condensed', size='scalable')) = 10.25
2025-07-23 20:30:15,770 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\upcfb.ttf', name='FreesiaUPC', style='normal', variant='normal', weight=700, stretch='normal', size='scalable')) = 10.335
2025-07-23 20:30:15,770 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\GillSansCondLightItNova.ttf', name='Gill Sans Nova', style='italic', variant='normal', weight=300, stretch='condensed', size='scalable')) = 11.344999999999999
2025-07-23 20:30:15,770 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\VerdanaPro-Black.ttf', name='Verdana Pro', style='normal', variant='normal', weight=900, stretch='normal', size='scalable')) = 10.525
2025-07-23 20:30:15,770 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\arialnbi.ttf', name='Arial', style='italic', variant='normal', weight=700, stretch='condensed', size='scalable')) = 7.8986363636363635
2025-07-23 20:30:15,770 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\upcibi.ttf', name='IrisUPC', style='italic', variant='normal', weight=700, stretch='normal', size='scalable')) = 11.335
2025-07-23 20:30:15,770 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\BOOKOS.TTF', name='Bookman Old Style', style='normal', variant='normal', weight=300, stretch='normal', size='scalable')) = 10.145
2025-07-23 20:30:15,770 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\GillSansCondLightNova.ttf', name='Gill Sans Nova', style='normal', variant='normal', weight=300, stretch='condensed', size='scalable')) = 10.344999999999999
2025-07-23 20:30:15,770 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\BELL.TTF', name='Bell MT', style='normal', variant='normal', weight=400, stretch='normal', size='scalable')) = 10.05
2025-07-23 20:30:15,770 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\OUTLOOK.TTF', name='MS Outlook', style='normal', variant='normal', weight=400, stretch='normal', size='scalable')) = 10.05
2025-07-23 20:30:15,770 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\ITCKRIST.TTF', name='Kristen ITC', style='normal', variant='normal', weight=400, stretch='normal', size='scalable')) = 10.05
2025-07-23 20:30:15,770 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\simkai.ttf', name='KaiTi', style='normal', variant='normal', weight=400, stretch='normal', size='scalable')) = 10.05
2025-07-23 20:30:15,770 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\aldhabi.ttf', name='Aldhabi', style='normal', variant='normal', weight=400, stretch='normal', size='scalable')) = 10.05
2025-07-23 20:30:15,770 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\FREESCPT.TTF', name='Freestyle Script', style='normal', variant='normal', weight=400, stretch='normal', size='scalable')) = 10.05
2025-07-23 20:30:15,770 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\PERI____.TTF', name='Perpetua', style='italic', variant='normal', weight=400, stretch='normal', size='scalable')) = 11.05
2025-07-23 20:30:15,770 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\arialn.ttf', name='Arial', style='normal', variant='normal', weight=400, stretch='condensed', size='scalable')) = 6.613636363636363
2025-07-23 20:30:15,770 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\YuGothM.ttc', name='Yu Gothic', style='normal', variant='normal', weight=500, stretch='normal', size='scalable')) = 10.145
2025-07-23 20:30:15,770 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\batang.ttc', name='Batang', style='normal', variant='normal', weight=400, stretch='normal', size='scalable')) = 10.05
2025-07-23 20:30:15,770 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\meiryo.ttc', name='Meiryo', style='normal', variant='normal', weight=400, stretch='normal', size='scalable')) = 10.05
2025-07-23 20:30:15,770 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\shrutib.ttf', name='Shruti', style='normal', variant='normal', weight=700, stretch='normal', size='scalable')) = 10.335
2025-07-23 20:30:15,770 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\kokilabi.ttf', name='Kokila', style='italic', variant='normal', weight=700, stretch='normal', size='scalable')) = 11.335
2025-07-23 20:30:15,770 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\GOUDYSTO.TTF', name='Goudy Stout', style='normal', variant='normal', weight=400, stretch='normal', size='scalable')) = 10.05
2025-07-23 20:30:15,770 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\seguihis.ttf', name='Segoe UI Historic', style='normal', variant='normal', weight=400, stretch='normal', size='scalable')) = 10.05
2025-07-23 20:30:15,770 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\majallab.ttf', name='Sakkal Majalla', style='normal', variant='normal', weight=700, stretch='normal', size='scalable')) = 10.335
2025-07-23 20:30:15,770 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\GILC____.TTF', name='Gill Sans MT Condensed', style='normal', variant='normal', weight=400, stretch='condensed', size='scalable')) = 10.25
2025-07-23 20:30:15,770 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\corbelli.ttf', name='Corbel', style='italic', variant='normal', weight=300, stretch='normal', size='scalable')) = 11.145
2025-07-23 20:30:15,770 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\seguisbi.ttf', name='Segoe UI', style='italic', variant='normal', weight=600, stretch='normal', size='scalable')) = 11.24
2025-07-23 20:30:15,770 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\BOD_CI.TTF', name='Bodoni MT', style='italic', variant='normal', weight=400, stretch='condensed', size='scalable')) = 11.25
2025-07-23 20:30:15,770 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\VerdanaPro-CondSemiBold.ttf', name='Verdana Pro', style='normal', variant='normal', weight=600, stretch='condensed', size='scalable')) = 10.44
2025-07-23 20:30:15,770 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\YuGothR.ttc', name='Yu Gothic', style='normal', variant='normal', weight=400, stretch='normal', size='scalable')) = 10.05
2025-07-23 20:30:15,770 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\ONYX.TTF', name='Onyx', style='normal', variant='normal', weight=400, stretch='normal', size='scalable')) = 10.05
2025-07-23 20:30:15,770 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\ArialNova-LightItalic.ttf', name='Arial Nova', style='italic', variant='normal', weight=300, stretch='normal', size='scalable')) = 11.145
2025-07-23 20:30:15,770 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\phagspab.ttf', name='Microsoft PhagsPa', style='normal', variant='normal', weight=700, stretch='normal', size='scalable')) = 10.335
2025-07-23 20:30:15,770 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\BOD_R.TTF', name='Bodoni MT', style='normal', variant='normal', weight=400, stretch='normal', size='scalable')) = 10.05
2025-07-23 20:30:15,770 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\REFSPCL.TTF', name='MS Reference Specialty', style='normal', variant='normal', weight=400, stretch='normal', size='scalable')) = 10.05
2025-07-23 20:30:15,770 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\gulim.ttc', name='Gulim', style='normal', variant='normal', weight=400, stretch='normal', size='scalable')) = 10.05
2025-07-23 20:30:15,770 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\ANTQUAB.TTF', name='Book Antiqua', style='normal', variant='normal', weight=700, stretch='normal', size='scalable')) = 10.335
2025-07-23 20:30:15,770 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\gisha.ttf', name='Gisha', style='normal', variant='normal', weight=400, stretch='normal', size='scalable')) = 10.05
2025-07-23 20:30:15,770 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\LBRITE.TTF', name='Lucida Bright', style='normal', variant='normal', weight=400, stretch='normal', size='scalable')) = 10.05
2025-07-23 20:30:15,801 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\calibrii.ttf', name='Calibri', style='italic', variant='normal', weight=400, stretch='normal', size='scalable')) = 11.05
2025-07-23 20:30:15,801 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\LTYPEO.TTF', name='Lucida Sans Typewriter', style='oblique', variant='normal', weight=400, stretch='normal', size='scalable')) = 11.05
2025-07-23 20:30:15,801 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\webdings.ttf', name='Webdings', style='normal', variant='normal', weight=400, stretch='normal', size='scalable')) = 10.05
2025-07-23 20:30:15,801 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\WINGDNG2.TTF', name='Wingdings 2', style='normal', variant='normal', weight=400, stretch='normal', size='scalable')) = 10.05
2025-07-23 20:30:15,801 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\corbel.ttf', name='Corbel', style='normal', variant='normal', weight=400, stretch='normal', size='scalable')) = 10.05
2025-07-23 20:30:15,801 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\MAIAN.TTF', name='Maiandra GD', style='normal', variant='normal', weight=400, stretch='normal', size='scalable')) = 10.05
2025-07-23 20:30:15,801 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\YuGothL.ttc', name='Yu Gothic', style='normal', variant='normal', weight=300, stretch='normal', size='scalable')) = 10.145
2025-07-23 20:30:15,801 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\ERASLGHT.TTF', name='Eras Light ITC', style='normal', variant='normal', weight=400, stretch='normal', size='scalable')) = 10.05
2025-07-23 20:30:15,801 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\segoeuisl.ttf', name='Segoe UI', style='normal', variant='normal', weight=350, stretch='normal', size='scalable')) = 10.0975
2025-07-23 20:30:15,801 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\RockwellNova-ExtraBold.ttf', name='Rockwell Nova', style='normal', variant='normal', weight=800, stretch='normal', size='scalable')) = 10.43
2025-07-23 20:30:15,801 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\RAGE.TTF', name='Rage Italic', style='italic', variant='normal', weight=400, stretch='normal', size='scalable')) = 11.05
2025-07-23 20:30:15,801 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\GillSansLightNova.ttf', name='Gill Sans Nova', style='normal', variant='normal', weight=300, stretch='normal', size='scalable')) = 10.145
2025-07-23 20:30:15,801 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\RockwellNova-BoldItalic.ttf', name='Rockwell Nova', style='italic', variant='normal', weight=700, stretch='normal', size='scalable')) = 11.335
2025-07-23 20:30:15,801 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\calibril.ttf', name='Calibri', style='normal', variant='normal', weight=300, stretch='normal', size='scalable')) = 10.145
2025-07-23 20:30:15,801 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\NHaasGroteskTXPro-65Md.ttf', name='Neue Haas Grotesk Text Pro', style='normal', variant='normal', weight=500, stretch='normal', size='scalable')) = 10.145
2025-07-23 20:30:15,801 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\OLDENGL.TTF', name='Old English Text MT', style='normal', variant='normal', weight=400, stretch='normal', size='scalable')) = 10.05
2025-07-23 20:30:15,801 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\BIZ-UDGothicR.ttc', name='BIZ UDGothic', style='normal', variant='normal', weight=400, stretch='normal', size='scalable')) = 10.05
2025-07-23 20:30:15,801 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\trado.ttf', name='Traditional Arabic', style='normal', variant='normal', weight=400, stretch='normal', size='scalable')) = 10.05
2025-07-23 20:30:15,801 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\upcdl.ttf', name='DilleniaUPC', style='normal', variant='normal', weight=400, stretch='normal', size='scalable')) = 10.05
2025-07-23 20:30:15,801 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\GeorgiaPro-CondBold.ttf', name='Georgia Pro', style='normal', variant='normal', weight=700, stretch='condensed', size='scalable')) = 10.535
2025-07-23 20:30:15,801 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\RockwellNova-ExtraBoldItalic.ttf', name='Rockwell Nova', style='italic', variant='normal', weight=800, stretch='normal', size='scalable')) = 11.43
2025-07-23 20:30:15,801 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\SCHLBKBI.TTF', name='Century Schoolbook', style='italic', variant='normal', weight=700, stretch='normal', size='scalable')) = 11.335
2025-07-23 20:30:15,801 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\shruti.ttf', name='Shruti', style='normal', variant='normal', weight=400, stretch='normal', size='scalable')) = 10.05
2025-07-23 20:30:15,801 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\RockwellNova.ttf', name='Rockwell Nova', style='normal', variant='normal', weight=400, stretch='normal', size='scalable')) = 10.05
2025-07-23 20:30:15,801 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\ArialNovaCond-Bold.ttf', name='Arial Nova', style='normal', variant='normal', weight=700, stretch='condensed', size='scalable')) = 10.535
2025-07-23 20:30:15,801 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\Sanskr.ttf', name='Sanskrit Text', style='normal', variant='normal', weight=400, stretch='normal', size='scalable')) = 10.05
2025-07-23 20:30:15,801 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\upcfbi.ttf', name='FreesiaUPC', style='italic', variant='normal', weight=700, stretch='normal', size='scalable')) = 11.335
2025-07-23 20:30:15,801 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\gadugi.ttf', name='Gadugi', style='normal', variant='normal', weight=400, stretch='normal', size='scalable')) = 10.05
2025-07-23 20:30:15,801 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\times.ttf', name='Times New Roman', style='normal', variant='normal', weight=400, stretch='normal', size='scalable')) = 10.05
2025-07-23 20:30:15,801 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\RockwellNova-Bold.ttf', name='Rockwell Nova', style='normal', variant='normal', weight=700, stretch='normal', size='scalable')) = 10.335
2025-07-23 20:30:15,801 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\VINERITC.TTF', name='Viner Hand ITC', style='normal', variant='normal', weight=400, stretch='normal', size='scalable')) = 10.05
2025-07-23 20:30:15,801 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\gautami.ttf', name='Gautami', style='normal', variant='normal', weight=400, stretch='normal', size='scalable')) = 10.05
2025-07-23 20:30:15,801 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\raavib.ttf', name='Raavi', style='normal', variant='normal', weight=700, stretch='normal', size='scalable')) = 10.335
2025-07-23 20:30:15,801 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\arialbi.ttf', name='Arial', style='italic', variant='normal', weight=700, stretch='normal', size='scalable')) = 7.698636363636363
2025-07-23 20:30:15,801 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\SHOWG.TTF', name='Showcard Gothic', style='normal', variant='normal', weight=400, stretch='normal', size='scalable')) = 10.05
2025-07-23 20:30:15,801 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\GeorgiaPro-CondSemiBold.ttf', name='Georgia Pro', style='normal', variant='normal', weight=600, stretch='condensed', size='scalable')) = 10.44
2025-07-23 20:30:15,801 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\GeorgiaPro-Regular.ttf', name='Georgia Pro', style='normal', variant='normal', weight=400, stretch='normal', size='scalable')) = 10.05
2025-07-23 20:30:15,801 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\NHaasGroteskTXPro-76BdIt.ttf', name='Neue Haas Grotesk Text Pro', style='italic', variant='normal', weight=700, stretch='normal', size='scalable')) = 11.335
2025-07-23 20:30:15,801 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\msuighur.ttf', name='Microsoft Uighur', style='normal', variant='normal', weight=400, stretch='normal', size='scalable')) = 10.05
2025-07-23 20:30:15,801 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\LTYPEBO.TTF', name='Lucida Sans Typewriter', style='oblique', variant='normal', weight=600, stretch='normal', size='scalable')) = 11.24
2025-07-23 20:30:15,801 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\LCALLIG.TTF', name='Lucida Calligraphy', style='italic', variant='normal', weight=400, stretch='normal', size='scalable')) = 11.05
2025-07-23 20:30:15,801 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\LBRITEI.TTF', name='Lucida Bright', style='italic', variant='normal', weight=400, stretch='normal', size='scalable')) = 11.05
2025-07-23 20:30:15,801 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\mingliu.ttc', name='MingLiU', style='normal', variant='normal', weight=400, stretch='normal', size='scalable')) = 10.05
2025-07-23 20:30:15,801 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\BOOKOSBI.TTF', name='Bookman Old Style', style='italic', variant='normal', weight=600, stretch='normal', size='scalable')) = 11.24
2025-07-23 20:30:15,801 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\ERASDEMI.TTF', name='Eras Demi ITC', style='normal', variant='normal', weight=400, stretch='normal', size='scalable')) = 10.05
2025-07-23 20:30:15,801 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\constanz.ttf', name='Constantia', style='italic', variant='normal', weight=700, stretch='normal', size='scalable')) = 11.335
2025-07-23 20:30:15,801 - DEBUG - findfont: Matching sans\-serif:style=normal:variant=normal:weight=normal:stretch=normal:size=10.0 to DejaVu Sans ('C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\site-packages\\matplotlib\\mpl-data\\fonts\\ttf\\DejaVuSans.ttf') with score of 0.050000.
2025-07-23 20:30:15,832 - INFO - Using categorical units to plot a list of strings that are all parsable as floats or dates. If these strings should be plotted as numbers, cast to the appropriate data type before plotting.
2025-07-23 20:30:15,832 - INFO - Using categorical units to plot a list of strings that are all parsable as floats or dates. If these strings should be plotted as numbers, cast to the appropriate data type before plotting.
2025-07-23 20:30:15,973 - DEBUG - findfont: Matching Arial:style=normal:variant=normal:weight=normal:stretch=normal:size=10.0.
2025-07-23 20:30:15,973 - DEBUG - findfont: score(FontEntry(fname='C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\site-packages\\matplotlib\\mpl-data\\fonts\\ttf\\cmss10.ttf', name='cmss10', style='normal', variant='normal', weight=400, stretch='normal', size='scalable')) = 10.05
2025-07-23 20:30:15,973 - DEBUG - findfont: score(FontEntry(fname='C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\site-packages\\matplotlib\\mpl-data\\fonts\\ttf\\DejaVuSansMono.ttf', name='DejaVu Sans Mono', style='normal', variant='normal', weight=400, stretch='normal', size='scalable')) = 10.05
2025-07-23 20:30:15,973 - DEBUG - findfont: score(FontEntry(fname='C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\site-packages\\matplotlib\\mpl-data\\fonts\\ttf\\STIXGeneral.ttf', name='STIXGeneral', style='normal', variant='normal', weight=400, stretch='normal', size='scalable')) = 10.05
2025-07-23 20:30:15,973 - DEBUG - findfont: score(FontEntry(fname='C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\site-packages\\matplotlib\\mpl-data\\fonts\\ttf\\STIXGeneralItalic.ttf', name='STIXGeneral', style='italic', variant='normal', weight=400, stretch='normal', size='scalable')) = 11.05
2025-07-23 20:30:15,973 - DEBUG - findfont: score(FontEntry(fname='C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\site-packages\\matplotlib\\mpl-data\\fonts\\ttf\\STIXNonUniBol.ttf', name='STIXNonUnicode', style='normal', variant='normal', weight=700, stretch='normal', size='scalable')) = 10.335
2025-07-23 20:30:15,973 - DEBUG - findfont: score(FontEntry(fname='C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\site-packages\\matplotlib\\mpl-data\\fonts\\ttf\\cmsy10.ttf', name='cmsy10', style='normal', variant='normal', weight=400, stretch='normal', size='scalable')) = 10.05
2025-07-23 20:30:15,973 - DEBUG - findfont: score(FontEntry(fname='C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\site-packages\\matplotlib\\mpl-data\\fonts\\ttf\\DejaVuSansMono-Oblique.ttf', name='DejaVu Sans Mono', style='oblique', variant='normal', weight=400, stretch='normal', size='scalable')) = 11.05
2025-07-23 20:30:15,973 - DEBUG - findfont: score(FontEntry(fname='C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\site-packages\\matplotlib\\mpl-data\\fonts\\ttf\\DejaVuSansMono-Bold.ttf', name='DejaVu Sans Mono', style='normal', variant='normal', weight=700, stretch='normal', size='scalable')) = 10.335
2025-07-23 20:30:15,973 - DEBUG - findfont: score(FontEntry(fname='C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\site-packages\\matplotlib\\mpl-data\\fonts\\ttf\\DejaVuSerif-Italic.ttf', name='DejaVu Serif', style='italic', variant='normal', weight=400, stretch='normal', size='scalable')) = 11.05
2025-07-23 20:30:15,973 - DEBUG - findfont: score(FontEntry(fname='C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\site-packages\\matplotlib\\mpl-data\\fonts\\ttf\\cmtt10.ttf', name='cmtt10', style='normal', variant='normal', weight=400, stretch='normal', size='scalable')) = 10.05
2025-07-23 20:30:15,973 - DEBUG - findfont: score(FontEntry(fname='C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\site-packages\\matplotlib\\mpl-data\\fonts\\ttf\\STIXSizThreeSymReg.ttf', name='STIXSizeThreeSym', style='normal', variant='normal', weight=400, stretch='normal', size='scalable')) = 10.05
2025-07-23 20:30:15,973 - DEBUG - findfont: score(FontEntry(fname='C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\site-packages\\matplotlib\\mpl-data\\fonts\\ttf\\cmex10.ttf', name='cmex10', style='normal', variant='normal', weight=400, stretch='normal', size='scalable')) = 10.05
2025-07-23 20:30:15,973 - DEBUG - findfont: score(FontEntry(fname='C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\site-packages\\matplotlib\\mpl-data\\fonts\\ttf\\STIXSizFourSymBol.ttf', name='STIXSizeFourSym', style='normal', variant='normal', weight=700, stretch='normal', size='scalable')) = 10.335
2025-07-23 20:30:15,973 - DEBUG - findfont: score(FontEntry(fname='C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\site-packages\\matplotlib\\mpl-data\\fonts\\ttf\\STIXSizFourSymReg.ttf', name='STIXSizeFourSym', style='normal', variant='normal', weight=400, stretch='normal', size='scalable')) = 10.05
2025-07-23 20:30:15,973 - DEBUG - findfont: score(FontEntry(fname='C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\site-packages\\matplotlib\\mpl-data\\fonts\\ttf\\STIXSizTwoSymBol.ttf', name='STIXSizeTwoSym', style='normal', variant='normal', weight=700, stretch='normal', size='scalable')) = 10.335
2025-07-23 20:30:15,973 - DEBUG - findfont: score(FontEntry(fname='C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\site-packages\\matplotlib\\mpl-data\\fonts\\ttf\\STIXSizTwoSymReg.ttf', name='STIXSizeTwoSym', style='normal', variant='normal', weight=400, stretch='normal', size='scalable')) = 10.05
2025-07-23 20:30:15,973 - DEBUG - findfont: score(FontEntry(fname='C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\site-packages\\matplotlib\\mpl-data\\fonts\\ttf\\STIXSizFiveSymReg.ttf', name='STIXSizeFiveSym', style='normal', variant='normal', weight=400, stretch='normal', size='scalable')) = 10.05
2025-07-23 20:30:15,973 - DEBUG - findfont: score(FontEntry(fname='C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\site-packages\\matplotlib\\mpl-data\\fonts\\ttf\\cmmi10.ttf', name='cmmi10', style='normal', variant='normal', weight=400, stretch='normal', size='scalable')) = 10.05
2025-07-23 20:30:15,973 - DEBUG - findfont: score(FontEntry(fname='C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\site-packages\\matplotlib\\mpl-data\\fonts\\ttf\\STIXSizOneSymReg.ttf', name='STIXSizeOneSym', style='normal', variant='normal', weight=400, stretch='normal', size='scalable')) = 10.05
2025-07-23 20:30:15,973 - DEBUG - findfont: score(FontEntry(fname='C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\site-packages\\matplotlib\\mpl-data\\fonts\\ttf\\DejaVuSansMono-BoldOblique.ttf', name='DejaVu Sans Mono', style='oblique', variant='normal', weight=700, stretch='normal', size='scalable')) = 11.335
2025-07-23 20:30:15,973 - DEBUG - findfont: score(FontEntry(fname='C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\site-packages\\matplotlib\\mpl-data\\fonts\\ttf\\STIXNonUni.ttf', name='STIXNonUnicode', style='normal', variant='normal', weight=400, stretch='normal', size='scalable')) = 10.05
2025-07-23 20:30:15,973 - DEBUG - findfont: score(FontEntry(fname='C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\site-packages\\matplotlib\\mpl-data\\fonts\\ttf\\DejaVuSerif-BoldItalic.ttf', name='DejaVu Serif', style='italic', variant='normal', weight=700, stretch='normal', size='scalable')) = 11.335
2025-07-23 20:30:15,973 - DEBUG - findfont: score(FontEntry(fname='C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\site-packages\\matplotlib\\mpl-data\\fonts\\ttf\\STIXGeneralBol.ttf', name='STIXGeneral', style='normal', variant='normal', weight=700, stretch='normal', size='scalable')) = 10.335
2025-07-23 20:30:15,973 - DEBUG - findfont: score(FontEntry(fname='C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\site-packages\\matplotlib\\mpl-data\\fonts\\ttf\\DejaVuSerif.ttf', name='DejaVu Serif', style='normal', variant='normal', weight=400, stretch='normal', size='scalable')) = 10.05
2025-07-23 20:30:15,973 - DEBUG - findfont: score(FontEntry(fname='C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\site-packages\\matplotlib\\mpl-data\\fonts\\ttf\\STIXNonUniBolIta.ttf', name='STIXNonUnicode', style='italic', variant='normal', weight=700, stretch='normal', size='scalable')) = 11.335
2025-07-23 20:30:15,973 - DEBUG - findfont: score(FontEntry(fname='C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\site-packages\\matplotlib\\mpl-data\\fonts\\ttf\\STIXSizOneSymBol.ttf', name='STIXSizeOneSym', style='normal', variant='normal', weight=700, stretch='normal', size='scalable')) = 10.335
2025-07-23 20:30:15,973 - DEBUG - findfont: score(FontEntry(fname='C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\site-packages\\matplotlib\\mpl-data\\fonts\\ttf\\DejaVuSerif-Bold.ttf', name='DejaVu Serif', style='normal', variant='normal', weight=700, stretch='normal', size='scalable')) = 10.335
2025-07-23 20:30:15,973 - DEBUG - findfont: score(FontEntry(fname='C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\site-packages\\matplotlib\\mpl-data\\fonts\\ttf\\DejaVuSans-Oblique.ttf', name='DejaVu Sans', style='oblique', variant='normal', weight=400, stretch='normal', size='scalable')) = 11.05
2025-07-23 20:30:15,973 - DEBUG - findfont: score(FontEntry(fname='C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\site-packages\\matplotlib\\mpl-data\\fonts\\ttf\\DejaVuSans-Bold.ttf', name='DejaVu Sans', style='normal', variant='normal', weight=700, stretch='normal', size='scalable')) = 10.335
2025-07-23 20:30:15,973 - DEBUG - findfont: score(FontEntry(fname='C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\site-packages\\matplotlib\\mpl-data\\fonts\\ttf\\cmr10.ttf', name='cmr10', style='normal', variant='normal', weight=400, stretch='normal', size='scalable')) = 10.05
2025-07-23 20:30:15,973 - DEBUG - findfont: score(FontEntry(fname='C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\site-packages\\matplotlib\\mpl-data\\fonts\\ttf\\cmb10.ttf', name='cmb10', style='normal', variant='normal', weight=400, stretch='normal', size='scalable')) = 10.05
2025-07-23 20:30:15,973 - DEBUG - findfont: score(FontEntry(fname='C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\site-packages\\matplotlib\\mpl-data\\fonts\\ttf\\DejaVuSansDisplay.ttf', name='DejaVu Sans Display', style='normal', variant='normal', weight=400, stretch='normal', size='scalable')) = 10.05
2025-07-23 20:30:15,973 - DEBUG - findfont: score(FontEntry(fname='C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\site-packages\\matplotlib\\mpl-data\\fonts\\ttf\\DejaVuSans.ttf', name='DejaVu Sans', style='normal', variant='normal', weight=400, stretch='normal', size='scalable')) = 10.05
2025-07-23 20:30:15,973 - DEBUG - findfont: score(FontEntry(fname='C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\site-packages\\matplotlib\\mpl-data\\fonts\\ttf\\STIXSizThreeSymBol.ttf', name='STIXSizeThreeSym', style='normal', variant='normal', weight=700, stretch='normal', size='scalable')) = 10.335
2025-07-23 20:30:15,973 - DEBUG - findfont: score(FontEntry(fname='C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\site-packages\\matplotlib\\mpl-data\\fonts\\ttf\\STIXGeneralBolIta.ttf', name='STIXGeneral', style='italic', variant='normal', weight=700, stretch='normal', size='scalable')) = 11.335
2025-07-23 20:30:15,973 - DEBUG - findfont: score(FontEntry(fname='C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\site-packages\\matplotlib\\mpl-data\\fonts\\ttf\\DejaVuSerifDisplay.ttf', name='DejaVu Serif Display', style='normal', variant='normal', weight=400, stretch='normal', size='scalable')) = 10.05
2025-07-23 20:30:15,973 - DEBUG - findfont: score(FontEntry(fname='C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\site-packages\\matplotlib\\mpl-data\\fonts\\ttf\\DejaVuSans-BoldOblique.ttf', name='DejaVu Sans', style='oblique', variant='normal', weight=700, stretch='normal', size='scalable')) = 11.335
2025-07-23 20:30:15,973 - DEBUG - findfont: score(FontEntry(fname='C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\site-packages\\matplotlib\\mpl-data\\fonts\\ttf\\STIXNonUniIta.ttf', name='STIXNonUnicode', style='italic', variant='normal', weight=400, stretch='normal', size='scalable')) = 11.05
2025-07-23 20:30:15,973 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\VerdanaPro-Regular.ttf', name='Verdana Pro', style='normal', variant='normal', weight=400, stretch='normal', size='scalable')) = 10.05
2025-07-23 20:30:15,973 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\ArialNova.ttf', name='Arial Nova', style='normal', variant='normal', weight=400, stretch='normal', size='scalable')) = 10.05
2025-07-23 20:30:15,973 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\dokchamp.ttf', name='DokChampa', style='normal', variant='normal', weight=400, stretch='normal', size='scalable')) = 10.05
2025-07-23 20:30:15,973 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\ROCKI.TTF', name='Rockwell', style='italic', variant='normal', weight=400, stretch='normal', size='scalable')) = 11.05
2025-07-23 20:30:15,973 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\upcebi.ttf', name='EucrosiaUPC', style='italic', variant='normal', weight=700, stretch='normal', size='scalable')) = 11.335
2025-07-23 20:30:15,973 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\LFAXDI.TTF', name='Lucida Fax', style='italic', variant='normal', weight=600, stretch='normal', size='scalable')) = 11.24
2025-07-23 20:30:15,973 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\BOD_I.TTF', name='Bodoni MT', style='italic', variant='normal', weight=400, stretch='normal', size='scalable')) = 11.05
2025-07-23 20:30:15,973 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\comicz.ttf', name='Comic Sans MS', style='italic', variant='normal', weight=700, stretch='normal', size='scalable')) = 11.335
2025-07-23 20:30:15,973 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\VerdanaPro-CondRegular.ttf', name='Verdana Pro', style='normal', variant='normal', weight=400, stretch='condensed', size='scalable')) = 10.25
2025-07-23 20:30:15,973 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\GillSansLightItNova.ttf', name='Gill Sans Nova', style='italic', variant='normal', weight=300, stretch='normal', size='scalable')) = 11.145
2025-07-23 20:30:15,973 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\NotoSansTC-VF.ttf', name='Noto Sans TC', style='normal', variant='normal', weight=100, stretch='normal', size='scalable')) = 10.335
2025-07-23 20:30:15,973 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\upcll.ttf', name='LilyUPC', style='normal', variant='normal', weight=400, stretch='normal', size='scalable')) = 10.05
2025-07-23 20:30:15,973 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\REFSAN.TTF', name='MS Reference Sans Serif', style='normal', variant='normal', weight=400, stretch='normal', size='scalable')) = 10.05
2025-07-23 20:30:15,973 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\consolai.ttf', name='Consolas', style='italic', variant='normal', weight=400, stretch='normal', size='scalable')) = 11.05
2025-07-23 20:30:15,988 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\verdana.ttf', name='Verdana', style='normal', variant='normal', weight=400, stretch='normal', size='scalable')) = 10.05
2025-07-23 20:30:15,988 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\SCHLBKB.TTF', name='Century Schoolbook', style='normal', variant='normal', weight=700, stretch='normal', size='scalable')) = 10.335
2025-07-23 20:30:15,988 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\SNAP____.TTF', name='Snap ITC', style='normal', variant='normal', weight=400, stretch='normal', size='scalable')) = 10.05
2025-07-23 20:30:15,988 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\upckbi.ttf', name='KodchiangUPC', style='italic', variant='normal', weight=700, stretch='normal', size='scalable')) = 11.335
2025-07-23 20:30:15,988 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\latha.ttf', name='Latha', style='normal', variant='normal', weight=400, stretch='normal', size='scalable')) = 10.05
2025-07-23 20:30:15,988 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\monbaiti.ttf', name='Mongolian Baiti', style='normal', variant='normal', weight=400, stretch='normal', size='scalable')) = 10.05
2025-07-23 20:30:15,988 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\JOKERMAN.TTF', name='Jokerman', style='normal', variant='normal', weight=400, stretch='normal', size='scalable')) = 10.05
2025-07-23 20:30:15,988 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\mangalb.ttf', name='Mangal', style='normal', variant='normal', weight=700, stretch='normal', size='scalable')) = 10.335
2025-07-23 20:30:15,988 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\GeorgiaPro-CondItalic.ttf', name='Georgia Pro', style='italic', variant='normal', weight=400, stretch='condensed', size='scalable')) = 11.25
2025-07-23 20:30:15,988 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\arialni.ttf', name='Arial', style='italic', variant='normal', weight=400, stretch='condensed', size='scalable')) = 1.25
2025-07-23 20:30:15,988 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\arialnb.ttf', name='Arial', style='normal', variant='normal', weight=700, stretch='condensed', size='scalable')) = 0.5349999999999999
2025-07-23 20:30:15,988 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\ANTQUABI.TTF', name='Book Antiqua', style='italic', variant='normal', weight=700, stretch='normal', size='scalable')) = 11.335
2025-07-23 20:30:15,988 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\FTLTLT.TTF', name='Footlight MT Light', style='normal', variant='normal', weight=300, stretch='normal', size='scalable')) = 10.145
2025-07-23 20:30:15,988 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\Shonarb.ttf', name='Shonar Bangla', style='normal', variant='normal', weight=700, stretch='normal', size='scalable')) = 10.335
2025-07-23 20:30:15,988 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\msyhbd.ttc', name='Microsoft YaHei', style='normal', variant='normal', weight=700, stretch='normal', size='scalable')) = 10.335
2025-07-23 20:30:15,988 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\GIL_____.TTF', name='Gill Sans MT', style='normal', variant='normal', weight=400, stretch='normal', size='scalable')) = 10.05
2025-07-23 20:30:15,988 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\FORTE.TTF', name='Forte', style='normal', variant='normal', weight=400, stretch='normal', size='scalable')) = 10.05
2025-07-23 20:30:15,988 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\cambriai.ttf', name='Cambria', style='italic', variant='normal', weight=400, stretch='normal', size='scalable')) = 11.05
2025-07-23 20:30:15,988 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\arial.ttf', name='Arial', style='normal', variant='normal', weight=400, stretch='normal', size='scalable')) = 0.05
2025-07-23 20:30:15,988 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\GillSansItNova.ttf', name='Gill Sans Nova', style='italic', variant='normal', weight=500, stretch='normal', size='scalable')) = 11.145
2025-07-23 20:30:15,988 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\kokilai.ttf', name='Kokila', style='italic', variant='normal', weight=400, stretch='normal', size='scalable')) = 11.05
2025-07-23 20:30:15,988 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\tunga.ttf', name='Tunga', style='normal', variant='normal', weight=400, stretch='normal', size='scalable')) = 10.05
2025-07-23 20:30:15,988 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\nyala.ttf', name='Nyala', style='normal', variant='normal', weight=400, stretch='normal', size='scalable')) = 10.05
2025-07-23 20:30:15,988 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\AGENCYR.TTF', name='Agency FB', style='normal', variant='normal', weight=400, stretch='normal', size='scalable')) = 10.05
2025-07-23 20:30:15,988 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\framdit.ttf', name='Franklin Gothic Medium', style='italic', variant='normal', weight=400, stretch='normal', size='scalable')) = 11.05
2025-07-23 20:30:15,988 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\bahnschrift.ttf', name='Bahnschrift', style='normal', variant='normal', weight=400, stretch='normal', size='scalable')) = 10.05
2025-07-23 20:30:15,988 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\GeorgiaPro-CondBlack.ttf', name='Georgia Pro', style='normal', variant='normal', weight=900, stretch='condensed', size='scalable')) = 10.725
2025-07-23 20:30:15,988 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\GillSansCondNova.ttf', name='Gill Sans Nova', style='normal', variant='normal', weight=500, stretch='condensed', size='scalable')) = 10.344999999999999
2025-07-23 20:30:15,988 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\GillSansNova.ttf', name='Gill Sans Nova', style='normal', variant='normal', weight=500, stretch='normal', size='scalable')) = 10.145
2025-07-23 20:30:15,988 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\msyhl.ttc', name='Microsoft YaHei', style='normal', variant='normal', weight=290, stretch='normal', size='scalable')) = 10.1545
2025-07-23 20:30:15,988 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\RockwellNova-Italic.ttf', name='Rockwell Nova', style='italic', variant='normal', weight=400, stretch='normal', size='scalable')) = 11.05
2025-07-23 20:30:15,988 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\BOD_BI.TTF', name='Bodoni MT', style='italic', variant='normal', weight=700, stretch='normal', size='scalable')) = 11.335
2025-07-23 20:30:15,988 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\COOPBL.TTF', name='Cooper Black', style='normal', variant='normal', weight=400, stretch='normal', size='scalable')) = 10.05
2025-07-23 20:30:15,988 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\CALIFI.TTF', name='Californian FB', style='italic', variant='normal', weight=400, stretch='normal', size='scalable')) = 11.05
2025-07-23 20:30:15,988 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\upclbi.ttf', name='LilyUPC', style='italic', variant='normal', weight=700, stretch='normal', size='scalable')) = 11.335
2025-07-23 20:30:15,988 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\RockwellNovaCond.ttf', name='Rockwell Nova', style='normal', variant='normal', weight=400, stretch='condensed', size='scalable')) = 10.25
2025-07-23 20:30:15,988 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\VerdanaPro-SemiBoldItalic.ttf', name='Verdana Pro', style='italic', variant='normal', weight=600, stretch='normal', size='scalable')) = 11.24
2025-07-23 20:30:15,988 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\GOUDOSI.TTF', name='Goudy Old Style', style='italic', variant='normal', weight=400, stretch='normal', size='scalable')) = 11.05
2025-07-23 20:30:15,988 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\andlso.ttf', name='Andalus', style='normal', variant='normal', weight=400, stretch='normal', size='scalable')) = 10.05
2025-07-23 20:30:15,988 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\SimsunExtG.ttf', name='SimSun-ExtG', style='normal', variant='normal', weight=400, stretch='normal', size='scalable')) = 10.05
2025-07-23 20:30:15,988 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\GILB____.TTF', name='Gill Sans MT', style='normal', variant='normal', weight=700, stretch='normal', size='scalable')) = 10.335
2025-07-23 20:30:15,988 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\kalingab.ttf', name='Kalinga', style='normal', variant='normal', weight=700, stretch='normal', size='scalable')) = 10.335
2025-07-23 20:30:15,988 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\Inkfree.ttf', name='Ink Free', style='normal', variant='normal', weight=400, stretch='normal', size='scalable')) = 10.05
2025-07-23 20:30:15,988 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\BSSYM7.TTF', name='Bookshelf Symbol 7', style='normal', variant='normal', weight=400, stretch='normal', size='scalable')) = 10.05
2025-07-23 20:30:15,988 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\INFROMAN.TTF', name='Informal Roman', style='normal', variant='normal', weight=400, stretch='normal', size='scalable')) = 10.05
2025-07-23 20:30:15,988 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\GillSansCondExtraItNova.ttf', name='Gill Sans Nova', style='italic', variant='normal', weight=800, stretch='condensed', size='scalable')) = 11.629999999999999
2025-07-23 20:30:15,988 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\BOD_CR.TTF', name='Bodoni MT', style='normal', variant='normal', weight=400, stretch='condensed', size='scalable')) = 10.25
2025-07-23 20:30:15,988 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\ITCBLKAD.TTF', name='Blackadder ITC', style='normal', variant='normal', weight=400, stretch='normal', size='scalable')) = 10.05
2025-07-23 20:30:15,988 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\ahronbd.ttf', name='Aharoni', style='normal', variant='normal', weight=700, stretch='normal', size='scalable')) = 10.335
2025-07-23 20:30:15,988 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\UDDigiKyokashoN-R.ttc', name='UD Digi Kyokasho N-R', style='normal', variant='normal', weight=400, stretch='normal', size='scalable')) = 10.05
2025-07-23 20:30:15,988 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\vijaya.ttf', name='Vijaya', style='normal', variant='normal', weight=400, stretch='normal', size='scalable')) = 10.05
2025-07-23 20:30:15,988 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\LaoUI.ttf', name='Lao UI', style='normal', variant='normal', weight=400, stretch='normal', size='scalable')) = 10.05
2025-07-23 20:30:15,988 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\GillSansUltraBoNova.ttf', name='Gill Sans Nova', style='normal', variant='normal', weight=900, stretch='normal', size='scalable')) = 10.525
2025-07-23 20:30:15,988 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\vijayab.ttf', name='Vijaya', style='normal', variant='normal', weight=700, stretch='normal', size='scalable')) = 10.335
2025-07-23 20:30:15,988 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\PRISTINA.TTF', name='Pristina', style='normal', variant='normal', weight=400, stretch='normal', size='scalable')) = 10.05
2025-07-23 20:30:15,988 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\msjh.ttc', name='Microsoft JhengHei', style='normal', variant='normal', weight=400, stretch='normal', size='scalable')) = 10.05
2025-07-23 20:30:15,988 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\mriamc.ttf', name='Miriam Fixed', style='normal', variant='normal', weight=400, stretch='normal', size='scalable')) = 10.05
2025-07-23 20:30:15,988 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\segoeuib.ttf', name='Segoe UI', style='normal', variant='normal', weight=700, stretch='normal', size='scalable')) = 10.335
2025-07-23 20:30:15,988 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\frank.ttf', name='FrankRuehl', style='normal', variant='normal', weight=400, stretch='normal', size='scalable')) = 10.05
2025-07-23 20:30:15,988 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\Dengb.ttf', name='DengXian', style='normal', variant='normal', weight=700, stretch='normal', size='scalable')) = 10.335
2025-07-23 20:30:15,988 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\LSANSDI.TTF', name='Lucida Sans', style='italic', variant='normal', weight=600, stretch='normal', size='scalable')) = 11.24
2025-07-23 20:30:15,988 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\CALISTBI.TTF', name='Calisto MT', style='italic', variant='normal', weight=700, stretch='normal', size='scalable')) = 11.335
2025-07-23 20:30:15,988 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\CALISTB.TTF', name='Calisto MT', style='normal', variant='normal', weight=700, stretch='normal', size='scalable')) = 10.335
2025-07-23 20:30:15,988 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\rod.ttf', name='Rod', style='normal', variant='normal', weight=400, stretch='normal', size='scalable')) = 10.05
2025-07-23 20:30:15,988 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\BROADW.TTF', name='Broadway', style='normal', variant='normal', weight=400, stretch='normal', size='scalable')) = 10.05
2025-07-23 20:30:15,988 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\lvnmbd.ttf', name='Levenim MT', style='normal', variant='normal', weight=700, stretch='normal', size='scalable')) = 10.335
2025-07-23 20:30:15,988 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\GILSANUB.TTF', name='Gill Sans Ultra Bold', style='normal', variant='normal', weight=400, stretch='normal', size='scalable')) = 10.05
2025-07-23 20:30:15,988 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\VerdanaPro-SemiBold.ttf', name='Verdana Pro', style='normal', variant='normal', weight=600, stretch='normal', size='scalable')) = 10.24
2025-07-23 20:30:15,988 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\GOTHICBI.TTF', name='Century Gothic', style='italic', variant='normal', weight=700, stretch='normal', size='scalable')) = 11.335
2025-07-23 20:30:15,988 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\simpo.ttf', name='Simplified Arabic', style='normal', variant='normal', weight=400, stretch='normal', size='scalable')) = 10.05
2025-07-23 20:30:15,988 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\RAVIE.TTF', name='Ravie', style='normal', variant='normal', weight=400, stretch='normal', size='scalable')) = 10.05
2025-07-23 20:30:15,988 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\majalla.ttf', name='Sakkal Majalla', style='normal', variant='normal', weight=400, stretch='normal', size='scalable')) = 10.05
2025-07-23 20:30:15,988 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\NIAGSOL.TTF', name='Niagara Solid', style='normal', variant='normal', weight=400, stretch='normal', size='scalable')) = 10.05
2025-07-23 20:30:15,988 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\CHILLER.TTF', name='Chiller', style='normal', variant='normal', weight=400, stretch='normal', size='scalable')) = 10.05
2025-07-23 20:30:15,988 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\consola.ttf', name='Consolas', style='normal', variant='normal', weight=400, stretch='normal', size='scalable')) = 10.05
2025-07-23 20:30:15,988 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\LBRITED.TTF', name='Lucida Bright', style='normal', variant='normal', weight=600, stretch='normal', size='scalable')) = 10.24
2025-07-23 20:30:15,988 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\BRLNSR.TTF', name='Berlin Sans FB', style='normal', variant='normal', weight=400, stretch='normal', size='scalable')) = 10.05
2025-07-23 20:30:15,988 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\calibrili.ttf', name='Calibri', style='italic', variant='normal', weight=300, stretch='normal', size='scalable')) = 11.145
2025-07-23 20:30:15,988 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\COPRGTB.TTF', name='Copperplate Gothic Bold', style='normal', variant='normal', weight=400, stretch='normal', size='scalable')) = 10.05
2025-07-23 20:30:15,988 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\simsun.ttc', name='SimSun', style='normal', variant='normal', weight=400, stretch='normal', size='scalable')) = 10.05
2025-07-23 20:30:15,988 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\MTCORSVA.TTF', name='Monotype Corsiva', style='normal', variant='normal', weight=400, stretch='normal', size='scalable')) = 10.05
2025-07-23 20:30:15,988 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\RockwellNovaCond-Light.ttf', name='Rockwell Nova', style='normal', variant='normal', weight=300, stretch='condensed', size='scalable')) = 10.344999999999999
2025-07-23 20:30:15,988 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\Candaral.ttf', name='Candara', style='normal', variant='normal', weight=400, stretch='normal', size='scalable')) = 10.05
2025-07-23 20:30:15,988 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\Candara.ttf', name='Candara', style='normal', variant='normal', weight=400, stretch='normal', size='scalable')) = 10.05
2025-07-23 20:30:15,988 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\seguibli.ttf', name='Segoe UI', style='italic', variant='normal', weight=900, stretch='normal', size='scalable')) = 11.525
2025-07-23 20:30:15,988 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\cambriaz.ttf', name='Cambria', style='italic', variant='normal', weight=700, stretch='normal', size='scalable')) = 11.335
2025-07-23 20:30:15,988 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\upcjl.ttf', name='JasmineUPC', style='normal', variant='normal', weight=400, stretch='normal', size='scalable')) = 10.05
2025-07-23 20:30:15,988 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\BOOKOSB.TTF', name='Bookman Old Style', style='normal', variant='normal', weight=600, stretch='normal', size='scalable')) = 10.24
2025-07-23 20:30:15,988 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\JUICE___.TTF', name='Juice ITC', style='normal', variant='normal', weight=400, stretch='normal', size='scalable')) = 10.05
2025-07-23 20:30:15,988 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\NotoSansHK-VF.ttf', name='Noto Sans HK', style='normal', variant='normal', weight=100, stretch='normal', size='scalable')) = 10.335
2025-07-23 20:30:15,988 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\Shonar.ttf', name='Shonar Bangla', style='normal', variant='normal', weight=400, stretch='normal', size='scalable')) = 10.05
2025-07-23 20:30:15,988 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\nrkis.ttf', name='Narkisim', style='normal', variant='normal', weight=400, stretch='normal', size='scalable')) = 10.05
2025-07-23 20:30:15,988 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\mingliub.ttc', name='MingLiU-ExtB', style='normal', variant='normal', weight=400, stretch='normal', size='scalable')) = 10.05
2025-07-23 20:30:15,988 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\GLSNECB.TTF', name='Gill Sans MT Ext Condensed Bold', style='normal', variant='normal', weight=400, stretch='condensed', size='scalable')) = 10.25
2025-07-23 20:30:15,988 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\BOOKOSI.TTF', name='Bookman Old Style', style='italic', variant='normal', weight=300, stretch='normal', size='scalable')) = 11.145
2025-07-23 20:30:15,988 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\HARLOWSI.TTF', name='Harlow Solid Italic', style='italic', variant='normal', weight=400, stretch='normal', size='scalable')) = 11.05
2025-07-23 20:30:15,988 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\VerdanaPro-CondBoldItalic.ttf', name='Verdana Pro', style='italic', variant='normal', weight=700, stretch='condensed', size='scalable')) = 11.535
2025-07-23 20:30:15,988 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\GillSansBoItNova.ttf', name='Gill Sans Nova', style='italic', variant='normal', weight=700, stretch='normal', size='scalable')) = 11.335
2025-07-23 20:30:15,988 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\l_10646.ttf', name='Lucida Sans Unicode', style='normal', variant='normal', weight=400, stretch='normal', size='scalable')) = 10.05
2025-07-23 20:30:15,988 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\gishabd.ttf', name='Gisha', style='normal', variant='normal', weight=700, stretch='normal', size='scalable')) = 10.335
2025-07-23 20:30:15,988 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\BOD_B.TTF', name='Bodoni MT', style='normal', variant='normal', weight=700, stretch='normal', size='scalable')) = 10.335
2025-07-23 20:30:15,988 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\utsaah.ttf', name='Utsaah', style='normal', variant='normal', weight=400, stretch='normal', size='scalable')) = 10.05
2025-07-23 20:30:15,988 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\utsaahb.ttf', name='Utsaah', style='normal', variant='normal', weight=700, stretch='normal', size='scalable')) = 10.335
2025-07-23 20:30:15,988 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\TCCEB.TTF', name='Tw Cen MT Condensed Extra Bold', style='normal', variant='normal', weight=400, stretch='condensed', size='scalable')) = 10.25
2025-07-23 20:30:15,988 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\GeorgiaPro-CondLightItalic.ttf', name='Georgia Pro', style='italic', variant='normal', weight=300, stretch='condensed', size='scalable')) = 11.344999999999999
2025-07-23 20:30:16,004 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\verdanai.ttf', name='Verdana', style='italic', variant='normal', weight=400, stretch='normal', size='scalable')) = 11.05
2025-07-23 20:30:16,004 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\ntailu.ttf', name='Microsoft New Tai Lue', style='normal', variant='normal', weight=400, stretch='normal', size='scalable')) = 10.05
2025-07-23 20:30:16,004 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\SitkaZ.ttc', name='Sitka Small', style='italic', variant='normal', weight=700, stretch='normal', size='scalable')) = 11.335
2025-07-23 20:30:16,004 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\LATINWD.TTF', name='Wide Latin', style='normal', variant='normal', weight=400, stretch='expanded', size='scalable')) = 10.25
2025-07-23 20:30:16,004 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\segoeprb.ttf', name='Segoe Print', style='normal', variant='normal', weight=700, stretch='normal', size='scalable')) = 10.335
2025-07-23 20:30:16,004 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\segoeui.ttf', name='Segoe UI', style='normal', variant='normal', weight=400, stretch='normal', size='scalable')) = 10.05
2025-07-23 20:30:16,004 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\COLONNA.TTF', name='Colonna MT', style='normal', variant='normal', weight=400, stretch='normal', size='scalable')) = 10.05
2025-07-23 20:30:16,004 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\Gabriola.ttf', name='Gabriola', style='normal', variant='normal', weight=400, stretch='normal', size='scalable')) = 10.05
2025-07-23 20:30:16,004 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\UDDigiKyokashoN-B.ttc', name='UD Digi Kyokasho N-B', style='normal', variant='normal', weight=700, stretch='normal', size='scalable')) = 10.335
2025-07-23 20:30:16,004 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\phagspa.ttf', name='Microsoft PhagsPa', style='normal', variant='normal', weight=400, stretch='normal', size='scalable')) = 10.05
2025-07-23 20:30:16,004 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\FRSCRIPT.TTF', name='French Script MT', style='normal', variant='normal', weight=400, stretch='normal', size='scalable')) = 10.05
2025-07-23 20:30:16,004 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\NotoSansJP-VF.ttf', name='Noto Sans JP', style='normal', variant='normal', weight=100, stretch='normal', size='scalable')) = 10.335
2025-07-23 20:30:16,004 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\GeorgiaPro-CondSemiBoldItalic.ttf', name='Georgia Pro', style='italic', variant='normal', weight=600, stretch='condensed', size='scalable')) = 11.44
2025-07-23 20:30:16,004 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\ArialNova-Light.ttf', name='Arial Nova', style='normal', variant='normal', weight=300, stretch='normal', size='scalable')) = 10.145
2025-07-23 20:30:16,004 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\davidbd.ttf', name='David', style='normal', variant='normal', weight=700, stretch='normal', size='scalable')) = 10.335
2025-07-23 20:30:16,004 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\GeorgiaPro-CondLight.ttf', name='Georgia Pro', style='normal', variant='normal', weight=300, stretch='condensed', size='scalable')) = 10.344999999999999
2025-07-23 20:30:16,004 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\GOTHICI.TTF', name='Century Gothic', style='italic', variant='normal', weight=400, stretch='normal', size='scalable')) = 11.05
2025-07-23 20:30:16,004 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\VLADIMIR.TTF', name='Vladimir Script', style='normal', variant='normal', weight=400, stretch='normal', size='scalable')) = 10.05
2025-07-23 20:30:16,004 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\upcjbi.ttf', name='JasmineUPC', style='italic', variant='normal', weight=700, stretch='normal', size='scalable')) = 11.335
2025-07-23 20:30:16,004 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\HPSimplified_It.ttf', name='HP Simplified', style='italic', variant='normal', weight=400, stretch='normal', size='scalable')) = 11.05
2025-07-23 20:30:16,004 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\GeorgiaPro-Black.ttf', name='Georgia Pro', style='normal', variant='normal', weight=900, stretch='normal', size='scalable')) = 10.525
2025-07-23 20:30:16,004 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\ERASMD.TTF', name='Eras Medium ITC', style='normal', variant='normal', weight=400, stretch='normal', size='scalable')) = 10.05
2025-07-23 20:30:16,004 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\VerdanaPro-LightItalic.ttf', name='Verdana Pro', style='italic', variant='normal', weight=300, stretch='normal', size='scalable')) = 11.145
2025-07-23 20:30:16,004 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\yumindb.ttf', name='Yu Mincho', style='normal', variant='normal', weight=600, stretch='normal', size='scalable')) = 10.24
2025-07-23 20:30:16,004 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\BAUHS93.TTF', name='Bauhaus 93', style='normal', variant='normal', weight=400, stretch='normal', size='scalable')) = 10.05
2025-07-23 20:30:16,004 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\mmrtextb.ttf', name='Myanmar Text', style='normal', variant='normal', weight=700, stretch='normal', size='scalable')) = 10.335
2025-07-23 20:30:16,004 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\OCRAEXT.TTF', name='OCR A Extended', style='normal', variant='normal', weight=400, stretch='expanded', size='scalable')) = 10.25
2025-07-23 20:30:16,004 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\kalinga.ttf', name='Kalinga', style='normal', variant='normal', weight=400, stretch='normal', size='scalable')) = 10.05
2025-07-23 20:30:16,004 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\MISTRAL.TTF', name='Mistral', style='normal', variant='normal', weight=400, stretch='normal', size='scalable')) = 10.05
2025-07-23 20:30:16,004 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\simfang.ttf', name='FangSong', style='normal', variant='normal', weight=400, stretch='normal', size='scalable')) = 10.05
2025-07-23 20:30:16,004 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\VerdanaPro-Bold.ttf', name='Verdana Pro', style='normal', variant='normal', weight=700, stretch='normal', size='scalable')) = 10.335
2025-07-23 20:30:16,004 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\upcki.ttf', name='KodchiangUPC', style='italic', variant='normal', weight=400, stretch='normal', size='scalable')) = 11.05
2025-07-23 20:30:16,004 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\ntailub.ttf', name='Microsoft New Tai Lue', style='normal', variant='normal', weight=700, stretch='normal', size='scalable')) = 10.335
2025-07-23 20:30:16,004 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\ALGER.TTF', name='Algerian', style='normal', variant='normal', weight=400, stretch='normal', size='scalable')) = 10.05
2025-07-23 20:30:16,004 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\RockwellNova-LightItalic.ttf', name='Rockwell Nova', style='italic', variant='normal', weight=300, stretch='normal', size='scalable')) = 11.145
2025-07-23 20:30:16,004 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\upcdi.ttf', name='DilleniaUPC', style='italic', variant='normal', weight=400, stretch='normal', size='scalable')) = 11.05
2025-07-23 20:30:16,004 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\GillSansCondExtraNova.ttf', name='Gill Sans Nova', style='normal', variant='normal', weight=800, stretch='condensed', size='scalable')) = 10.629999999999999
2025-07-23 20:30:16,004 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\ARIALUNI.TTF', name='Arial Unicode MS', style='normal', variant='normal', weight=400, stretch='normal', size='scalable')) = 10.05
2025-07-23 20:30:16,004 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\palai.ttf', name='Palatino Linotype', style='italic', variant='normal', weight=400, stretch='normal', size='scalable')) = 11.05
2025-07-23 20:30:16,004 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\ROCC____.TTF', name='Rockwell Condensed', style='normal', variant='normal', weight=400, stretch='condensed', size='scalable')) = 10.25
2025-07-23 20:30:16,004 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\GeorgiaPro-CondRegular.ttf', name='Georgia Pro', style='normal', variant='normal', weight=400, stretch='condensed', size='scalable')) = 10.25
2025-07-23 20:30:16,004 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\LFAX.TTF', name='Lucida Fax', style='normal', variant='normal', weight=400, stretch='normal', size='scalable')) = 10.05
2025-07-23 20:30:16,004 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\TCCB____.TTF', name='Tw Cen MT Condensed', style='normal', variant='normal', weight=700, stretch='condensed', size='scalable')) = 10.535
2025-07-23 20:30:16,004 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\tungab.ttf', name='Tunga', style='normal', variant='normal', weight=700, stretch='normal', size='scalable')) = 10.335
2025-07-23 20:30:16,004 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\FRAMDCN.TTF', name='Franklin Gothic Medium Cond', style='normal', variant='normal', weight=400, stretch='condensed', size='scalable')) = 10.25
2025-07-23 20:30:16,004 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\kartika.ttf', name='Kartika', style='normal', variant='normal', weight=400, stretch='normal', size='scalable')) = 10.05
2025-07-23 20:30:16,004 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\PAPYRUS.TTF', name='Papyrus', style='normal', variant='normal', weight=400, stretch='normal', size='scalable')) = 10.05
2025-07-23 20:30:16,004 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\LFAXI.TTF', name='Lucida Fax', style='italic', variant='normal', weight=400, stretch='normal', size='scalable')) = 11.05
2025-07-23 20:30:16,004 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\BERNHC.TTF', name='Bernard MT Condensed', style='normal', variant='normal', weight=400, stretch='condensed', size='scalable')) = 10.25
2025-07-23 20:30:16,004 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\GeorgiaPro-SemiBold.ttf', name='Georgia Pro', style='normal', variant='normal', weight=600, stretch='normal', size='scalable')) = 10.24
2025-07-23 20:30:16,004 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\simsunb.ttf', name='SimSun-ExtB', style='normal', variant='normal', weight=400, stretch='normal', size='scalable')) = 10.05
2025-07-23 20:30:16,004 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\cambriab.ttf', name='Cambria', style='normal', variant='normal', weight=700, stretch='normal', size='scalable')) = 10.335
2025-07-23 20:30:16,004 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\ArialNovaCond-Light.ttf', name='Arial Nova', style='normal', variant='normal', weight=300, stretch='condensed', size='scalable')) = 10.344999999999999
2025-07-23 20:30:16,004 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\ArialNova-BoldItalic.ttf', name='Arial Nova', style='italic', variant='normal', weight=700, stretch='normal', size='scalable')) = 11.335
2025-07-23 20:30:16,004 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\yuminl.ttf', name='Yu Mincho', style='normal', variant='normal', weight=300, stretch='normal', size='scalable')) = 10.145
2025-07-23 20:30:16,004 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\BRLNSDB.TTF', name='Berlin Sans FB Demi', style='normal', variant='normal', weight=700, stretch='normal', size='scalable')) = 10.335
2025-07-23 20:30:16,004 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\taile.ttf', name='Microsoft Tai Le', style='normal', variant='normal', weight=400, stretch='normal', size='scalable')) = 10.05
2025-07-23 20:30:16,004 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\courbd.ttf', name='Courier New', style='normal', variant='normal', weight=700, stretch='normal', size='scalable')) = 10.335
2025-07-23 20:30:16,004 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\gadugib.ttf', name='Gadugi', style='normal', variant='normal', weight=700, stretch='normal', size='scalable')) = 10.335
2025-07-23 20:30:16,004 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\mvboli.ttf', name='MV Boli', style='normal', variant='normal', weight=400, stretch='normal', size='scalable')) = 10.05
2025-07-23 20:30:16,004 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\upcfi.ttf', name='FreesiaUPC', style='italic', variant='normal', weight=400, stretch='normal', size='scalable')) = 11.05
2025-07-23 20:30:16,004 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\VerdanaPro-CondBlackItalic.ttf', name='Verdana Pro', style='italic', variant='normal', weight=900, stretch='condensed', size='scalable')) = 11.725
2025-07-23 20:30:16,004 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\consolaz.ttf', name='Consolas', style='italic', variant='normal', weight=700, stretch='normal', size='scalable')) = 11.335
2025-07-23 20:30:16,004 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\GeorgiaPro-Italic.ttf', name='Georgia Pro', style='italic', variant='normal', weight=400, stretch='normal', size='scalable')) = 11.05
2025-07-23 20:30:16,004 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\PALSCRI.TTF', name='Palace Script MT', style='normal', variant='normal', weight=400, stretch='normal', size='scalable')) = 10.05
2025-07-23 20:30:16,004 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\PERTIBD.TTF', name='Perpetua Titling MT', style='normal', variant='normal', weight=700, stretch='normal', size='scalable')) = 10.335
2025-07-23 20:30:16,004 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\segoepr.ttf', name='Segoe Print', style='normal', variant='normal', weight=400, stretch='normal', size='scalable')) = 10.05
2025-07-23 20:30:16,004 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\ROCK.TTF', name='Rockwell', style='normal', variant='normal', weight=400, stretch='normal', size='scalable')) = 10.05
2025-07-23 20:30:16,004 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\GILI____.TTF', name='Gill Sans MT', style='italic', variant='normal', weight=400, stretch='normal', size='scalable')) = 11.05
2025-07-23 20:30:16,004 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\upcib.ttf', name='IrisUPC', style='normal', variant='normal', weight=700, stretch='normal', size='scalable')) = 10.335
2025-07-23 20:30:16,004 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\GillSansCondBoItNova.ttf', name='Gill Sans Nova', style='italic', variant='normal', weight=700, stretch='condensed', size='scalable')) = 11.535
2025-07-23 20:30:16,004 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\upcli.ttf', name='LilyUPC', style='italic', variant='normal', weight=400, stretch='normal', size='scalable')) = 11.05
2025-07-23 20:30:16,004 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\NirmalaB.ttf', name='Nirmala UI', style='normal', variant='normal', weight=700, stretch='normal', size='scalable')) = 10.335
2025-07-23 20:30:16,004 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\PARCHM.TTF', name='Parchment', style='normal', variant='normal', weight=400, stretch='normal', size='scalable')) = 10.05
2025-07-23 20:30:16,004 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\FRADMIT.TTF', name='Franklin Gothic Demi', style='italic', variant='normal', weight=400, stretch='normal', size='scalable')) = 11.05
2025-07-23 20:30:16,004 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\arabtype.ttf', name='Arabic Typesetting', style='normal', variant='normal', weight=400, stretch='normal', size='scalable')) = 10.05
2025-07-23 20:30:16,004 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\TEMPSITC.TTF', name='Tempus Sans ITC', style='normal', variant='normal', weight=400, stretch='normal', size='scalable')) = 10.05
2025-07-23 20:30:16,004 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\cordia.ttc', name='Cordia New', style='normal', variant='normal', weight=400, stretch='normal', size='scalable')) = 10.05
2025-07-23 20:30:16,004 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\ITCEDSCR.TTF', name='Edwardian Script ITC', style='normal', variant='normal', weight=400, stretch='normal', size='scalable')) = 10.05
2025-07-23 20:30:16,004 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\MAGNETOB.TTF', name='Magneto', style='normal', variant='normal', weight=700, stretch='normal', size='scalable')) = 10.335
2025-07-23 20:30:16,004 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\seguibl.ttf', name='Segoe UI', style='normal', variant='normal', weight=900, stretch='normal', size='scalable')) = 10.525
2025-07-23 20:30:16,004 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\YuGothB.ttc', name='Yu Gothic', style='normal', variant='normal', weight=700, stretch='normal', size='scalable')) = 10.335
2025-07-23 20:30:16,004 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\VerdanaPro-Light.ttf', name='Verdana Pro', style='normal', variant='normal', weight=300, stretch='normal', size='scalable')) = 10.145
2025-07-23 20:30:16,004 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\NotoSansSC-VF.ttf', name='Noto Sans SC', style='normal', variant='normal', weight=100, stretch='normal', size='scalable')) = 10.335
2025-07-23 20:30:16,004 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\micross.ttf', name='Microsoft Sans Serif', style='normal', variant='normal', weight=400, stretch='normal', size='scalable')) = 10.05
2025-07-23 20:30:16,004 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\FRADMCN.TTF', name='Franklin Gothic Demi Cond', style='normal', variant='normal', weight=400, stretch='condensed', size='scalable')) = 10.25
2025-07-23 20:30:16,004 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\upcel.ttf', name='EucrosiaUPC', style='normal', variant='normal', weight=400, stretch='normal', size='scalable')) = 10.05
2025-07-23 20:30:16,004 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\BOD_BLAI.TTF', name='Bodoni MT', style='italic', variant='normal', weight=900, stretch='normal', size='scalable')) = 11.525
2025-07-23 20:30:16,004 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\CALIFR.TTF', name='Californian FB', style='normal', variant='normal', weight=400, stretch='normal', size='scalable')) = 10.05
2025-07-23 20:30:16,004 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\VIVALDII.TTF', name='Vivaldi', style='italic', variant='normal', weight=400, stretch='normal', size='scalable')) = 11.05
2025-07-23 20:30:16,004 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\GARABD.TTF', name='Garamond', style='normal', variant='normal', weight=700, stretch='normal', size='scalable')) = 10.335
2025-07-23 20:30:16,004 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\upckb.ttf', name='KodchiangUPC', style='normal', variant='normal', weight=700, stretch='normal', size='scalable')) = 10.335
2025-07-23 20:30:16,004 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\FRABK.TTF', name='Franklin Gothic Book', style='normal', variant='normal', weight=400, stretch='normal', size='scalable')) = 10.05
2025-07-23 20:30:16,004 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\georgiai.ttf', name='Georgia', style='italic', variant='normal', weight=400, stretch='normal', size='scalable')) = 11.05
2025-07-23 20:30:16,004 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\COPRGTL.TTF', name='Copperplate Gothic Light', style='normal', variant='normal', weight=400, stretch='normal', size='scalable')) = 10.05
2025-07-23 20:30:16,004 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\VerdanaPro-BlackItalic.ttf', name='Verdana Pro', style='italic', variant='normal', weight=900, stretch='normal', size='scalable')) = 11.525
2025-07-23 20:30:16,004 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\upcji.ttf', name='JasmineUPC', style='italic', variant='normal', weight=400, stretch='normal', size='scalable')) = 11.05
2025-07-23 20:30:16,004 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\GillSansCondItNova.ttf', name='Gill Sans Nova', style='italic', variant='normal', weight=500, stretch='condensed', size='scalable')) = 11.344999999999999
2025-07-23 20:30:16,004 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\GeorgiaPro-Bold.ttf', name='Georgia Pro', style='normal', variant='normal', weight=700, stretch='normal', size='scalable')) = 10.335
2025-07-23 20:30:16,004 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\BASKVILL.TTF', name='Baskerville Old Face', style='normal', variant='normal', weight=400, stretch='normal', size='scalable')) = 10.05
2025-07-23 20:30:16,004 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\segoeuil.ttf', name='Segoe UI', style='normal', variant='normal', weight=300, stretch='normal', size='scalable')) = 10.145
2025-07-23 20:30:16,004 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\GIGI.TTF', name='Gigi', style='normal', variant='normal', weight=400, stretch='normal', size='scalable')) = 10.05
2025-07-23 20:30:16,004 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\IMPRISHA.TTF', name='Imprint MT Shadow', style='normal', variant='normal', weight=400, stretch='normal', size='scalable')) = 10.05
2025-07-23 20:30:16,004 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\GLECB.TTF', name='Gloucester MT Extra Condensed', style='normal', variant='normal', weight=400, stretch='condensed', size='scalable')) = 10.25
2025-07-23 20:30:16,020 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\KUNSTLER.TTF', name='Kunstler Script', style='normal', variant='normal', weight=400, stretch='normal', size='scalable')) = 10.05
2025-07-23 20:30:16,020 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\GillSansCondUltraBoNova.ttf', name='Gill Sans Nova', style='normal', variant='normal', weight=900, stretch='condensed', size='scalable')) = 10.725
2025-07-23 20:30:16,020 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\CENSCBK.TTF', name='Century Schoolbook', style='normal', variant='normal', weight=400, stretch='normal', size='scalable')) = 10.05
2025-07-23 20:30:16,020 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\MOD20.TTF', name='Modern No. 20', style='normal', variant='normal', weight=400, stretch='normal', size='scalable')) = 10.05
2025-07-23 20:30:16,020 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\VerdanaPro-BoldItalic.ttf', name='Verdana Pro', style='italic', variant='normal', weight=700, stretch='normal', size='scalable')) = 11.335
2025-07-23 20:30:16,020 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\LBRITEDI.TTF', name='Lucida Bright', style='italic', variant='normal', weight=600, stretch='normal', size='scalable')) = 11.24
2025-07-23 20:30:16,020 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\POORICH.TTF', name='Poor Richard', style='normal', variant='normal', weight=400, stretch='normal', size='scalable')) = 10.05
2025-07-23 20:30:16,020 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\UrdType.ttf', name='Urdu Typesetting', style='normal', variant='normal', weight=400, stretch='normal', size='scalable')) = 10.05
2025-07-23 20:30:16,020 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\aparajbi.ttf', name='Aparajita', style='italic', variant='normal', weight=700, stretch='normal', size='scalable')) = 11.335
2025-07-23 20:30:16,020 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\ROCKEB.TTF', name='Rockwell Extra Bold', style='normal', variant='normal', weight=800, stretch='normal', size='scalable')) = 10.43
2025-07-23 20:30:16,020 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\euphemia.ttf', name='Euphemia', style='normal', variant='normal', weight=400, stretch='normal', size='scalable')) = 10.05
2025-07-23 20:30:16,020 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\RockwellNovaCond-BoldItalic.ttf', name='Rockwell Nova', style='italic', variant='normal', weight=700, stretch='condensed', size='scalable')) = 11.535
2025-07-23 20:30:16,020 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\TCB_____.TTF', name='Tw Cen MT', style='normal', variant='normal', weight=700, stretch='normal', size='scalable')) = 10.335
2025-07-23 20:30:16,020 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\aparaji.ttf', name='Aparajita', style='italic', variant='normal', weight=400, stretch='normal', size='scalable')) = 11.05
2025-07-23 20:30:16,020 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\upcfl.ttf', name='FreesiaUPC', style='normal', variant='normal', weight=400, stretch='normal', size='scalable')) = 10.05
2025-07-23 20:30:16,020 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\upceb.ttf', name='EucrosiaUPC', style='normal', variant='normal', weight=700, stretch='normal', size='scalable')) = 10.335
2025-07-23 20:30:16,020 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\calibri.ttf', name='Calibri', style='normal', variant='normal', weight=400, stretch='normal', size='scalable')) = 10.05
2025-07-23 20:30:16,020 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\seguisli.ttf', name='Segoe UI', style='italic', variant='normal', weight=350, stretch='normal', size='scalable')) = 11.0975
2025-07-23 20:30:16,020 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\TCM_____.TTF', name='Tw Cen MT', style='normal', variant='normal', weight=400, stretch='normal', size='scalable')) = 10.05
2025-07-23 20:30:16,020 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\corbelz.ttf', name='Corbel', style='italic', variant='normal', weight=700, stretch='normal', size='scalable')) = 11.335
2025-07-23 20:30:16,020 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\ariblk.ttf', name='Arial', style='normal', variant='normal', weight=900, stretch='normal', size='scalable')) = 0.525
2025-07-23 20:30:16,020 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\mmrtext.ttf', name='Myanmar Text', style='normal', variant='normal', weight=400, stretch='normal', size='scalable')) = 10.05
2025-07-23 20:30:16,020 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\georgia.ttf', name='Georgia', style='normal', variant='normal', weight=400, stretch='normal', size='scalable')) = 10.05
2025-07-23 20:30:16,020 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\msjhl.ttc', name='Microsoft JhengHei', style='normal', variant='normal', weight=290, stretch='normal', size='scalable')) = 10.1545
2025-07-23 20:30:16,020 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\tradbdo.ttf', name='Traditional Arabic', style='normal', variant='normal', weight=700, stretch='normal', size='scalable')) = 10.335
2025-07-23 20:30:16,020 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\ArialNovaCond-Italic.ttf', name='Arial Nova', style='italic', variant='normal', weight=400, stretch='condensed', size='scalable')) = 11.25
2025-07-23 20:30:16,020 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\msjhbd.ttc', name='Microsoft JhengHei', style='normal', variant='normal', weight=700, stretch='normal', size='scalable')) = 10.335
2025-07-23 20:30:16,020 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\FRAHVIT.TTF', name='Franklin Gothic Heavy', style='italic', variant='normal', weight=400, stretch='normal', size='scalable')) = 11.05
2025-07-23 20:30:16,020 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\georgiab.ttf', name='Georgia', style='normal', variant='normal', weight=700, stretch='normal', size='scalable')) = 10.335
2025-07-23 20:30:16,020 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\daunpenh.ttf', name='DaunPenh', style='normal', variant='normal', weight=400, stretch='normal', size='scalable')) = 10.05
2025-07-23 20:30:16,020 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\HTOWERT.TTF', name='High Tower Text', style='normal', variant='normal', weight=400, stretch='normal', size='scalable')) = 10.05
2025-07-23 20:30:16,020 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\david.ttf', name='David', style='normal', variant='normal', weight=400, stretch='normal', size='scalable')) = 10.05
2025-07-23 20:30:16,020 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\NotoSansKR-VF.ttf', name='Noto Sans KR', style='normal', variant='normal', weight=100, stretch='normal', size='scalable')) = 10.335
2025-07-23 20:30:16,020 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\ANTQUAI.TTF', name='Book Antiqua', style='italic', variant='normal', weight=400, stretch='normal', size='scalable')) = 11.05
2025-07-23 20:30:16,020 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\holomdl2.ttf', name='HoloLens MDL2 Assets', style='normal', variant='normal', weight=400, stretch='normal', size='scalable')) = 10.05
2025-07-23 20:30:16,020 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\corbeli.ttf', name='Corbel', style='italic', variant='normal', weight=400, stretch='normal', size='scalable')) = 11.05
2025-07-23 20:30:16,020 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\aparajb.ttf', name='Aparajita', style='normal', variant='normal', weight=700, stretch='normal', size='scalable')) = 10.335
2025-07-23 20:30:16,020 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\constanb.ttf', name='Constantia', style='normal', variant='normal', weight=700, stretch='normal', size='scalable')) = 10.335
2025-07-23 20:30:16,020 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\GeorgiaPro-LightItalic.ttf', name='Georgia Pro', style='italic', variant='normal', weight=300, stretch='normal', size='scalable')) = 11.145
2025-07-23 20:30:16,020 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\plantc.ttf', name='Plantagenet Cherokee', style='normal', variant='normal', weight=400, stretch='normal', size='scalable')) = 10.05
2025-07-23 20:30:16,020 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\HARNGTON.TTF', name='Harrington', style='normal', variant='normal', weight=400, stretch='normal', size='scalable')) = 10.05
2025-07-23 20:30:16,020 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\himalaya.ttf', name='Microsoft Himalaya', style='normal', variant='normal', weight=400, stretch='normal', size='scalable')) = 10.05
2025-07-23 20:30:16,020 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\TCBI____.TTF', name='Tw Cen MT', style='italic', variant='normal', weight=700, stretch='normal', size='scalable')) = 11.335
2025-07-23 20:30:16,020 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\leelawdb.ttf', name='Leelawadee', style='normal', variant='normal', weight=700, stretch='normal', size='scalable')) = 10.335
2025-07-23 20:30:16,020 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\ariali.ttf', name='Arial', style='italic', variant='normal', weight=400, stretch='normal', size='scalable')) = 1.05
2025-07-23 20:30:16,020 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\LSANS.TTF', name='Lucida Sans', style='normal', variant='normal', weight=400, stretch='normal', size='scalable')) = 10.05
2025-07-23 20:30:16,020 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\iskpota.ttf', name='Iskoola Pota', style='normal', variant='normal', weight=400, stretch='normal', size='scalable')) = 10.05
2025-07-23 20:30:16,020 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\raavi.ttf', name='Raavi', style='normal', variant='normal', weight=400, stretch='normal', size='scalable')) = 10.05
2025-07-23 20:30:16,020 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\ArialNovaCond-LightItalic.ttf', name='Arial Nova', style='italic', variant='normal', weight=300, stretch='condensed', size='scalable')) = 11.344999999999999
2025-07-23 20:30:16,020 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\PLAYBILL.TTF', name='Playbill', style='normal', variant='normal', weight=400, stretch='normal', size='scalable')) = 10.05
2025-07-23 20:30:16,020 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\vrindab.ttf', name='Vrinda', style='normal', variant='normal', weight=700, stretch='normal', size='scalable')) = 10.335
2025-07-23 20:30:16,020 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\LSANSD.TTF', name='Lucida Sans', style='normal', variant='normal', weight=600, stretch='normal', size='scalable')) = 10.24
2025-07-23 20:30:16,020 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\seguili.ttf', name='Segoe UI', style='italic', variant='normal', weight=300, stretch='normal', size='scalable')) = 11.145
2025-07-23 20:30:16,020 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\estre.ttf', name='Estrangelo Edessa', style='normal', variant='normal', weight=400, stretch='normal', size='scalable')) = 10.05
2025-07-23 20:30:16,020 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\upckl.ttf', name='KodchiangUPC', style='normal', variant='normal', weight=400, stretch='normal', size='scalable')) = 10.05
2025-07-23 20:30:16,020 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\arialbd.ttf', name='Arial', style='normal', variant='normal', weight=700, stretch='normal', size='scalable')) = 0.33499999999999996
2025-07-23 20:30:16,020 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\verdanab.ttf', name='Verdana', style='normal', variant='normal', weight=700, stretch='normal', size='scalable')) = 10.335
2025-07-23 20:30:16,020 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\Dengl.ttf', name='DengXian', style='normal', variant='normal', weight=300, stretch='normal', size='scalable')) = 10.145
2025-07-23 20:30:16,020 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\KhmerUIb.ttf', name='Khmer UI', style='normal', variant='normal', weight=700, stretch='normal', size='scalable')) = 10.335
2025-07-23 20:30:16,020 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\ArialNovaCond-BoldItalic.ttf', name='Arial Nova', style='italic', variant='normal', weight=700, stretch='condensed', size='scalable')) = 11.535
2025-07-23 20:30:16,020 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\meiryob.ttc', name='Meiryo', style='normal', variant='normal', weight=700, stretch='normal', size='scalable')) = 10.335
2025-07-23 20:30:16,020 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\BRLNSB.TTF', name='Berlin Sans FB', style='normal', variant='normal', weight=700, stretch='normal', size='scalable')) = 10.335
2025-07-23 20:30:16,020 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\verdanaz.ttf', name='Verdana', style='italic', variant='normal', weight=700, stretch='normal', size='scalable')) = 11.335
2025-07-23 20:30:16,020 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\GOUDOS.TTF', name='Goudy Old Style', style='normal', variant='normal', weight=400, stretch='normal', size='scalable')) = 10.05
2025-07-23 20:30:16,020 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\upcdbi.ttf', name='DilleniaUPC', style='italic', variant='normal', weight=700, stretch='normal', size='scalable')) = 11.335
2025-07-23 20:30:16,020 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\SitkaB.ttc', name='Sitka Small', style='normal', variant='normal', weight=700, stretch='normal', size='scalable')) = 10.335
2025-07-23 20:30:16,020 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\ARLRDBD.TTF', name='Arial Rounded MT Bold', style='normal', variant='normal', weight=400, stretch='normal', size='scalable')) = 10.05
2025-07-23 20:30:16,020 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\kartikab.ttf', name='Kartika', style='normal', variant='normal', weight=700, stretch='normal', size='scalable')) = 10.335
2025-07-23 20:30:16,020 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\BELLB.TTF', name='Bell MT', style='normal', variant='normal', weight=700, stretch='normal', size='scalable')) = 10.335
2025-07-23 20:30:16,020 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\wingding.ttf', name='Wingdings', style='normal', variant='normal', weight=400, stretch='normal', size='scalable')) = 10.05
2025-07-23 20:30:16,020 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\mangal.ttf', name='Mangal', style='normal', variant='normal', weight=400, stretch='normal', size='scalable')) = 10.05
2025-07-23 20:30:16,020 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\WINGDNG3.TTF', name='Wingdings 3', style='normal', variant='normal', weight=400, stretch='normal', size='scalable')) = 10.05
2025-07-23 20:30:16,020 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\RockwellNovaCond-LightItalic.ttf', name='Rockwell Nova', style='italic', variant='normal', weight=300, stretch='condensed', size='scalable')) = 11.344999999999999
2025-07-23 20:30:16,020 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\HPSimplified_LtIt.ttf', name='HP Simplified', style='italic', variant='normal', weight=300, stretch='normal', size='scalable')) = 11.145
2025-07-23 20:30:16,020 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\VerdanaPro-CondLight.ttf', name='Verdana Pro', style='normal', variant='normal', weight=300, stretch='condensed', size='scalable')) = 10.344999999999999
2025-07-23 20:30:16,020 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\cambria.ttc', name='Cambria', style='normal', variant='normal', weight=400, stretch='normal', size='scalable')) = 10.05
2025-07-23 20:30:16,020 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\Candarai.ttf', name='Candara', style='italic', variant='normal', weight=400, stretch='normal', size='scalable')) = 11.05
2025-07-23 20:30:16,020 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\CALIST.TTF', name='Calisto MT', style='normal', variant='normal', weight=400, stretch='normal', size='scalable')) = 10.05
2025-07-23 20:30:16,020 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\ebrimabd.ttf', name='Ebrima', style='normal', variant='normal', weight=700, stretch='normal', size='scalable')) = 10.335
2025-07-23 20:30:16,020 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\CALIFB.TTF', name='Californian FB', style='normal', variant='normal', weight=700, stretch='normal', size='scalable')) = 10.335
2025-07-23 20:30:16,020 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\framd.ttf', name='Franklin Gothic Medium', style='normal', variant='normal', weight=400, stretch='normal', size='scalable')) = 10.05
2025-07-23 20:30:16,020 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\constani.ttf', name='Constantia', style='italic', variant='normal', weight=400, stretch='normal', size='scalable')) = 11.05
2025-07-23 20:30:16,020 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\LTYPE.TTF', name='Lucida Sans Typewriter', style='normal', variant='normal', weight=400, stretch='normal', size='scalable')) = 10.05
2025-07-23 20:30:16,020 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\LFAXD.TTF', name='Lucida Fax', style='normal', variant='normal', weight=600, stretch='normal', size='scalable')) = 10.24
2025-07-23 20:30:16,020 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\symbol.ttf', name='Symbol', style='normal', variant='normal', weight=400, stretch='normal', size='scalable')) = 10.05
2025-07-23 20:30:16,020 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\BIZ-UDGothicB.ttc', name='BIZ UDGothic', style='normal', variant='normal', weight=700, stretch='normal', size='scalable')) = 10.335
2025-07-23 20:30:16,020 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\VerdanaPro-Italic.ttf', name='Verdana Pro', style='italic', variant='normal', weight=400, stretch='normal', size='scalable')) = 11.05
2025-07-23 20:30:16,020 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\corbell.ttf', name='Corbel', style='normal', variant='normal', weight=300, stretch='normal', size='scalable')) = 10.145
2025-07-23 20:30:16,020 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\simpbdo.ttf', name='Simplified Arabic', style='normal', variant='normal', weight=700, stretch='normal', size='scalable')) = 10.335
2025-07-23 20:30:16,020 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\PER_____.TTF', name='Perpetua', style='normal', variant='normal', weight=400, stretch='normal', size='scalable')) = 10.05
2025-07-23 20:30:16,020 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\LaoUIb.ttf', name='Lao UI', style='normal', variant='normal', weight=700, stretch='normal', size='scalable')) = 10.335
2025-07-23 20:30:16,020 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\FELIXTI.TTF', name='Felix Titling', style='normal', variant='normal', weight=400, stretch='normal', size='scalable')) = 10.05
2025-07-23 20:30:16,020 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\trebuc.ttf', name='Trebuchet MS', style='normal', variant='normal', weight=400, stretch='normal', size='scalable')) = 10.05
2025-07-23 20:30:16,020 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\HTOWERTI.TTF', name='High Tower Text', style='italic', variant='normal', weight=400, stretch='normal', size='scalable')) = 11.05
2025-07-23 20:30:16,020 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\BIZ-UDMinchoM.ttc', name='BIZ UDMincho', style='normal', variant='normal', weight=500, stretch='normal', size='scalable')) = 10.145
2025-07-23 20:30:16,020 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\UrdTypeb.ttf', name='Urdu Typesetting', style='normal', variant='normal', weight=700, stretch='normal', size='scalable')) = 10.335
2025-07-23 20:30:16,020 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\VerdanaPro-CondBlack.ttf', name='Verdana Pro', style='normal', variant='normal', weight=900, stretch='condensed', size='scalable')) = 10.725
2025-07-23 20:30:16,020 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\Candarali.ttf', name='Candara', style='italic', variant='normal', weight=400, stretch='normal', size='scalable')) = 11.05
2025-07-23 20:30:16,020 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\ROCKB.TTF', name='Rockwell', style='normal', variant='normal', weight=700, stretch='normal', size='scalable')) = 10.335
2025-07-23 20:30:16,020 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\msyi.ttf', name='Microsoft Yi Baiti', style='normal', variant='normal', weight=400, stretch='normal', size='scalable')) = 10.05
2025-07-23 20:30:16,020 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\comici.ttf', name='Comic Sans MS', style='italic', variant='normal', weight=400, stretch='normal', size='scalable')) = 11.05
2025-07-23 20:30:16,020 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\cour.ttf', name='Courier New', style='normal', variant='normal', weight=400, stretch='normal', size='scalable')) = 10.05
2025-07-23 20:30:16,020 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\CENTAUR.TTF', name='Centaur', style='normal', variant='normal', weight=400, stretch='normal', size='scalable')) = 10.05
2025-07-23 20:30:16,020 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\VerdanaPro-CondBold.ttf', name='Verdana Pro', style='normal', variant='normal', weight=700, stretch='condensed', size='scalable')) = 10.535
2025-07-23 20:30:16,035 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\GOUDOSB.TTF', name='Goudy Old Style', style='normal', variant='normal', weight=700, stretch='normal', size='scalable')) = 10.335
2025-07-23 20:30:16,035 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\MATURASC.TTF', name='Matura MT Script Capitals', style='normal', variant='normal', weight=400, stretch='normal', size='scalable')) = 10.05
2025-07-23 20:30:16,035 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\FRAHV.TTF', name='Franklin Gothic Heavy', style='normal', variant='normal', weight=400, stretch='normal', size='scalable')) = 10.05
2025-07-23 20:30:16,035 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\corbelb.ttf', name='Corbel', style='normal', variant='normal', weight=700, stretch='normal', size='scalable')) = 10.335
2025-07-23 20:30:16,035 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\ArialNova-Bold.ttf', name='Arial Nova', style='normal', variant='normal', weight=700, stretch='normal', size='scalable')) = 10.335
2025-07-23 20:30:16,035 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\timesbd.ttf', name='Times New Roman', style='normal', variant='normal', weight=700, stretch='normal', size='scalable')) = 10.335
2025-07-23 20:30:16,035 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\LSANSI.TTF', name='Lucida Sans', style='italic', variant='normal', weight=400, stretch='normal', size='scalable')) = 11.05
2025-07-23 20:30:16,035 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\utsaahi.ttf', name='Utsaah', style='italic', variant='normal', weight=400, stretch='normal', size='scalable')) = 11.05
2025-07-23 20:30:16,035 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\georgiaz.ttf', name='Georgia', style='italic', variant='normal', weight=700, stretch='normal', size='scalable')) = 11.335
2025-07-23 20:30:16,035 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\GillSansCondBoNova.ttf', name='Gill Sans Nova', style='normal', variant='normal', weight=700, stretch='condensed', size='scalable')) = 10.535
2025-07-23 20:30:16,035 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\lvnm.ttf', name='Levenim MT', style='normal', variant='normal', weight=400, stretch='normal', size='scalable')) = 10.05
2025-07-23 20:30:16,035 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\FRABKIT.TTF', name='Franklin Gothic Book', style='italic', variant='normal', weight=400, stretch='normal', size='scalable')) = 11.05
2025-07-23 20:30:16,035 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\PERBI___.TTF', name='Perpetua', style='italic', variant='normal', weight=700, stretch='normal', size='scalable')) = 11.335
2025-07-23 20:30:16,035 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\Vanib.ttf', name='Vani', style='normal', variant='normal', weight=700, stretch='normal', size='scalable')) = 10.335
2025-07-23 20:30:16,035 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\segoesc.ttf', name='Segoe Script', style='normal', variant='normal', weight=400, stretch='normal', size='scalable')) = 10.05
2025-07-23 20:30:16,035 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\TCMI____.TTF', name='Tw Cen MT', style='italic', variant='normal', weight=400, stretch='normal', size='scalable')) = 11.05
2025-07-23 20:30:16,035 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\BELLI.TTF', name='Bell MT', style='italic', variant='normal', weight=400, stretch='normal', size='scalable')) = 11.05
2025-07-23 20:30:16,035 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\javatext.ttf', name='Javanese Text', style='normal', variant='normal', weight=400, stretch='normal', size='scalable')) = 10.05
2025-07-23 20:30:16,035 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\HPSimplified.ttf', name='HP Simplified', style='normal', variant='normal', weight=700, stretch='normal', size='scalable')) = 10.335
2025-07-23 20:30:16,035 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\HPSimplified_Lt.ttf', name='HP Simplified', style='normal', variant='normal', weight=300, stretch='normal', size='scalable')) = 10.145
2025-07-23 20:30:16,035 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\FRADM.TTF', name='Franklin Gothic Demi', style='normal', variant='normal', weight=400, stretch='normal', size='scalable')) = 10.05
2025-07-23 20:30:16,035 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\TCCM____.TTF', name='Tw Cen MT Condensed', style='normal', variant='normal', weight=400, stretch='condensed', size='scalable')) = 10.25
2025-07-23 20:30:16,035 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\constan.ttf', name='Constantia', style='normal', variant='normal', weight=400, stretch='normal', size='scalable')) = 10.05
2025-07-23 20:30:16,035 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\HPSimplified_BdIt.ttf', name='HP Simplified', style='italic', variant='normal', weight=700, stretch='normal', size='scalable')) = 11.335
2025-07-23 20:30:16,035 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\Candaraz.ttf', name='Candara', style='italic', variant='normal', weight=700, stretch='normal', size='scalable')) = 11.335
2025-07-23 20:30:16,035 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\ArialNovaCond.ttf', name='Arial Nova', style='normal', variant='normal', weight=400, stretch='condensed', size='scalable')) = 10.25
2025-07-23 20:30:16,035 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\Sitka.ttc', name='Sitka Small', style='normal', variant='normal', weight=400, stretch='normal', size='scalable')) = 10.05
2025-07-23 20:30:16,035 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\consolab.ttf', name='Consolas', style='normal', variant='normal', weight=700, stretch='normal', size='scalable')) = 10.335
2025-07-23 20:30:16,035 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\NHaasGroteskTXPro-66MdIt.ttf', name='Neue Haas Grotesk Text Pro', style='italic', variant='normal', weight=500, stretch='normal', size='scalable')) = 11.145
2025-07-23 20:30:16,035 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\PERTILI.TTF', name='Perpetua Titling MT', style='normal', variant='normal', weight=300, stretch='normal', size='scalable')) = 10.145
2025-07-23 20:30:16,035 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\leelawad.ttf', name='Leelawadee', style='normal', variant='normal', weight=400, stretch='normal', size='scalable')) = 10.05
2025-07-23 20:30:16,035 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\VerdanaPro-CondLightItalic.ttf', name='Verdana Pro', style='italic', variant='normal', weight=300, stretch='condensed', size='scalable')) = 11.344999999999999
2025-07-23 20:30:16,035 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\VerdanaPro-CondItalic.ttf', name='Verdana Pro', style='italic', variant='normal', weight=400, stretch='condensed', size='scalable')) = 11.25
2025-07-23 20:30:16,035 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\calibrib.ttf', name='Calibri', style='normal', variant='normal', weight=700, stretch='normal', size='scalable')) = 10.335
2025-07-23 20:30:16,035 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\vrinda.ttf', name='Vrinda', style='normal', variant='normal', weight=400, stretch='normal', size='scalable')) = 10.05
2025-07-23 20:30:16,035 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\BOD_PSTC.TTF', name='Bodoni MT', style='normal', variant='normal', weight=300, stretch='normal', size='scalable')) = 10.145
2025-07-23 20:30:16,035 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\segoescb.ttf', name='Segoe Script', style='normal', variant='normal', weight=700, stretch='normal', size='scalable')) = 10.335
2025-07-23 20:30:16,035 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\trebucbd.ttf', name='Trebuchet MS', style='normal', variant='normal', weight=700, stretch='normal', size='scalable')) = 10.335
2025-07-23 20:30:16,035 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\KhmerUI.ttf', name='Khmer UI', style='normal', variant='normal', weight=400, stretch='normal', size='scalable')) = 10.05
2025-07-23 20:30:16,035 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\AGENCYB.TTF', name='Agency FB', style='normal', variant='normal', weight=700, stretch='normal', size='scalable')) = 10.335
2025-07-23 20:30:16,035 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\BOD_BLAR.TTF', name='Bodoni MT', style='normal', variant='normal', weight=900, stretch='normal', size='scalable')) = 10.525
2025-07-23 20:30:16,035 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\upclb.ttf', name='LilyUPC', style='normal', variant='normal', weight=700, stretch='normal', size='scalable')) = 10.335
2025-07-23 20:30:16,035 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\seguisym.ttf', name='Segoe UI Symbol', style='normal', variant='normal', weight=400, stretch='normal', size='scalable')) = 10.05
2025-07-23 20:30:16,035 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\couri.ttf', name='Courier New', style='italic', variant='normal', weight=400, stretch='normal', size='scalable')) = 11.05
2025-07-23 20:30:16,035 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\angsana.ttc', name='Angsana New', style='normal', variant='normal', weight=400, stretch='normal', size='scalable')) = 10.05
2025-07-23 20:30:16,035 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\lathab.ttf', name='Latha', style='normal', variant='normal', weight=700, stretch='normal', size='scalable')) = 10.335
2025-07-23 20:30:16,035 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\GillSansBoNova.ttf', name='Gill Sans Nova', style='normal', variant='normal', weight=700, stretch='normal', size='scalable')) = 10.335
2025-07-23 20:30:16,035 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\GeorgiaPro-Light.ttf', name='Georgia Pro', style='normal', variant='normal', weight=300, stretch='normal', size='scalable')) = 10.145
2025-07-23 20:30:16,035 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\ELEPHNTI.TTF', name='Elephant', style='italic', variant='normal', weight=400, stretch='normal', size='scalable')) = 11.05
2025-07-23 20:30:16,035 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\simhei.ttf', name='SimHei', style='normal', variant='normal', weight=400, stretch='normal', size='scalable')) = 10.05
2025-07-23 20:30:16,035 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\LeelUIsl.ttf', name='Leelawadee UI', style='normal', variant='normal', weight=350, stretch='normal', size='scalable')) = 10.0975
2025-07-23 20:30:16,035 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\CURLZ___.TTF', name='Curlz MT', style='normal', variant='normal', weight=400, stretch='normal', size='scalable')) = 10.05
2025-07-23 20:30:16,035 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\upcil.ttf', name='IrisUPC', style='normal', variant='normal', weight=400, stretch='normal', size='scalable')) = 10.05
2025-07-23 20:30:16,035 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\BRITANIC.TTF', name='Britannic Bold', style='normal', variant='normal', weight=400, stretch='normal', size='scalable')) = 10.05
2025-07-23 20:30:16,035 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\aparaj.ttf', name='Aparajita', style='normal', variant='normal', weight=400, stretch='normal', size='scalable')) = 10.05
2025-07-23 20:30:16,035 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\LeelawUI.ttf', name='Leelawadee UI', style='normal', variant='normal', weight=400, stretch='normal', size='scalable')) = 10.05
2025-07-23 20:30:16,035 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\upcei.ttf', name='EucrosiaUPC', style='italic', variant='normal', weight=400, stretch='normal', size='scalable')) = 11.05
2025-07-23 20:30:16,035 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\seguiemj.ttf', name='Segoe UI Emoji', style='normal', variant='normal', weight=400, stretch='normal', size='scalable')) = 10.05
2025-07-23 20:30:16,035 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\GeorgiaPro-CondBoldItalic.ttf', name='Georgia Pro', style='italic', variant='normal', weight=700, stretch='condensed', size='scalable')) = 11.535
2025-07-23 20:30:16,035 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\iskpotab.ttf', name='Iskoola Pota', style='normal', variant='normal', weight=700, stretch='normal', size='scalable')) = 10.335
2025-07-23 20:30:16,035 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\LeelaUIb.ttf', name='Leelawadee UI', style='normal', variant='normal', weight=700, stretch='normal', size='scalable')) = 10.335
2025-07-23 20:30:16,035 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\GARAIT.TTF', name='Garamond', style='italic', variant='normal', weight=400, stretch='normal', size='scalable')) = 11.05
2025-07-23 20:30:16,035 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\mriam.ttf', name='Miriam', style='normal', variant='normal', weight=400, stretch='normal', size='scalable')) = 10.05
2025-07-23 20:30:16,035 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\LHANDW.TTF', name='Lucida Handwriting', style='italic', variant='normal', weight=400, stretch='normal', size='scalable')) = 11.05
2025-07-23 20:30:16,035 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\timesbi.ttf', name='Times New Roman', style='italic', variant='normal', weight=700, stretch='normal', size='scalable')) = 11.335
2025-07-23 20:30:16,035 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\sylfaen.ttf', name='Sylfaen', style='normal', variant='normal', weight=400, stretch='normal', size='scalable')) = 10.05
2025-07-23 20:30:16,035 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\ROCCB___.TTF', name='Rockwell Condensed', style='normal', variant='normal', weight=700, stretch='condensed', size='scalable')) = 10.535
2025-07-23 20:30:16,035 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\RockwellNovaCond-Italic.ttf', name='Rockwell Nova', style='italic', variant='normal', weight=400, stretch='condensed', size='scalable')) = 11.25
2025-07-23 20:30:16,035 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\upcjb.ttf', name='JasmineUPC', style='normal', variant='normal', weight=700, stretch='normal', size='scalable')) = 10.335
2025-07-23 20:30:16,035 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\HPSimplified_Rg.ttf', name='HP Simplified', style='normal', variant='normal', weight=400, stretch='normal', size='scalable')) = 10.05
2025-07-23 20:30:16,035 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\timesi.ttf', name='Times New Roman', style='italic', variant='normal', weight=400, stretch='normal', size='scalable')) = 11.05
2025-07-23 20:30:16,035 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\gautamib.ttf', name='Gautami', style='normal', variant='normal', weight=700, stretch='normal', size='scalable')) = 10.335
2025-07-23 20:30:16,035 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\comicbd.ttf', name='Comic Sans MS', style='normal', variant='normal', weight=700, stretch='normal', size='scalable')) = 10.335
2025-07-23 20:30:16,035 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\yumin.ttf', name='Yu Mincho', style='normal', variant='normal', weight=400, stretch='normal', size='scalable')) = 10.05
2025-07-23 20:30:16,035 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\taileb.ttf', name='Microsoft Tai Le', style='normal', variant='normal', weight=700, stretch='normal', size='scalable')) = 10.335
2025-07-23 20:30:16,035 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\ENGR.TTF', name='Engravers MT', style='normal', variant='normal', weight=500, stretch='normal', size='scalable')) = 10.145
2025-07-23 20:30:16,035 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\lucon.ttf', name='Lucida Console', style='normal', variant='normal', weight=400, stretch='normal', size='scalable')) = 10.05
2025-07-23 20:30:16,035 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\GILBI___.TTF', name='Gill Sans MT', style='italic', variant='normal', weight=700, stretch='normal', size='scalable')) = 11.335
2025-07-23 20:30:16,035 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\msgothic.ttc', name='MS Gothic', style='normal', variant='normal', weight=400, stretch='normal', size='scalable')) = 10.05
2025-07-23 20:30:16,035 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\ArialNova-Italic.ttf', name='Arial Nova', style='italic', variant='normal', weight=400, stretch='normal', size='scalable')) = 11.05
2025-07-23 20:30:16,035 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\trebucit.ttf', name='Trebuchet MS', style='italic', variant='normal', weight=400, stretch='normal', size='scalable')) = 11.05
2025-07-23 20:30:16,035 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\segmdl2.ttf', name='Segoe MDL2 Assets', style='normal', variant='normal', weight=400, stretch='normal', size='scalable')) = 10.05
2025-07-23 20:30:16,035 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\Candarab.ttf', name='Candara', style='normal', variant='normal', weight=700, stretch='normal', size='scalable')) = 10.335
2025-07-23 20:30:16,035 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\kaiu.ttf', name='DFKai-SB', style='normal', variant='normal', weight=400, stretch='normal', size='scalable')) = 10.05
2025-07-23 20:30:16,035 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\GeorgiaPro-CondBlackItalic.ttf', name='Georgia Pro', style='italic', variant='normal', weight=900, stretch='condensed', size='scalable')) = 11.725
2025-07-23 20:30:16,035 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\malgunsl.ttf', name='Malgun Gothic', style='normal', variant='normal', weight=300, stretch='normal', size='scalable')) = 10.145
2025-07-23 20:30:16,035 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\RockwellNova-Light.ttf', name='Rockwell Nova', style='normal', variant='normal', weight=300, stretch='normal', size='scalable')) = 10.145
2025-07-23 20:30:16,035 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\utsaahbi.ttf', name='Utsaah', style='italic', variant='normal', weight=700, stretch='normal', size='scalable')) = 11.335
2025-07-23 20:30:16,035 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\moolbor.ttf', name='MoolBoran', style='normal', variant='normal', weight=400, stretch='normal', size='scalable')) = 10.05
2025-07-23 20:30:16,035 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\GeorgiaPro-BlackItalic.ttf', name='Georgia Pro', style='italic', variant='normal', weight=900, stretch='normal', size='scalable')) = 11.525
2025-07-23 20:30:16,035 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\palabi.ttf', name='Palatino Linotype', style='italic', variant='normal', weight=700, stretch='normal', size='scalable')) = 11.335
2025-07-23 20:30:16,035 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\pala.ttf', name='Palatino Linotype', style='normal', variant='normal', weight=400, stretch='normal', size='scalable')) = 10.05
2025-07-23 20:30:16,035 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\NHaasGroteskTXPro-56It.ttf', name='Neue Haas Grotesk Text Pro', style='italic', variant='normal', weight=400, stretch='normal', size='scalable')) = 11.05
2025-07-23 20:30:16,035 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\HATTEN.TTF', name='Haettenschweiler', style='normal', variant='normal', weight=400, stretch='normal', size='scalable')) = 10.05
2025-07-23 20:30:16,035 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\BOD_CB.TTF', name='Bodoni MT', style='normal', variant='normal', weight=700, stretch='condensed', size='scalable')) = 10.535
2025-07-23 20:30:16,035 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\CALISTI.TTF', name='Calisto MT', style='italic', variant='normal', weight=400, stretch='normal', size='scalable')) = 11.05
2025-07-23 20:30:16,035 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\NHaasGroteskTXPro-55Rg.ttf', name='Neue Haas Grotesk Text Pro', style='normal', variant='normal', weight=400, stretch='normal', size='scalable')) = 10.05
2025-07-23 20:30:16,035 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\kokilab.ttf', name='Kokila', style='normal', variant='normal', weight=700, stretch='normal', size='scalable')) = 10.335
2025-07-23 20:30:16,035 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\BKANT.TTF', name='Book Antiqua', style='normal', variant='normal', weight=400, stretch='normal', size='scalable')) = 10.05
2025-07-23 20:30:16,035 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\ROCKBI.TTF', name='Rockwell', style='italic', variant='normal', weight=700, stretch='normal', size='scalable')) = 11.335
2025-07-23 20:30:16,035 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\BOD_CBI.TTF', name='Bodoni MT', style='italic', variant='normal', weight=700, stretch='condensed', size='scalable')) = 11.535
2025-07-23 20:30:16,035 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\Nirmala.ttf', name='Nirmala UI', style='normal', variant='normal', weight=400, stretch='normal', size='scalable')) = 10.05
2025-07-23 20:30:16,035 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\malgunbd.ttf', name='Malgun Gothic', style='normal', variant='normal', weight=700, stretch='normal', size='scalable')) = 10.335
2025-07-23 20:30:16,035 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\SitkaI.ttc', name='Sitka Small', style='italic', variant='normal', weight=400, stretch='normal', size='scalable')) = 11.05
2025-07-23 20:30:16,066 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\ERASBD.TTF', name='Eras Bold ITC', style='normal', variant='normal', weight=400, stretch='normal', size='scalable')) = 10.05
2025-07-23 20:30:16,066 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\seguisb.ttf', name='Segoe UI', style='normal', variant='normal', weight=600, stretch='normal', size='scalable')) = 10.24
2025-07-23 20:30:16,066 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\simpfxo.ttf', name='Simplified Arabic Fixed', style='normal', variant='normal', weight=400, stretch='normal', size='scalable')) = 10.05
2025-07-23 20:30:16,066 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\SCRIPTBL.TTF', name='Script MT Bold', style='normal', variant='normal', weight=700, stretch='normal', size='scalable')) = 10.335
2025-07-23 20:30:16,066 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\Deng.ttf', name='DengXian', style='normal', variant='normal', weight=400, stretch='normal', size='scalable')) = 10.05
2025-07-23 20:30:16,066 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\Vani.ttf', name='Vani', style='normal', variant='normal', weight=400, stretch='normal', size='scalable')) = 10.05
2025-07-23 20:30:16,066 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\comic.ttf', name='Comic Sans MS', style='normal', variant='normal', weight=400, stretch='normal', size='scalable')) = 10.05
2025-07-23 20:30:16,066 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\msyh.ttc', name='Microsoft YaHei', style='normal', variant='normal', weight=400, stretch='normal', size='scalable')) = 10.05
2025-07-23 20:30:16,066 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\BRUSHSCI.TTF', name='Brush Script MT', style='italic', variant='normal', weight=400, stretch='normal', size='scalable')) = 11.05
2025-07-23 20:30:16,066 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\GOTHIC.TTF', name='Century Gothic', style='normal', variant='normal', weight=400, stretch='normal', size='scalable')) = 10.05
2025-07-23 20:30:16,066 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\GOTHICB.TTF', name='Century Gothic', style='normal', variant='normal', weight=700, stretch='normal', size='scalable')) = 10.335
2025-07-23 20:30:16,066 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\msuighub.ttf', name='Microsoft Uighur', style='normal', variant='normal', weight=700, stretch='normal', size='scalable')) = 10.335
2025-07-23 20:30:16,066 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\kokila.ttf', name='Kokila', style='normal', variant='normal', weight=400, stretch='normal', size='scalable')) = 10.05
2025-07-23 20:30:16,066 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\VerdanaPro-CondSemiBoldItalic.ttf', name='Verdana Pro', style='italic', variant='normal', weight=600, stretch='condensed', size='scalable')) = 11.44
2025-07-23 20:30:16,066 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\browalia.ttc', name='Browallia New', style='normal', variant='normal', weight=400, stretch='normal', size='scalable')) = 10.05
2025-07-23 20:30:16,066 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\SCHLBKI.TTF', name='Century Schoolbook', style='italic', variant='normal', weight=400, stretch='normal', size='scalable')) = 11.05
2025-07-23 20:30:16,066 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\PERB____.TTF', name='Perpetua', style='normal', variant='normal', weight=700, stretch='normal', size='scalable')) = 10.335
2025-07-23 20:30:16,066 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\RockwellNovaCond-Bold.ttf', name='Rockwell Nova', style='normal', variant='normal', weight=700, stretch='condensed', size='scalable')) = 10.535
2025-07-23 20:30:16,066 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\ELEPHNT.TTF', name='Elephant', style='normal', variant='normal', weight=400, stretch='normal', size='scalable')) = 10.05
2025-07-23 20:30:16,066 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\NIAGENG.TTF', name='Niagara Engraved', style='normal', variant='normal', weight=400, stretch='normal', size='scalable')) = 10.05
2025-07-23 20:30:16,066 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\courbi.ttf', name='Courier New', style='italic', variant='normal', weight=700, stretch='normal', size='scalable')) = 11.335
2025-07-23 20:30:16,066 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\impact.ttf', name='Impact', style='normal', variant='normal', weight=400, stretch='normal', size='scalable')) = 10.05
2025-07-23 20:30:16,066 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\trebucbi.ttf', name='Trebuchet MS', style='italic', variant='normal', weight=700, stretch='normal', size='scalable')) = 11.335
2025-07-23 20:30:16,066 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\upcdb.ttf', name='DilleniaUPC', style='normal', variant='normal', weight=700, stretch='normal', size='scalable')) = 10.335
2025-07-23 20:30:16,066 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\LTYPEB.TTF', name='Lucida Sans Typewriter', style='normal', variant='normal', weight=600, stretch='normal', size='scalable')) = 10.24
2025-07-23 20:30:16,066 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\NHaasGroteskTXPro-75Bd.ttf', name='Neue Haas Grotesk Text Pro', style='normal', variant='normal', weight=700, stretch='normal', size='scalable')) = 10.335
2025-07-23 20:30:16,066 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\BRADHITC.TTF', name='Bradley Hand ITC', style='normal', variant='normal', weight=400, stretch='normal', size='scalable')) = 10.05
2025-07-23 20:30:16,066 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\GeorgiaPro-BoldItalic.ttf', name='Georgia Pro', style='italic', variant='normal', weight=700, stretch='normal', size='scalable')) = 11.335
2025-07-23 20:30:16,066 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\palab.ttf', name='Palatino Linotype', style='normal', variant='normal', weight=700, stretch='normal', size='scalable')) = 10.335
2025-07-23 20:30:16,066 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\malgun.ttf', name='Malgun Gothic', style='normal', variant='normal', weight=400, stretch='normal', size='scalable')) = 10.05
2025-07-23 20:30:16,066 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\NirmalaS.ttf', name='Nirmala UI', style='normal', variant='normal', weight=350, stretch='normal', size='scalable')) = 10.0975
2025-07-23 20:30:16,066 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\upcii.ttf', name='IrisUPC', style='italic', variant='normal', weight=400, stretch='normal', size='scalable')) = 11.05
2025-07-23 20:30:16,066 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\calibriz.ttf', name='Calibri', style='italic', variant='normal', weight=700, stretch='normal', size='scalable')) = 11.335
2025-07-23 20:30:16,066 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\tahomabd.ttf', name='Tahoma', style='normal', variant='normal', weight=700, stretch='normal', size='scalable')) = 10.335
2025-07-23 20:30:16,066 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\msmincho.ttc', name='MS Mincho', style='normal', variant='normal', weight=400, stretch='normal', size='scalable')) = 10.05
2025-07-23 20:30:16,066 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\segoeuii.ttf', name='Segoe UI', style='italic', variant='normal', weight=400, stretch='normal', size='scalable')) = 11.05
2025-07-23 20:30:16,066 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\GARA.TTF', name='Garamond', style='normal', variant='normal', weight=400, stretch='normal', size='scalable')) = 10.05
2025-07-23 20:30:16,066 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\STENCIL.TTF', name='Stencil', style='normal', variant='normal', weight=400, stretch='normal', size='scalable')) = 10.05
2025-07-23 20:30:16,066 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\ebrima.ttf', name='Ebrima', style='normal', variant='normal', weight=400, stretch='normal', size='scalable')) = 10.05
2025-07-23 20:30:16,066 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\tahoma.ttf', name='Tahoma', style='normal', variant='normal', weight=400, stretch='normal', size='scalable')) = 10.05
2025-07-23 20:30:16,066 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\CASTELAR.TTF', name='Castellar', style='normal', variant='normal', weight=400, stretch='normal', size='scalable')) = 10.05
2025-07-23 20:30:16,066 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\CENTURY.TTF', name='Century', style='normal', variant='normal', weight=400, stretch='normal', size='scalable')) = 10.05
2025-07-23 20:30:16,066 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\segoeuiz.ttf', name='Segoe UI', style='italic', variant='normal', weight=700, stretch='normal', size='scalable')) = 11.335
2025-07-23 20:30:16,066 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\GeorgiaPro-SemiBoldItalic.ttf', name='Georgia Pro', style='italic', variant='normal', weight=600, stretch='normal', size='scalable')) = 11.24
2025-07-23 20:30:16,066 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\GILLUBCD.TTF', name='Gill Sans Ultra Bold Condensed', style='normal', variant='normal', weight=400, stretch='condensed', size='scalable')) = 10.25
2025-07-23 20:30:16,066 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\upcfb.ttf', name='FreesiaUPC', style='normal', variant='normal', weight=700, stretch='normal', size='scalable')) = 10.335
2025-07-23 20:30:16,066 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\GillSansCondLightItNova.ttf', name='Gill Sans Nova', style='italic', variant='normal', weight=300, stretch='condensed', size='scalable')) = 11.344999999999999
2025-07-23 20:30:16,066 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\VerdanaPro-Black.ttf', name='Verdana Pro', style='normal', variant='normal', weight=900, stretch='normal', size='scalable')) = 10.525
2025-07-23 20:30:16,066 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\arialnbi.ttf', name='Arial', style='italic', variant='normal', weight=700, stretch='condensed', size='scalable')) = 1.535
2025-07-23 20:30:16,066 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\upcibi.ttf', name='IrisUPC', style='italic', variant='normal', weight=700, stretch='normal', size='scalable')) = 11.335
2025-07-23 20:30:16,066 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\BOOKOS.TTF', name='Bookman Old Style', style='normal', variant='normal', weight=300, stretch='normal', size='scalable')) = 10.145
2025-07-23 20:30:16,066 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\GillSansCondLightNova.ttf', name='Gill Sans Nova', style='normal', variant='normal', weight=300, stretch='condensed', size='scalable')) = 10.344999999999999
2025-07-23 20:30:16,066 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\BELL.TTF', name='Bell MT', style='normal', variant='normal', weight=400, stretch='normal', size='scalable')) = 10.05
2025-07-23 20:30:16,066 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\OUTLOOK.TTF', name='MS Outlook', style='normal', variant='normal', weight=400, stretch='normal', size='scalable')) = 10.05
2025-07-23 20:30:16,066 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\ITCKRIST.TTF', name='Kristen ITC', style='normal', variant='normal', weight=400, stretch='normal', size='scalable')) = 10.05
2025-07-23 20:30:16,066 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\simkai.ttf', name='KaiTi', style='normal', variant='normal', weight=400, stretch='normal', size='scalable')) = 10.05
2025-07-23 20:30:16,066 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\aldhabi.ttf', name='Aldhabi', style='normal', variant='normal', weight=400, stretch='normal', size='scalable')) = 10.05
2025-07-23 20:30:16,066 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\FREESCPT.TTF', name='Freestyle Script', style='normal', variant='normal', weight=400, stretch='normal', size='scalable')) = 10.05
2025-07-23 20:30:16,066 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\PERI____.TTF', name='Perpetua', style='italic', variant='normal', weight=400, stretch='normal', size='scalable')) = 11.05
2025-07-23 20:30:16,066 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\arialn.ttf', name='Arial', style='normal', variant='normal', weight=400, stretch='condensed', size='scalable')) = 0.25
2025-07-23 20:30:16,066 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\YuGothM.ttc', name='Yu Gothic', style='normal', variant='normal', weight=500, stretch='normal', size='scalable')) = 10.145
2025-07-23 20:30:16,066 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\batang.ttc', name='Batang', style='normal', variant='normal', weight=400, stretch='normal', size='scalable')) = 10.05
2025-07-23 20:30:16,066 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\meiryo.ttc', name='Meiryo', style='normal', variant='normal', weight=400, stretch='normal', size='scalable')) = 10.05
2025-07-23 20:30:16,066 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\shrutib.ttf', name='Shruti', style='normal', variant='normal', weight=700, stretch='normal', size='scalable')) = 10.335
2025-07-23 20:30:16,066 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\kokilabi.ttf', name='Kokila', style='italic', variant='normal', weight=700, stretch='normal', size='scalable')) = 11.335
2025-07-23 20:30:16,066 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\GOUDYSTO.TTF', name='Goudy Stout', style='normal', variant='normal', weight=400, stretch='normal', size='scalable')) = 10.05
2025-07-23 20:30:16,066 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\seguihis.ttf', name='Segoe UI Historic', style='normal', variant='normal', weight=400, stretch='normal', size='scalable')) = 10.05
2025-07-23 20:30:16,066 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\majallab.ttf', name='Sakkal Majalla', style='normal', variant='normal', weight=700, stretch='normal', size='scalable')) = 10.335
2025-07-23 20:30:16,066 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\GILC____.TTF', name='Gill Sans MT Condensed', style='normal', variant='normal', weight=400, stretch='condensed', size='scalable')) = 10.25
2025-07-23 20:30:16,066 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\corbelli.ttf', name='Corbel', style='italic', variant='normal', weight=300, stretch='normal', size='scalable')) = 11.145
2025-07-23 20:30:16,066 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\seguisbi.ttf', name='Segoe UI', style='italic', variant='normal', weight=600, stretch='normal', size='scalable')) = 11.24
2025-07-23 20:30:16,066 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\BOD_CI.TTF', name='Bodoni MT', style='italic', variant='normal', weight=400, stretch='condensed', size='scalable')) = 11.25
2025-07-23 20:30:16,066 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\VerdanaPro-CondSemiBold.ttf', name='Verdana Pro', style='normal', variant='normal', weight=600, stretch='condensed', size='scalable')) = 10.44
2025-07-23 20:30:16,066 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\YuGothR.ttc', name='Yu Gothic', style='normal', variant='normal', weight=400, stretch='normal', size='scalable')) = 10.05
2025-07-23 20:30:16,066 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\ONYX.TTF', name='Onyx', style='normal', variant='normal', weight=400, stretch='normal', size='scalable')) = 10.05
2025-07-23 20:30:16,066 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\ArialNova-LightItalic.ttf', name='Arial Nova', style='italic', variant='normal', weight=300, stretch='normal', size='scalable')) = 11.145
2025-07-23 20:30:16,066 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\phagspab.ttf', name='Microsoft PhagsPa', style='normal', variant='normal', weight=700, stretch='normal', size='scalable')) = 10.335
2025-07-23 20:30:16,066 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\BOD_R.TTF', name='Bodoni MT', style='normal', variant='normal', weight=400, stretch='normal', size='scalable')) = 10.05
2025-07-23 20:30:16,066 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\REFSPCL.TTF', name='MS Reference Specialty', style='normal', variant='normal', weight=400, stretch='normal', size='scalable')) = 10.05
2025-07-23 20:30:16,066 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\gulim.ttc', name='Gulim', style='normal', variant='normal', weight=400, stretch='normal', size='scalable')) = 10.05
2025-07-23 20:30:16,066 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\ANTQUAB.TTF', name='Book Antiqua', style='normal', variant='normal', weight=700, stretch='normal', size='scalable')) = 10.335
2025-07-23 20:30:16,066 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\gisha.ttf', name='Gisha', style='normal', variant='normal', weight=400, stretch='normal', size='scalable')) = 10.05
2025-07-23 20:30:16,066 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\LBRITE.TTF', name='Lucida Bright', style='normal', variant='normal', weight=400, stretch='normal', size='scalable')) = 10.05
2025-07-23 20:30:16,066 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\calibrii.ttf', name='Calibri', style='italic', variant='normal', weight=400, stretch='normal', size='scalable')) = 11.05
2025-07-23 20:30:16,066 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\LTYPEO.TTF', name='Lucida Sans Typewriter', style='oblique', variant='normal', weight=400, stretch='normal', size='scalable')) = 11.05
2025-07-23 20:30:16,066 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\webdings.ttf', name='Webdings', style='normal', variant='normal', weight=400, stretch='normal', size='scalable')) = 10.05
2025-07-23 20:30:16,066 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\WINGDNG2.TTF', name='Wingdings 2', style='normal', variant='normal', weight=400, stretch='normal', size='scalable')) = 10.05
2025-07-23 20:30:16,066 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\corbel.ttf', name='Corbel', style='normal', variant='normal', weight=400, stretch='normal', size='scalable')) = 10.05
2025-07-23 20:30:16,066 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\MAIAN.TTF', name='Maiandra GD', style='normal', variant='normal', weight=400, stretch='normal', size='scalable')) = 10.05
2025-07-23 20:30:16,066 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\YuGothL.ttc', name='Yu Gothic', style='normal', variant='normal', weight=300, stretch='normal', size='scalable')) = 10.145
2025-07-23 20:30:16,066 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\ERASLGHT.TTF', name='Eras Light ITC', style='normal', variant='normal', weight=400, stretch='normal', size='scalable')) = 10.05
2025-07-23 20:30:16,066 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\segoeuisl.ttf', name='Segoe UI', style='normal', variant='normal', weight=350, stretch='normal', size='scalable')) = 10.0975
2025-07-23 20:30:16,066 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\RockwellNova-ExtraBold.ttf', name='Rockwell Nova', style='normal', variant='normal', weight=800, stretch='normal', size='scalable')) = 10.43
2025-07-23 20:30:16,066 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\RAGE.TTF', name='Rage Italic', style='italic', variant='normal', weight=400, stretch='normal', size='scalable')) = 11.05
2025-07-23 20:30:16,066 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\GillSansLightNova.ttf', name='Gill Sans Nova', style='normal', variant='normal', weight=300, stretch='normal', size='scalable')) = 10.145
2025-07-23 20:30:16,066 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\RockwellNova-BoldItalic.ttf', name='Rockwell Nova', style='italic', variant='normal', weight=700, stretch='normal', size='scalable')) = 11.335
2025-07-23 20:30:16,066 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\calibril.ttf', name='Calibri', style='normal', variant='normal', weight=300, stretch='normal', size='scalable')) = 10.145
2025-07-23 20:30:16,066 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\NHaasGroteskTXPro-65Md.ttf', name='Neue Haas Grotesk Text Pro', style='normal', variant='normal', weight=500, stretch='normal', size='scalable')) = 10.145
2025-07-23 20:30:16,066 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\OLDENGL.TTF', name='Old English Text MT', style='normal', variant='normal', weight=400, stretch='normal', size='scalable')) = 10.05
2025-07-23 20:30:16,066 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\BIZ-UDGothicR.ttc', name='BIZ UDGothic', style='normal', variant='normal', weight=400, stretch='normal', size='scalable')) = 10.05
2025-07-23 20:30:16,066 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\trado.ttf', name='Traditional Arabic', style='normal', variant='normal', weight=400, stretch='normal', size='scalable')) = 10.05
2025-07-23 20:30:16,066 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\upcdl.ttf', name='DilleniaUPC', style='normal', variant='normal', weight=400, stretch='normal', size='scalable')) = 10.05
2025-07-23 20:30:16,066 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\GeorgiaPro-CondBold.ttf', name='Georgia Pro', style='normal', variant='normal', weight=700, stretch='condensed', size='scalable')) = 10.535
2025-07-23 20:30:16,066 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\RockwellNova-ExtraBoldItalic.ttf', name='Rockwell Nova', style='italic', variant='normal', weight=800, stretch='normal', size='scalable')) = 11.43
2025-07-23 20:30:16,082 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\SCHLBKBI.TTF', name='Century Schoolbook', style='italic', variant='normal', weight=700, stretch='normal', size='scalable')) = 11.335
2025-07-23 20:30:16,082 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\shruti.ttf', name='Shruti', style='normal', variant='normal', weight=400, stretch='normal', size='scalable')) = 10.05
2025-07-23 20:30:16,082 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\RockwellNova.ttf', name='Rockwell Nova', style='normal', variant='normal', weight=400, stretch='normal', size='scalable')) = 10.05
2025-07-23 20:30:16,082 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\ArialNovaCond-Bold.ttf', name='Arial Nova', style='normal', variant='normal', weight=700, stretch='condensed', size='scalable')) = 10.535
2025-07-23 20:30:16,082 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\Sanskr.ttf', name='Sanskrit Text', style='normal', variant='normal', weight=400, stretch='normal', size='scalable')) = 10.05
2025-07-23 20:30:16,082 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\upcfbi.ttf', name='FreesiaUPC', style='italic', variant='normal', weight=700, stretch='normal', size='scalable')) = 11.335
2025-07-23 20:30:16,082 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\gadugi.ttf', name='Gadugi', style='normal', variant='normal', weight=400, stretch='normal', size='scalable')) = 10.05
2025-07-23 20:30:16,082 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\times.ttf', name='Times New Roman', style='normal', variant='normal', weight=400, stretch='normal', size='scalable')) = 10.05
2025-07-23 20:30:16,082 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\RockwellNova-Bold.ttf', name='Rockwell Nova', style='normal', variant='normal', weight=700, stretch='normal', size='scalable')) = 10.335
2025-07-23 20:30:16,082 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\VINERITC.TTF', name='Viner Hand ITC', style='normal', variant='normal', weight=400, stretch='normal', size='scalable')) = 10.05
2025-07-23 20:30:16,082 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\gautami.ttf', name='Gautami', style='normal', variant='normal', weight=400, stretch='normal', size='scalable')) = 10.05
2025-07-23 20:30:16,082 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\raavib.ttf', name='Raavi', style='normal', variant='normal', weight=700, stretch='normal', size='scalable')) = 10.335
2025-07-23 20:30:16,082 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\arialbi.ttf', name='Arial', style='italic', variant='normal', weight=700, stretch='normal', size='scalable')) = 1.335
2025-07-23 20:30:16,082 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\SHOWG.TTF', name='Showcard Gothic', style='normal', variant='normal', weight=400, stretch='normal', size='scalable')) = 10.05
2025-07-23 20:30:16,082 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\GeorgiaPro-CondSemiBold.ttf', name='Georgia Pro', style='normal', variant='normal', weight=600, stretch='condensed', size='scalable')) = 10.44
2025-07-23 20:30:16,082 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\GeorgiaPro-Regular.ttf', name='Georgia Pro', style='normal', variant='normal', weight=400, stretch='normal', size='scalable')) = 10.05
2025-07-23 20:30:16,082 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\NHaasGroteskTXPro-76BdIt.ttf', name='Neue Haas Grotesk Text Pro', style='italic', variant='normal', weight=700, stretch='normal', size='scalable')) = 11.335
2025-07-23 20:30:16,082 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\msuighur.ttf', name='Microsoft Uighur', style='normal', variant='normal', weight=400, stretch='normal', size='scalable')) = 10.05
2025-07-23 20:30:16,082 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\LTYPEBO.TTF', name='Lucida Sans Typewriter', style='oblique', variant='normal', weight=600, stretch='normal', size='scalable')) = 11.24
2025-07-23 20:30:16,082 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\LCALLIG.TTF', name='Lucida Calligraphy', style='italic', variant='normal', weight=400, stretch='normal', size='scalable')) = 11.05
2025-07-23 20:30:16,082 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\LBRITEI.TTF', name='Lucida Bright', style='italic', variant='normal', weight=400, stretch='normal', size='scalable')) = 11.05
2025-07-23 20:30:16,082 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\mingliu.ttc', name='MingLiU', style='normal', variant='normal', weight=400, stretch='normal', size='scalable')) = 10.05
2025-07-23 20:30:16,082 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\BOOKOSBI.TTF', name='Bookman Old Style', style='italic', variant='normal', weight=600, stretch='normal', size='scalable')) = 11.24
2025-07-23 20:30:16,082 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\ERASDEMI.TTF', name='Eras Demi ITC', style='normal', variant='normal', weight=400, stretch='normal', size='scalable')) = 10.05
2025-07-23 20:30:16,082 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\constanz.ttf', name='Constantia', style='italic', variant='normal', weight=700, stretch='normal', size='scalable')) = 11.335
2025-07-23 20:30:16,082 - DEBUG - findfont: Matching Arial:style=normal:variant=normal:weight=normal:stretch=normal:size=10.0 to Arial ('C:\\Windows\\Fonts\\arial.ttf') with score of 0.050000.
2025-07-23 20:30:16,082 - DEBUG - findfont: Matching Arial:style=normal:variant=normal:weight=normal:stretch=normal:size=12.0.
2025-07-23 20:30:16,082 - DEBUG - findfont: score(FontEntry(fname='C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\site-packages\\matplotlib\\mpl-data\\fonts\\ttf\\cmss10.ttf', name='cmss10', style='normal', variant='normal', weight=400, stretch='normal', size='scalable')) = 10.05
2025-07-23 20:30:16,082 - DEBUG - findfont: score(FontEntry(fname='C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\site-packages\\matplotlib\\mpl-data\\fonts\\ttf\\DejaVuSansMono.ttf', name='DejaVu Sans Mono', style='normal', variant='normal', weight=400, stretch='normal', size='scalable')) = 10.05
2025-07-23 20:30:16,082 - DEBUG - findfont: score(FontEntry(fname='C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\site-packages\\matplotlib\\mpl-data\\fonts\\ttf\\STIXGeneral.ttf', name='STIXGeneral', style='normal', variant='normal', weight=400, stretch='normal', size='scalable')) = 10.05
2025-07-23 20:30:16,098 - DEBUG - findfont: score(FontEntry(fname='C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\site-packages\\matplotlib\\mpl-data\\fonts\\ttf\\STIXGeneralItalic.ttf', name='STIXGeneral', style='italic', variant='normal', weight=400, stretch='normal', size='scalable')) = 11.05
2025-07-23 20:30:16,098 - DEBUG - findfont: score(FontEntry(fname='C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\site-packages\\matplotlib\\mpl-data\\fonts\\ttf\\STIXNonUniBol.ttf', name='STIXNonUnicode', style='normal', variant='normal', weight=700, stretch='normal', size='scalable')) = 10.335
2025-07-23 20:30:16,098 - DEBUG - findfont: score(FontEntry(fname='C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\site-packages\\matplotlib\\mpl-data\\fonts\\ttf\\cmsy10.ttf', name='cmsy10', style='normal', variant='normal', weight=400, stretch='normal', size='scalable')) = 10.05
2025-07-23 20:30:16,098 - DEBUG - findfont: score(FontEntry(fname='C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\site-packages\\matplotlib\\mpl-data\\fonts\\ttf\\DejaVuSansMono-Oblique.ttf', name='DejaVu Sans Mono', style='oblique', variant='normal', weight=400, stretch='normal', size='scalable')) = 11.05
2025-07-23 20:30:16,098 - DEBUG - findfont: score(FontEntry(fname='C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\site-packages\\matplotlib\\mpl-data\\fonts\\ttf\\DejaVuSansMono-Bold.ttf', name='DejaVu Sans Mono', style='normal', variant='normal', weight=700, stretch='normal', size='scalable')) = 10.335
2025-07-23 20:30:16,098 - DEBUG - findfont: score(FontEntry(fname='C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\site-packages\\matplotlib\\mpl-data\\fonts\\ttf\\DejaVuSerif-Italic.ttf', name='DejaVu Serif', style='italic', variant='normal', weight=400, stretch='normal', size='scalable')) = 11.05
2025-07-23 20:30:16,098 - DEBUG - findfont: score(FontEntry(fname='C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\site-packages\\matplotlib\\mpl-data\\fonts\\ttf\\cmtt10.ttf', name='cmtt10', style='normal', variant='normal', weight=400, stretch='normal', size='scalable')) = 10.05
2025-07-23 20:30:16,098 - DEBUG - findfont: score(FontEntry(fname='C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\site-packages\\matplotlib\\mpl-data\\fonts\\ttf\\STIXSizThreeSymReg.ttf', name='STIXSizeThreeSym', style='normal', variant='normal', weight=400, stretch='normal', size='scalable')) = 10.05
2025-07-23 20:30:16,098 - DEBUG - findfont: score(FontEntry(fname='C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\site-packages\\matplotlib\\mpl-data\\fonts\\ttf\\cmex10.ttf', name='cmex10', style='normal', variant='normal', weight=400, stretch='normal', size='scalable')) = 10.05
2025-07-23 20:30:16,098 - DEBUG - findfont: score(FontEntry(fname='C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\site-packages\\matplotlib\\mpl-data\\fonts\\ttf\\STIXSizFourSymBol.ttf', name='STIXSizeFourSym', style='normal', variant='normal', weight=700, stretch='normal', size='scalable')) = 10.335
2025-07-23 20:30:16,098 - DEBUG - findfont: score(FontEntry(fname='C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\site-packages\\matplotlib\\mpl-data\\fonts\\ttf\\STIXSizFourSymReg.ttf', name='STIXSizeFourSym', style='normal', variant='normal', weight=400, stretch='normal', size='scalable')) = 10.05
2025-07-23 20:30:16,098 - DEBUG - findfont: score(FontEntry(fname='C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\site-packages\\matplotlib\\mpl-data\\fonts\\ttf\\STIXSizTwoSymBol.ttf', name='STIXSizeTwoSym', style='normal', variant='normal', weight=700, stretch='normal', size='scalable')) = 10.335
2025-07-23 20:30:16,098 - DEBUG - findfont: score(FontEntry(fname='C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\site-packages\\matplotlib\\mpl-data\\fonts\\ttf\\STIXSizTwoSymReg.ttf', name='STIXSizeTwoSym', style='normal', variant='normal', weight=400, stretch='normal', size='scalable')) = 10.05
2025-07-23 20:30:16,098 - DEBUG - findfont: score(FontEntry(fname='C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\site-packages\\matplotlib\\mpl-data\\fonts\\ttf\\STIXSizFiveSymReg.ttf', name='STIXSizeFiveSym', style='normal', variant='normal', weight=400, stretch='normal', size='scalable')) = 10.05
2025-07-23 20:30:16,098 - DEBUG - findfont: score(FontEntry(fname='C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\site-packages\\matplotlib\\mpl-data\\fonts\\ttf\\cmmi10.ttf', name='cmmi10', style='normal', variant='normal', weight=400, stretch='normal', size='scalable')) = 10.05
2025-07-23 20:30:16,098 - DEBUG - findfont: score(FontEntry(fname='C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\site-packages\\matplotlib\\mpl-data\\fonts\\ttf\\STIXSizOneSymReg.ttf', name='STIXSizeOneSym', style='normal', variant='normal', weight=400, stretch='normal', size='scalable')) = 10.05
2025-07-23 20:30:16,098 - DEBUG - findfont: score(FontEntry(fname='C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\site-packages\\matplotlib\\mpl-data\\fonts\\ttf\\DejaVuSansMono-BoldOblique.ttf', name='DejaVu Sans Mono', style='oblique', variant='normal', weight=700, stretch='normal', size='scalable')) = 11.335
2025-07-23 20:30:16,098 - DEBUG - findfont: score(FontEntry(fname='C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\site-packages\\matplotlib\\mpl-data\\fonts\\ttf\\STIXNonUni.ttf', name='STIXNonUnicode', style='normal', variant='normal', weight=400, stretch='normal', size='scalable')) = 10.05
2025-07-23 20:30:16,098 - DEBUG - findfont: score(FontEntry(fname='C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\site-packages\\matplotlib\\mpl-data\\fonts\\ttf\\DejaVuSerif-BoldItalic.ttf', name='DejaVu Serif', style='italic', variant='normal', weight=700, stretch='normal', size='scalable')) = 11.335
2025-07-23 20:30:16,098 - DEBUG - findfont: score(FontEntry(fname='C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\site-packages\\matplotlib\\mpl-data\\fonts\\ttf\\STIXGeneralBol.ttf', name='STIXGeneral', style='normal', variant='normal', weight=700, stretch='normal', size='scalable')) = 10.335
2025-07-23 20:30:16,098 - DEBUG - findfont: score(FontEntry(fname='C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\site-packages\\matplotlib\\mpl-data\\fonts\\ttf\\DejaVuSerif.ttf', name='DejaVu Serif', style='normal', variant='normal', weight=400, stretch='normal', size='scalable')) = 10.05
2025-07-23 20:30:16,098 - DEBUG - findfont: score(FontEntry(fname='C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\site-packages\\matplotlib\\mpl-data\\fonts\\ttf\\STIXNonUniBolIta.ttf', name='STIXNonUnicode', style='italic', variant='normal', weight=700, stretch='normal', size='scalable')) = 11.335
2025-07-23 20:30:16,098 - DEBUG - findfont: score(FontEntry(fname='C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\site-packages\\matplotlib\\mpl-data\\fonts\\ttf\\STIXSizOneSymBol.ttf', name='STIXSizeOneSym', style='normal', variant='normal', weight=700, stretch='normal', size='scalable')) = 10.335
2025-07-23 20:30:16,098 - DEBUG - findfont: score(FontEntry(fname='C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\site-packages\\matplotlib\\mpl-data\\fonts\\ttf\\DejaVuSerif-Bold.ttf', name='DejaVu Serif', style='normal', variant='normal', weight=700, stretch='normal', size='scalable')) = 10.335
2025-07-23 20:30:16,098 - DEBUG - findfont: score(FontEntry(fname='C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\site-packages\\matplotlib\\mpl-data\\fonts\\ttf\\DejaVuSans-Oblique.ttf', name='DejaVu Sans', style='oblique', variant='normal', weight=400, stretch='normal', size='scalable')) = 11.05
2025-07-23 20:30:16,098 - DEBUG - findfont: score(FontEntry(fname='C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\site-packages\\matplotlib\\mpl-data\\fonts\\ttf\\DejaVuSans-Bold.ttf', name='DejaVu Sans', style='normal', variant='normal', weight=700, stretch='normal', size='scalable')) = 10.335
2025-07-23 20:30:16,098 - DEBUG - findfont: score(FontEntry(fname='C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\site-packages\\matplotlib\\mpl-data\\fonts\\ttf\\cmr10.ttf', name='cmr10', style='normal', variant='normal', weight=400, stretch='normal', size='scalable')) = 10.05
2025-07-23 20:30:16,098 - DEBUG - findfont: score(FontEntry(fname='C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\site-packages\\matplotlib\\mpl-data\\fonts\\ttf\\cmb10.ttf', name='cmb10', style='normal', variant='normal', weight=400, stretch='normal', size='scalable')) = 10.05
2025-07-23 20:30:16,098 - DEBUG - findfont: score(FontEntry(fname='C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\site-packages\\matplotlib\\mpl-data\\fonts\\ttf\\DejaVuSansDisplay.ttf', name='DejaVu Sans Display', style='normal', variant='normal', weight=400, stretch='normal', size='scalable')) = 10.05
2025-07-23 20:30:16,098 - DEBUG - findfont: score(FontEntry(fname='C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\site-packages\\matplotlib\\mpl-data\\fonts\\ttf\\DejaVuSans.ttf', name='DejaVu Sans', style='normal', variant='normal', weight=400, stretch='normal', size='scalable')) = 10.05
2025-07-23 20:30:16,098 - DEBUG - findfont: score(FontEntry(fname='C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\site-packages\\matplotlib\\mpl-data\\fonts\\ttf\\STIXSizThreeSymBol.ttf', name='STIXSizeThreeSym', style='normal', variant='normal', weight=700, stretch='normal', size='scalable')) = 10.335
2025-07-23 20:30:16,098 - DEBUG - findfont: score(FontEntry(fname='C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\site-packages\\matplotlib\\mpl-data\\fonts\\ttf\\STIXGeneralBolIta.ttf', name='STIXGeneral', style='italic', variant='normal', weight=700, stretch='normal', size='scalable')) = 11.335
2025-07-23 20:30:16,098 - DEBUG - findfont: score(FontEntry(fname='C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\site-packages\\matplotlib\\mpl-data\\fonts\\ttf\\DejaVuSerifDisplay.ttf', name='DejaVu Serif Display', style='normal', variant='normal', weight=400, stretch='normal', size='scalable')) = 10.05
2025-07-23 20:30:16,098 - DEBUG - findfont: score(FontEntry(fname='C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\site-packages\\matplotlib\\mpl-data\\fonts\\ttf\\DejaVuSans-BoldOblique.ttf', name='DejaVu Sans', style='oblique', variant='normal', weight=700, stretch='normal', size='scalable')) = 11.335
2025-07-23 20:30:16,098 - DEBUG - findfont: score(FontEntry(fname='C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\site-packages\\matplotlib\\mpl-data\\fonts\\ttf\\STIXNonUniIta.ttf', name='STIXNonUnicode', style='italic', variant='normal', weight=400, stretch='normal', size='scalable')) = 11.05
2025-07-23 20:30:16,098 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\VerdanaPro-Regular.ttf', name='Verdana Pro', style='normal', variant='normal', weight=400, stretch='normal', size='scalable')) = 10.05
2025-07-23 20:30:16,098 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\ArialNova.ttf', name='Arial Nova', style='normal', variant='normal', weight=400, stretch='normal', size='scalable')) = 10.05
2025-07-23 20:30:16,098 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\dokchamp.ttf', name='DokChampa', style='normal', variant='normal', weight=400, stretch='normal', size='scalable')) = 10.05
2025-07-23 20:30:16,098 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\ROCKI.TTF', name='Rockwell', style='italic', variant='normal', weight=400, stretch='normal', size='scalable')) = 11.05
2025-07-23 20:30:16,098 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\upcebi.ttf', name='EucrosiaUPC', style='italic', variant='normal', weight=700, stretch='normal', size='scalable')) = 11.335
2025-07-23 20:30:16,098 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\LFAXDI.TTF', name='Lucida Fax', style='italic', variant='normal', weight=600, stretch='normal', size='scalable')) = 11.24
2025-07-23 20:30:16,098 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\BOD_I.TTF', name='Bodoni MT', style='italic', variant='normal', weight=400, stretch='normal', size='scalable')) = 11.05
2025-07-23 20:30:16,098 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\comicz.ttf', name='Comic Sans MS', style='italic', variant='normal', weight=700, stretch='normal', size='scalable')) = 11.335
2025-07-23 20:30:16,098 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\VerdanaPro-CondRegular.ttf', name='Verdana Pro', style='normal', variant='normal', weight=400, stretch='condensed', size='scalable')) = 10.25
2025-07-23 20:30:16,098 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\GillSansLightItNova.ttf', name='Gill Sans Nova', style='italic', variant='normal', weight=300, stretch='normal', size='scalable')) = 11.145
2025-07-23 20:30:16,098 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\NotoSansTC-VF.ttf', name='Noto Sans TC', style='normal', variant='normal', weight=100, stretch='normal', size='scalable')) = 10.335
2025-07-23 20:30:16,098 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\upcll.ttf', name='LilyUPC', style='normal', variant='normal', weight=400, stretch='normal', size='scalable')) = 10.05
2025-07-23 20:30:16,098 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\REFSAN.TTF', name='MS Reference Sans Serif', style='normal', variant='normal', weight=400, stretch='normal', size='scalable')) = 10.05
2025-07-23 20:30:16,098 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\consolai.ttf', name='Consolas', style='italic', variant='normal', weight=400, stretch='normal', size='scalable')) = 11.05
2025-07-23 20:30:16,098 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\verdana.ttf', name='Verdana', style='normal', variant='normal', weight=400, stretch='normal', size='scalable')) = 10.05
2025-07-23 20:30:16,098 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\SCHLBKB.TTF', name='Century Schoolbook', style='normal', variant='normal', weight=700, stretch='normal', size='scalable')) = 10.335
2025-07-23 20:30:16,098 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\SNAP____.TTF', name='Snap ITC', style='normal', variant='normal', weight=400, stretch='normal', size='scalable')) = 10.05
2025-07-23 20:30:16,098 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\upckbi.ttf', name='KodchiangUPC', style='italic', variant='normal', weight=700, stretch='normal', size='scalable')) = 11.335
2025-07-23 20:30:16,098 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\latha.ttf', name='Latha', style='normal', variant='normal', weight=400, stretch='normal', size='scalable')) = 10.05
2025-07-23 20:30:16,098 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\monbaiti.ttf', name='Mongolian Baiti', style='normal', variant='normal', weight=400, stretch='normal', size='scalable')) = 10.05
2025-07-23 20:30:16,098 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\JOKERMAN.TTF', name='Jokerman', style='normal', variant='normal', weight=400, stretch='normal', size='scalable')) = 10.05
2025-07-23 20:30:16,098 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\mangalb.ttf', name='Mangal', style='normal', variant='normal', weight=700, stretch='normal', size='scalable')) = 10.335
2025-07-23 20:30:16,098 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\GeorgiaPro-CondItalic.ttf', name='Georgia Pro', style='italic', variant='normal', weight=400, stretch='condensed', size='scalable')) = 11.25
2025-07-23 20:30:16,098 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\arialni.ttf', name='Arial', style='italic', variant='normal', weight=400, stretch='condensed', size='scalable')) = 1.25
2025-07-23 20:30:16,098 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\arialnb.ttf', name='Arial', style='normal', variant='normal', weight=700, stretch='condensed', size='scalable')) = 0.5349999999999999
2025-07-23 20:30:16,098 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\ANTQUABI.TTF', name='Book Antiqua', style='italic', variant='normal', weight=700, stretch='normal', size='scalable')) = 11.335
2025-07-23 20:30:16,098 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\FTLTLT.TTF', name='Footlight MT Light', style='normal', variant='normal', weight=300, stretch='normal', size='scalable')) = 10.145
2025-07-23 20:30:16,098 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\Shonarb.ttf', name='Shonar Bangla', style='normal', variant='normal', weight=700, stretch='normal', size='scalable')) = 10.335
2025-07-23 20:30:16,098 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\msyhbd.ttc', name='Microsoft YaHei', style='normal', variant='normal', weight=700, stretch='normal', size='scalable')) = 10.335
2025-07-23 20:30:16,098 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\GIL_____.TTF', name='Gill Sans MT', style='normal', variant='normal', weight=400, stretch='normal', size='scalable')) = 10.05
2025-07-23 20:30:16,098 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\FORTE.TTF', name='Forte', style='normal', variant='normal', weight=400, stretch='normal', size='scalable')) = 10.05
2025-07-23 20:30:16,098 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\cambriai.ttf', name='Cambria', style='italic', variant='normal', weight=400, stretch='normal', size='scalable')) = 11.05
2025-07-23 20:30:16,098 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\arial.ttf', name='Arial', style='normal', variant='normal', weight=400, stretch='normal', size='scalable')) = 0.05
2025-07-23 20:30:16,098 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\GillSansItNova.ttf', name='Gill Sans Nova', style='italic', variant='normal', weight=500, stretch='normal', size='scalable')) = 11.145
2025-07-23 20:30:16,098 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\kokilai.ttf', name='Kokila', style='italic', variant='normal', weight=400, stretch='normal', size='scalable')) = 11.05
2025-07-23 20:30:16,098 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\tunga.ttf', name='Tunga', style='normal', variant='normal', weight=400, stretch='normal', size='scalable')) = 10.05
2025-07-23 20:30:16,098 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\nyala.ttf', name='Nyala', style='normal', variant='normal', weight=400, stretch='normal', size='scalable')) = 10.05
2025-07-23 20:30:16,098 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\AGENCYR.TTF', name='Agency FB', style='normal', variant='normal', weight=400, stretch='normal', size='scalable')) = 10.05
2025-07-23 20:30:16,098 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\framdit.ttf', name='Franklin Gothic Medium', style='italic', variant='normal', weight=400, stretch='normal', size='scalable')) = 11.05
2025-07-23 20:30:16,098 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\bahnschrift.ttf', name='Bahnschrift', style='normal', variant='normal', weight=400, stretch='normal', size='scalable')) = 10.05
2025-07-23 20:30:16,098 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\GeorgiaPro-CondBlack.ttf', name='Georgia Pro', style='normal', variant='normal', weight=900, stretch='condensed', size='scalable')) = 10.725
2025-07-23 20:30:16,098 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\GillSansCondNova.ttf', name='Gill Sans Nova', style='normal', variant='normal', weight=500, stretch='condensed', size='scalable')) = 10.344999999999999
2025-07-23 20:30:16,098 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\GillSansNova.ttf', name='Gill Sans Nova', style='normal', variant='normal', weight=500, stretch='normal', size='scalable')) = 10.145
2025-07-23 20:30:16,098 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\msyhl.ttc', name='Microsoft YaHei', style='normal', variant='normal', weight=290, stretch='normal', size='scalable')) = 10.1545
2025-07-23 20:30:16,098 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\RockwellNova-Italic.ttf', name='Rockwell Nova', style='italic', variant='normal', weight=400, stretch='normal', size='scalable')) = 11.05
2025-07-23 20:30:16,098 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\BOD_BI.TTF', name='Bodoni MT', style='italic', variant='normal', weight=700, stretch='normal', size='scalable')) = 11.335
2025-07-23 20:30:16,098 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\COOPBL.TTF', name='Cooper Black', style='normal', variant='normal', weight=400, stretch='normal', size='scalable')) = 10.05
2025-07-23 20:30:16,098 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\CALIFI.TTF', name='Californian FB', style='italic', variant='normal', weight=400, stretch='normal', size='scalable')) = 11.05
2025-07-23 20:30:16,098 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\upclbi.ttf', name='LilyUPC', style='italic', variant='normal', weight=700, stretch='normal', size='scalable')) = 11.335
2025-07-23 20:30:16,098 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\RockwellNovaCond.ttf', name='Rockwell Nova', style='normal', variant='normal', weight=400, stretch='condensed', size='scalable')) = 10.25
2025-07-23 20:30:16,098 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\VerdanaPro-SemiBoldItalic.ttf', name='Verdana Pro', style='italic', variant='normal', weight=600, stretch='normal', size='scalable')) = 11.24
2025-07-23 20:30:16,098 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\GOUDOSI.TTF', name='Goudy Old Style', style='italic', variant='normal', weight=400, stretch='normal', size='scalable')) = 11.05
2025-07-23 20:30:16,098 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\andlso.ttf', name='Andalus', style='normal', variant='normal', weight=400, stretch='normal', size='scalable')) = 10.05
2025-07-23 20:30:16,098 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\SimsunExtG.ttf', name='SimSun-ExtG', style='normal', variant='normal', weight=400, stretch='normal', size='scalable')) = 10.05
2025-07-23 20:30:16,113 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\GILB____.TTF', name='Gill Sans MT', style='normal', variant='normal', weight=700, stretch='normal', size='scalable')) = 10.335
2025-07-23 20:30:16,113 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\kalingab.ttf', name='Kalinga', style='normal', variant='normal', weight=700, stretch='normal', size='scalable')) = 10.335
2025-07-23 20:30:16,113 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\Inkfree.ttf', name='Ink Free', style='normal', variant='normal', weight=400, stretch='normal', size='scalable')) = 10.05
2025-07-23 20:30:16,113 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\BSSYM7.TTF', name='Bookshelf Symbol 7', style='normal', variant='normal', weight=400, stretch='normal', size='scalable')) = 10.05
2025-07-23 20:30:16,113 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\INFROMAN.TTF', name='Informal Roman', style='normal', variant='normal', weight=400, stretch='normal', size='scalable')) = 10.05
2025-07-23 20:30:16,113 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\GillSansCondExtraItNova.ttf', name='Gill Sans Nova', style='italic', variant='normal', weight=800, stretch='condensed', size='scalable')) = 11.629999999999999
2025-07-23 20:30:16,113 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\BOD_CR.TTF', name='Bodoni MT', style='normal', variant='normal', weight=400, stretch='condensed', size='scalable')) = 10.25
2025-07-23 20:30:16,113 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\ITCBLKAD.TTF', name='Blackadder ITC', style='normal', variant='normal', weight=400, stretch='normal', size='scalable')) = 10.05
2025-07-23 20:30:16,113 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\ahronbd.ttf', name='Aharoni', style='normal', variant='normal', weight=700, stretch='normal', size='scalable')) = 10.335
2025-07-23 20:30:16,113 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\UDDigiKyokashoN-R.ttc', name='UD Digi Kyokasho N-R', style='normal', variant='normal', weight=400, stretch='normal', size='scalable')) = 10.05
2025-07-23 20:30:16,113 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\vijaya.ttf', name='Vijaya', style='normal', variant='normal', weight=400, stretch='normal', size='scalable')) = 10.05
2025-07-23 20:30:16,113 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\LaoUI.ttf', name='Lao UI', style='normal', variant='normal', weight=400, stretch='normal', size='scalable')) = 10.05
2025-07-23 20:30:16,113 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\GillSansUltraBoNova.ttf', name='Gill Sans Nova', style='normal', variant='normal', weight=900, stretch='normal', size='scalable')) = 10.525
2025-07-23 20:30:16,113 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\vijayab.ttf', name='Vijaya', style='normal', variant='normal', weight=700, stretch='normal', size='scalable')) = 10.335
2025-07-23 20:30:16,113 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\PRISTINA.TTF', name='Pristina', style='normal', variant='normal', weight=400, stretch='normal', size='scalable')) = 10.05
2025-07-23 20:30:16,113 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\msjh.ttc', name='Microsoft JhengHei', style='normal', variant='normal', weight=400, stretch='normal', size='scalable')) = 10.05
2025-07-23 20:30:16,113 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\mriamc.ttf', name='Miriam Fixed', style='normal', variant='normal', weight=400, stretch='normal', size='scalable')) = 10.05
2025-07-23 20:30:16,113 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\segoeuib.ttf', name='Segoe UI', style='normal', variant='normal', weight=700, stretch='normal', size='scalable')) = 10.335
2025-07-23 20:30:16,113 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\frank.ttf', name='FrankRuehl', style='normal', variant='normal', weight=400, stretch='normal', size='scalable')) = 10.05
2025-07-23 20:30:16,113 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\Dengb.ttf', name='DengXian', style='normal', variant='normal', weight=700, stretch='normal', size='scalable')) = 10.335
2025-07-23 20:30:16,113 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\LSANSDI.TTF', name='Lucida Sans', style='italic', variant='normal', weight=600, stretch='normal', size='scalable')) = 11.24
2025-07-23 20:30:16,113 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\CALISTBI.TTF', name='Calisto MT', style='italic', variant='normal', weight=700, stretch='normal', size='scalable')) = 11.335
2025-07-23 20:30:16,113 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\CALISTB.TTF', name='Calisto MT', style='normal', variant='normal', weight=700, stretch='normal', size='scalable')) = 10.335
2025-07-23 20:30:16,113 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\rod.ttf', name='Rod', style='normal', variant='normal', weight=400, stretch='normal', size='scalable')) = 10.05
2025-07-23 20:30:16,113 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\BROADW.TTF', name='Broadway', style='normal', variant='normal', weight=400, stretch='normal', size='scalable')) = 10.05
2025-07-23 20:30:16,113 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\lvnmbd.ttf', name='Levenim MT', style='normal', variant='normal', weight=700, stretch='normal', size='scalable')) = 10.335
2025-07-23 20:30:16,113 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\GILSANUB.TTF', name='Gill Sans Ultra Bold', style='normal', variant='normal', weight=400, stretch='normal', size='scalable')) = 10.05
2025-07-23 20:30:16,113 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\VerdanaPro-SemiBold.ttf', name='Verdana Pro', style='normal', variant='normal', weight=600, stretch='normal', size='scalable')) = 10.24
2025-07-23 20:30:16,113 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\GOTHICBI.TTF', name='Century Gothic', style='italic', variant='normal', weight=700, stretch='normal', size='scalable')) = 11.335
2025-07-23 20:30:16,113 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\simpo.ttf', name='Simplified Arabic', style='normal', variant='normal', weight=400, stretch='normal', size='scalable')) = 10.05
2025-07-23 20:30:16,113 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\RAVIE.TTF', name='Ravie', style='normal', variant='normal', weight=400, stretch='normal', size='scalable')) = 10.05
2025-07-23 20:30:16,113 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\majalla.ttf', name='Sakkal Majalla', style='normal', variant='normal', weight=400, stretch='normal', size='scalable')) = 10.05
2025-07-23 20:30:16,113 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\NIAGSOL.TTF', name='Niagara Solid', style='normal', variant='normal', weight=400, stretch='normal', size='scalable')) = 10.05
2025-07-23 20:30:16,113 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\CHILLER.TTF', name='Chiller', style='normal', variant='normal', weight=400, stretch='normal', size='scalable')) = 10.05
2025-07-23 20:30:16,113 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\consola.ttf', name='Consolas', style='normal', variant='normal', weight=400, stretch='normal', size='scalable')) = 10.05
2025-07-23 20:30:16,113 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\LBRITED.TTF', name='Lucida Bright', style='normal', variant='normal', weight=600, stretch='normal', size='scalable')) = 10.24
2025-07-23 20:30:16,113 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\BRLNSR.TTF', name='Berlin Sans FB', style='normal', variant='normal', weight=400, stretch='normal', size='scalable')) = 10.05
2025-07-23 20:30:16,113 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\calibrili.ttf', name='Calibri', style='italic', variant='normal', weight=300, stretch='normal', size='scalable')) = 11.145
2025-07-23 20:30:16,113 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\COPRGTB.TTF', name='Copperplate Gothic Bold', style='normal', variant='normal', weight=400, stretch='normal', size='scalable')) = 10.05
2025-07-23 20:30:16,113 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\simsun.ttc', name='SimSun', style='normal', variant='normal', weight=400, stretch='normal', size='scalable')) = 10.05
2025-07-23 20:30:16,113 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\MTCORSVA.TTF', name='Monotype Corsiva', style='normal', variant='normal', weight=400, stretch='normal', size='scalable')) = 10.05
2025-07-23 20:30:16,113 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\RockwellNovaCond-Light.ttf', name='Rockwell Nova', style='normal', variant='normal', weight=300, stretch='condensed', size='scalable')) = 10.344999999999999
2025-07-23 20:30:16,113 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\Candaral.ttf', name='Candara', style='normal', variant='normal', weight=400, stretch='normal', size='scalable')) = 10.05
2025-07-23 20:30:16,113 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\Candara.ttf', name='Candara', style='normal', variant='normal', weight=400, stretch='normal', size='scalable')) = 10.05
2025-07-23 20:30:16,113 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\seguibli.ttf', name='Segoe UI', style='italic', variant='normal', weight=900, stretch='normal', size='scalable')) = 11.525
2025-07-23 20:30:16,113 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\cambriaz.ttf', name='Cambria', style='italic', variant='normal', weight=700, stretch='normal', size='scalable')) = 11.335
2025-07-23 20:30:16,113 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\upcjl.ttf', name='JasmineUPC', style='normal', variant='normal', weight=400, stretch='normal', size='scalable')) = 10.05
2025-07-23 20:30:16,113 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\BOOKOSB.TTF', name='Bookman Old Style', style='normal', variant='normal', weight=600, stretch='normal', size='scalable')) = 10.24
2025-07-23 20:30:16,113 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\JUICE___.TTF', name='Juice ITC', style='normal', variant='normal', weight=400, stretch='normal', size='scalable')) = 10.05
2025-07-23 20:30:16,113 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\NotoSansHK-VF.ttf', name='Noto Sans HK', style='normal', variant='normal', weight=100, stretch='normal', size='scalable')) = 10.335
2025-07-23 20:30:16,113 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\Shonar.ttf', name='Shonar Bangla', style='normal', variant='normal', weight=400, stretch='normal', size='scalable')) = 10.05
2025-07-23 20:30:16,113 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\nrkis.ttf', name='Narkisim', style='normal', variant='normal', weight=400, stretch='normal', size='scalable')) = 10.05
2025-07-23 20:30:16,113 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\mingliub.ttc', name='MingLiU-ExtB', style='normal', variant='normal', weight=400, stretch='normal', size='scalable')) = 10.05
2025-07-23 20:30:16,113 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\GLSNECB.TTF', name='Gill Sans MT Ext Condensed Bold', style='normal', variant='normal', weight=400, stretch='condensed', size='scalable')) = 10.25
2025-07-23 20:30:16,113 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\BOOKOSI.TTF', name='Bookman Old Style', style='italic', variant='normal', weight=300, stretch='normal', size='scalable')) = 11.145
2025-07-23 20:30:16,113 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\HARLOWSI.TTF', name='Harlow Solid Italic', style='italic', variant='normal', weight=400, stretch='normal', size='scalable')) = 11.05
2025-07-23 20:30:16,113 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\VerdanaPro-CondBoldItalic.ttf', name='Verdana Pro', style='italic', variant='normal', weight=700, stretch='condensed', size='scalable')) = 11.535
2025-07-23 20:30:16,113 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\GillSansBoItNova.ttf', name='Gill Sans Nova', style='italic', variant='normal', weight=700, stretch='normal', size='scalable')) = 11.335
2025-07-23 20:30:16,113 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\l_10646.ttf', name='Lucida Sans Unicode', style='normal', variant='normal', weight=400, stretch='normal', size='scalable')) = 10.05
2025-07-23 20:30:16,113 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\gishabd.ttf', name='Gisha', style='normal', variant='normal', weight=700, stretch='normal', size='scalable')) = 10.335
2025-07-23 20:30:16,113 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\BOD_B.TTF', name='Bodoni MT', style='normal', variant='normal', weight=700, stretch='normal', size='scalable')) = 10.335
2025-07-23 20:30:16,113 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\utsaah.ttf', name='Utsaah', style='normal', variant='normal', weight=400, stretch='normal', size='scalable')) = 10.05
2025-07-23 20:30:16,113 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\utsaahb.ttf', name='Utsaah', style='normal', variant='normal', weight=700, stretch='normal', size='scalable')) = 10.335
2025-07-23 20:30:16,113 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\TCCEB.TTF', name='Tw Cen MT Condensed Extra Bold', style='normal', variant='normal', weight=400, stretch='condensed', size='scalable')) = 10.25
2025-07-23 20:30:16,113 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\GeorgiaPro-CondLightItalic.ttf', name='Georgia Pro', style='italic', variant='normal', weight=300, stretch='condensed', size='scalable')) = 11.344999999999999
2025-07-23 20:30:16,113 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\verdanai.ttf', name='Verdana', style='italic', variant='normal', weight=400, stretch='normal', size='scalable')) = 11.05
2025-07-23 20:30:16,113 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\ntailu.ttf', name='Microsoft New Tai Lue', style='normal', variant='normal', weight=400, stretch='normal', size='scalable')) = 10.05
2025-07-23 20:30:16,113 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\SitkaZ.ttc', name='Sitka Small', style='italic', variant='normal', weight=700, stretch='normal', size='scalable')) = 11.335
2025-07-23 20:30:16,113 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\LATINWD.TTF', name='Wide Latin', style='normal', variant='normal', weight=400, stretch='expanded', size='scalable')) = 10.25
2025-07-23 20:30:16,113 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\segoeprb.ttf', name='Segoe Print', style='normal', variant='normal', weight=700, stretch='normal', size='scalable')) = 10.335
2025-07-23 20:30:16,113 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\segoeui.ttf', name='Segoe UI', style='normal', variant='normal', weight=400, stretch='normal', size='scalable')) = 10.05
2025-07-23 20:30:16,113 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\COLONNA.TTF', name='Colonna MT', style='normal', variant='normal', weight=400, stretch='normal', size='scalable')) = 10.05
2025-07-23 20:30:16,113 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\Gabriola.ttf', name='Gabriola', style='normal', variant='normal', weight=400, stretch='normal', size='scalable')) = 10.05
2025-07-23 20:30:16,113 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\UDDigiKyokashoN-B.ttc', name='UD Digi Kyokasho N-B', style='normal', variant='normal', weight=700, stretch='normal', size='scalable')) = 10.335
2025-07-23 20:30:16,113 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\phagspa.ttf', name='Microsoft PhagsPa', style='normal', variant='normal', weight=400, stretch='normal', size='scalable')) = 10.05
2025-07-23 20:30:16,113 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\FRSCRIPT.TTF', name='French Script MT', style='normal', variant='normal', weight=400, stretch='normal', size='scalable')) = 10.05
2025-07-23 20:30:16,113 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\NotoSansJP-VF.ttf', name='Noto Sans JP', style='normal', variant='normal', weight=100, stretch='normal', size='scalable')) = 10.335
2025-07-23 20:30:16,113 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\GeorgiaPro-CondSemiBoldItalic.ttf', name='Georgia Pro', style='italic', variant='normal', weight=600, stretch='condensed', size='scalable')) = 11.44
2025-07-23 20:30:16,113 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\ArialNova-Light.ttf', name='Arial Nova', style='normal', variant='normal', weight=300, stretch='normal', size='scalable')) = 10.145
2025-07-23 20:30:16,113 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\davidbd.ttf', name='David', style='normal', variant='normal', weight=700, stretch='normal', size='scalable')) = 10.335
2025-07-23 20:30:16,113 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\GeorgiaPro-CondLight.ttf', name='Georgia Pro', style='normal', variant='normal', weight=300, stretch='condensed', size='scalable')) = 10.344999999999999
2025-07-23 20:30:16,113 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\GOTHICI.TTF', name='Century Gothic', style='italic', variant='normal', weight=400, stretch='normal', size='scalable')) = 11.05
2025-07-23 20:30:16,113 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\VLADIMIR.TTF', name='Vladimir Script', style='normal', variant='normal', weight=400, stretch='normal', size='scalable')) = 10.05
2025-07-23 20:30:16,113 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\upcjbi.ttf', name='JasmineUPC', style='italic', variant='normal', weight=700, stretch='normal', size='scalable')) = 11.335
2025-07-23 20:30:16,113 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\HPSimplified_It.ttf', name='HP Simplified', style='italic', variant='normal', weight=400, stretch='normal', size='scalable')) = 11.05
2025-07-23 20:30:16,113 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\GeorgiaPro-Black.ttf', name='Georgia Pro', style='normal', variant='normal', weight=900, stretch='normal', size='scalable')) = 10.525
2025-07-23 20:30:16,113 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\ERASMD.TTF', name='Eras Medium ITC', style='normal', variant='normal', weight=400, stretch='normal', size='scalable')) = 10.05
2025-07-23 20:30:16,113 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\VerdanaPro-LightItalic.ttf', name='Verdana Pro', style='italic', variant='normal', weight=300, stretch='normal', size='scalable')) = 11.145
2025-07-23 20:30:16,113 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\yumindb.ttf', name='Yu Mincho', style='normal', variant='normal', weight=600, stretch='normal', size='scalable')) = 10.24
2025-07-23 20:30:16,113 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\BAUHS93.TTF', name='Bauhaus 93', style='normal', variant='normal', weight=400, stretch='normal', size='scalable')) = 10.05
2025-07-23 20:30:16,113 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\mmrtextb.ttf', name='Myanmar Text', style='normal', variant='normal', weight=700, stretch='normal', size='scalable')) = 10.335
2025-07-23 20:30:16,113 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\OCRAEXT.TTF', name='OCR A Extended', style='normal', variant='normal', weight=400, stretch='expanded', size='scalable')) = 10.25
2025-07-23 20:30:16,129 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\kalinga.ttf', name='Kalinga', style='normal', variant='normal', weight=400, stretch='normal', size='scalable')) = 10.05
2025-07-23 20:30:16,129 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\MISTRAL.TTF', name='Mistral', style='normal', variant='normal', weight=400, stretch='normal', size='scalable')) = 10.05
2025-07-23 20:30:16,129 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\simfang.ttf', name='FangSong', style='normal', variant='normal', weight=400, stretch='normal', size='scalable')) = 10.05
2025-07-23 20:30:16,129 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\VerdanaPro-Bold.ttf', name='Verdana Pro', style='normal', variant='normal', weight=700, stretch='normal', size='scalable')) = 10.335
2025-07-23 20:30:16,129 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\upcki.ttf', name='KodchiangUPC', style='italic', variant='normal', weight=400, stretch='normal', size='scalable')) = 11.05
2025-07-23 20:30:16,129 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\ntailub.ttf', name='Microsoft New Tai Lue', style='normal', variant='normal', weight=700, stretch='normal', size='scalable')) = 10.335
2025-07-23 20:30:16,129 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\ALGER.TTF', name='Algerian', style='normal', variant='normal', weight=400, stretch='normal', size='scalable')) = 10.05
2025-07-23 20:30:16,129 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\RockwellNova-LightItalic.ttf', name='Rockwell Nova', style='italic', variant='normal', weight=300, stretch='normal', size='scalable')) = 11.145
2025-07-23 20:30:16,129 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\upcdi.ttf', name='DilleniaUPC', style='italic', variant='normal', weight=400, stretch='normal', size='scalable')) = 11.05
2025-07-23 20:30:16,129 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\GillSansCondExtraNova.ttf', name='Gill Sans Nova', style='normal', variant='normal', weight=800, stretch='condensed', size='scalable')) = 10.629999999999999
2025-07-23 20:30:16,129 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\ARIALUNI.TTF', name='Arial Unicode MS', style='normal', variant='normal', weight=400, stretch='normal', size='scalable')) = 10.05
2025-07-23 20:30:16,129 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\palai.ttf', name='Palatino Linotype', style='italic', variant='normal', weight=400, stretch='normal', size='scalable')) = 11.05
2025-07-23 20:30:16,129 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\ROCC____.TTF', name='Rockwell Condensed', style='normal', variant='normal', weight=400, stretch='condensed', size='scalable')) = 10.25
2025-07-23 20:30:16,129 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\GeorgiaPro-CondRegular.ttf', name='Georgia Pro', style='normal', variant='normal', weight=400, stretch='condensed', size='scalable')) = 10.25
2025-07-23 20:30:16,129 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\LFAX.TTF', name='Lucida Fax', style='normal', variant='normal', weight=400, stretch='normal', size='scalable')) = 10.05
2025-07-23 20:30:16,129 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\TCCB____.TTF', name='Tw Cen MT Condensed', style='normal', variant='normal', weight=700, stretch='condensed', size='scalable')) = 10.535
2025-07-23 20:30:16,129 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\tungab.ttf', name='Tunga', style='normal', variant='normal', weight=700, stretch='normal', size='scalable')) = 10.335
2025-07-23 20:30:16,129 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\FRAMDCN.TTF', name='Franklin Gothic Medium Cond', style='normal', variant='normal', weight=400, stretch='condensed', size='scalable')) = 10.25
2025-07-23 20:30:16,129 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\kartika.ttf', name='Kartika', style='normal', variant='normal', weight=400, stretch='normal', size='scalable')) = 10.05
2025-07-23 20:30:16,129 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\PAPYRUS.TTF', name='Papyrus', style='normal', variant='normal', weight=400, stretch='normal', size='scalable')) = 10.05
2025-07-23 20:30:16,129 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\LFAXI.TTF', name='Lucida Fax', style='italic', variant='normal', weight=400, stretch='normal', size='scalable')) = 11.05
2025-07-23 20:30:16,129 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\BERNHC.TTF', name='Bernard MT Condensed', style='normal', variant='normal', weight=400, stretch='condensed', size='scalable')) = 10.25
2025-07-23 20:30:16,129 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\GeorgiaPro-SemiBold.ttf', name='Georgia Pro', style='normal', variant='normal', weight=600, stretch='normal', size='scalable')) = 10.24
2025-07-23 20:30:16,129 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\simsunb.ttf', name='SimSun-ExtB', style='normal', variant='normal', weight=400, stretch='normal', size='scalable')) = 10.05
2025-07-23 20:30:16,129 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\cambriab.ttf', name='Cambria', style='normal', variant='normal', weight=700, stretch='normal', size='scalable')) = 10.335
2025-07-23 20:30:16,129 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\ArialNovaCond-Light.ttf', name='Arial Nova', style='normal', variant='normal', weight=300, stretch='condensed', size='scalable')) = 10.344999999999999
2025-07-23 20:30:16,129 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\ArialNova-BoldItalic.ttf', name='Arial Nova', style='italic', variant='normal', weight=700, stretch='normal', size='scalable')) = 11.335
2025-07-23 20:30:16,129 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\yuminl.ttf', name='Yu Mincho', style='normal', variant='normal', weight=300, stretch='normal', size='scalable')) = 10.145
2025-07-23 20:30:16,129 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\BRLNSDB.TTF', name='Berlin Sans FB Demi', style='normal', variant='normal', weight=700, stretch='normal', size='scalable')) = 10.335
2025-07-23 20:30:16,129 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\taile.ttf', name='Microsoft Tai Le', style='normal', variant='normal', weight=400, stretch='normal', size='scalable')) = 10.05
2025-07-23 20:30:16,129 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\courbd.ttf', name='Courier New', style='normal', variant='normal', weight=700, stretch='normal', size='scalable')) = 10.335
2025-07-23 20:30:16,129 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\gadugib.ttf', name='Gadugi', style='normal', variant='normal', weight=700, stretch='normal', size='scalable')) = 10.335
2025-07-23 20:30:16,129 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\mvboli.ttf', name='MV Boli', style='normal', variant='normal', weight=400, stretch='normal', size='scalable')) = 10.05
2025-07-23 20:30:16,129 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\upcfi.ttf', name='FreesiaUPC', style='italic', variant='normal', weight=400, stretch='normal', size='scalable')) = 11.05
2025-07-23 20:30:16,129 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\VerdanaPro-CondBlackItalic.ttf', name='Verdana Pro', style='italic', variant='normal', weight=900, stretch='condensed', size='scalable')) = 11.725
2025-07-23 20:30:16,129 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\consolaz.ttf', name='Consolas', style='italic', variant='normal', weight=700, stretch='normal', size='scalable')) = 11.335
2025-07-23 20:30:16,129 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\GeorgiaPro-Italic.ttf', name='Georgia Pro', style='italic', variant='normal', weight=400, stretch='normal', size='scalable')) = 11.05
2025-07-23 20:30:16,129 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\PALSCRI.TTF', name='Palace Script MT', style='normal', variant='normal', weight=400, stretch='normal', size='scalable')) = 10.05
2025-07-23 20:30:16,129 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\PERTIBD.TTF', name='Perpetua Titling MT', style='normal', variant='normal', weight=700, stretch='normal', size='scalable')) = 10.335
2025-07-23 20:30:16,129 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\segoepr.ttf', name='Segoe Print', style='normal', variant='normal', weight=400, stretch='normal', size='scalable')) = 10.05
2025-07-23 20:30:16,129 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\ROCK.TTF', name='Rockwell', style='normal', variant='normal', weight=400, stretch='normal', size='scalable')) = 10.05
2025-07-23 20:30:16,129 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\GILI____.TTF', name='Gill Sans MT', style='italic', variant='normal', weight=400, stretch='normal', size='scalable')) = 11.05
2025-07-23 20:30:16,129 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\upcib.ttf', name='IrisUPC', style='normal', variant='normal', weight=700, stretch='normal', size='scalable')) = 10.335
2025-07-23 20:30:16,129 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\GillSansCondBoItNova.ttf', name='Gill Sans Nova', style='italic', variant='normal', weight=700, stretch='condensed', size='scalable')) = 11.535
2025-07-23 20:30:16,129 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\upcli.ttf', name='LilyUPC', style='italic', variant='normal', weight=400, stretch='normal', size='scalable')) = 11.05
2025-07-23 20:30:16,129 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\NirmalaB.ttf', name='Nirmala UI', style='normal', variant='normal', weight=700, stretch='normal', size='scalable')) = 10.335
2025-07-23 20:30:16,129 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\PARCHM.TTF', name='Parchment', style='normal', variant='normal', weight=400, stretch='normal', size='scalable')) = 10.05
2025-07-23 20:30:16,129 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\FRADMIT.TTF', name='Franklin Gothic Demi', style='italic', variant='normal', weight=400, stretch='normal', size='scalable')) = 11.05
2025-07-23 20:30:16,129 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\arabtype.ttf', name='Arabic Typesetting', style='normal', variant='normal', weight=400, stretch='normal', size='scalable')) = 10.05
2025-07-23 20:30:16,129 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\TEMPSITC.TTF', name='Tempus Sans ITC', style='normal', variant='normal', weight=400, stretch='normal', size='scalable')) = 10.05
2025-07-23 20:30:16,129 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\cordia.ttc', name='Cordia New', style='normal', variant='normal', weight=400, stretch='normal', size='scalable')) = 10.05
2025-07-23 20:30:16,129 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\ITCEDSCR.TTF', name='Edwardian Script ITC', style='normal', variant='normal', weight=400, stretch='normal', size='scalable')) = 10.05
2025-07-23 20:30:16,129 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\MAGNETOB.TTF', name='Magneto', style='normal', variant='normal', weight=700, stretch='normal', size='scalable')) = 10.335
2025-07-23 20:30:16,129 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\seguibl.ttf', name='Segoe UI', style='normal', variant='normal', weight=900, stretch='normal', size='scalable')) = 10.525
2025-07-23 20:30:16,129 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\YuGothB.ttc', name='Yu Gothic', style='normal', variant='normal', weight=700, stretch='normal', size='scalable')) = 10.335
2025-07-23 20:30:16,129 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\VerdanaPro-Light.ttf', name='Verdana Pro', style='normal', variant='normal', weight=300, stretch='normal', size='scalable')) = 10.145
2025-07-23 20:30:16,129 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\NotoSansSC-VF.ttf', name='Noto Sans SC', style='normal', variant='normal', weight=100, stretch='normal', size='scalable')) = 10.335
2025-07-23 20:30:16,129 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\micross.ttf', name='Microsoft Sans Serif', style='normal', variant='normal', weight=400, stretch='normal', size='scalable')) = 10.05
2025-07-23 20:30:16,129 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\FRADMCN.TTF', name='Franklin Gothic Demi Cond', style='normal', variant='normal', weight=400, stretch='condensed', size='scalable')) = 10.25
2025-07-23 20:30:16,129 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\upcel.ttf', name='EucrosiaUPC', style='normal', variant='normal', weight=400, stretch='normal', size='scalable')) = 10.05
2025-07-23 20:30:16,129 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\BOD_BLAI.TTF', name='Bodoni MT', style='italic', variant='normal', weight=900, stretch='normal', size='scalable')) = 11.525
2025-07-23 20:30:16,129 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\CALIFR.TTF', name='Californian FB', style='normal', variant='normal', weight=400, stretch='normal', size='scalable')) = 10.05
2025-07-23 20:30:16,129 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\VIVALDII.TTF', name='Vivaldi', style='italic', variant='normal', weight=400, stretch='normal', size='scalable')) = 11.05
2025-07-23 20:30:16,129 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\GARABD.TTF', name='Garamond', style='normal', variant='normal', weight=700, stretch='normal', size='scalable')) = 10.335
2025-07-23 20:30:16,129 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\upckb.ttf', name='KodchiangUPC', style='normal', variant='normal', weight=700, stretch='normal', size='scalable')) = 10.335
2025-07-23 20:30:16,129 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\FRABK.TTF', name='Franklin Gothic Book', style='normal', variant='normal', weight=400, stretch='normal', size='scalable')) = 10.05
2025-07-23 20:30:16,129 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\georgiai.ttf', name='Georgia', style='italic', variant='normal', weight=400, stretch='normal', size='scalable')) = 11.05
2025-07-23 20:30:16,129 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\COPRGTL.TTF', name='Copperplate Gothic Light', style='normal', variant='normal', weight=400, stretch='normal', size='scalable')) = 10.05
2025-07-23 20:30:16,129 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\VerdanaPro-BlackItalic.ttf', name='Verdana Pro', style='italic', variant='normal', weight=900, stretch='normal', size='scalable')) = 11.525
2025-07-23 20:30:16,129 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\upcji.ttf', name='JasmineUPC', style='italic', variant='normal', weight=400, stretch='normal', size='scalable')) = 11.05
2025-07-23 20:30:16,129 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\GillSansCondItNova.ttf', name='Gill Sans Nova', style='italic', variant='normal', weight=500, stretch='condensed', size='scalable')) = 11.344999999999999
2025-07-23 20:30:16,129 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\GeorgiaPro-Bold.ttf', name='Georgia Pro', style='normal', variant='normal', weight=700, stretch='normal', size='scalable')) = 10.335
2025-07-23 20:30:16,129 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\BASKVILL.TTF', name='Baskerville Old Face', style='normal', variant='normal', weight=400, stretch='normal', size='scalable')) = 10.05
2025-07-23 20:30:16,129 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\segoeuil.ttf', name='Segoe UI', style='normal', variant='normal', weight=300, stretch='normal', size='scalable')) = 10.145
2025-07-23 20:30:16,129 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\GIGI.TTF', name='Gigi', style='normal', variant='normal', weight=400, stretch='normal', size='scalable')) = 10.05
2025-07-23 20:30:16,129 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\IMPRISHA.TTF', name='Imprint MT Shadow', style='normal', variant='normal', weight=400, stretch='normal', size='scalable')) = 10.05
2025-07-23 20:30:16,129 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\GLECB.TTF', name='Gloucester MT Extra Condensed', style='normal', variant='normal', weight=400, stretch='condensed', size='scalable')) = 10.25
2025-07-23 20:30:16,129 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\KUNSTLER.TTF', name='Kunstler Script', style='normal', variant='normal', weight=400, stretch='normal', size='scalable')) = 10.05
2025-07-23 20:30:16,129 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\GillSansCondUltraBoNova.ttf', name='Gill Sans Nova', style='normal', variant='normal', weight=900, stretch='condensed', size='scalable')) = 10.725
2025-07-23 20:30:16,129 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\CENSCBK.TTF', name='Century Schoolbook', style='normal', variant='normal', weight=400, stretch='normal', size='scalable')) = 10.05
2025-07-23 20:30:16,129 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\MOD20.TTF', name='Modern No. 20', style='normal', variant='normal', weight=400, stretch='normal', size='scalable')) = 10.05
2025-07-23 20:30:16,129 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\VerdanaPro-BoldItalic.ttf', name='Verdana Pro', style='italic', variant='normal', weight=700, stretch='normal', size='scalable')) = 11.335
2025-07-23 20:30:16,129 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\LBRITEDI.TTF', name='Lucida Bright', style='italic', variant='normal', weight=600, stretch='normal', size='scalable')) = 11.24
2025-07-23 20:30:16,129 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\POORICH.TTF', name='Poor Richard', style='normal', variant='normal', weight=400, stretch='normal', size='scalable')) = 10.05
2025-07-23 20:30:16,129 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\UrdType.ttf', name='Urdu Typesetting', style='normal', variant='normal', weight=400, stretch='normal', size='scalable')) = 10.05
2025-07-23 20:30:16,129 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\aparajbi.ttf', name='Aparajita', style='italic', variant='normal', weight=700, stretch='normal', size='scalable')) = 11.335
2025-07-23 20:30:16,129 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\ROCKEB.TTF', name='Rockwell Extra Bold', style='normal', variant='normal', weight=800, stretch='normal', size='scalable')) = 10.43
2025-07-23 20:30:16,129 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\euphemia.ttf', name='Euphemia', style='normal', variant='normal', weight=400, stretch='normal', size='scalable')) = 10.05
2025-07-23 20:30:16,129 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\RockwellNovaCond-BoldItalic.ttf', name='Rockwell Nova', style='italic', variant='normal', weight=700, stretch='condensed', size='scalable')) = 11.535
2025-07-23 20:30:16,129 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\TCB_____.TTF', name='Tw Cen MT', style='normal', variant='normal', weight=700, stretch='normal', size='scalable')) = 10.335
2025-07-23 20:30:16,129 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\aparaji.ttf', name='Aparajita', style='italic', variant='normal', weight=400, stretch='normal', size='scalable')) = 11.05
2025-07-23 20:30:16,129 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\upcfl.ttf', name='FreesiaUPC', style='normal', variant='normal', weight=400, stretch='normal', size='scalable')) = 10.05
2025-07-23 20:30:16,129 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\upceb.ttf', name='EucrosiaUPC', style='normal', variant='normal', weight=700, stretch='normal', size='scalable')) = 10.335
2025-07-23 20:30:16,129 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\calibri.ttf', name='Calibri', style='normal', variant='normal', weight=400, stretch='normal', size='scalable')) = 10.05
2025-07-23 20:30:16,129 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\seguisli.ttf', name='Segoe UI', style='italic', variant='normal', weight=350, stretch='normal', size='scalable')) = 11.0975
2025-07-23 20:30:16,129 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\TCM_____.TTF', name='Tw Cen MT', style='normal', variant='normal', weight=400, stretch='normal', size='scalable')) = 10.05
2025-07-23 20:30:16,129 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\corbelz.ttf', name='Corbel', style='italic', variant='normal', weight=700, stretch='normal', size='scalable')) = 11.335
2025-07-23 20:30:16,129 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\ariblk.ttf', name='Arial', style='normal', variant='normal', weight=900, stretch='normal', size='scalable')) = 0.525
2025-07-23 20:30:16,145 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\mmrtext.ttf', name='Myanmar Text', style='normal', variant='normal', weight=400, stretch='normal', size='scalable')) = 10.05
2025-07-23 20:30:16,145 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\georgia.ttf', name='Georgia', style='normal', variant='normal', weight=400, stretch='normal', size='scalable')) = 10.05
2025-07-23 20:30:16,145 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\msjhl.ttc', name='Microsoft JhengHei', style='normal', variant='normal', weight=290, stretch='normal', size='scalable')) = 10.1545
2025-07-23 20:30:16,145 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\tradbdo.ttf', name='Traditional Arabic', style='normal', variant='normal', weight=700, stretch='normal', size='scalable')) = 10.335
2025-07-23 20:30:16,145 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\ArialNovaCond-Italic.ttf', name='Arial Nova', style='italic', variant='normal', weight=400, stretch='condensed', size='scalable')) = 11.25
2025-07-23 20:30:16,145 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\msjhbd.ttc', name='Microsoft JhengHei', style='normal', variant='normal', weight=700, stretch='normal', size='scalable')) = 10.335
2025-07-23 20:30:16,145 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\FRAHVIT.TTF', name='Franklin Gothic Heavy', style='italic', variant='normal', weight=400, stretch='normal', size='scalable')) = 11.05
2025-07-23 20:30:16,145 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\georgiab.ttf', name='Georgia', style='normal', variant='normal', weight=700, stretch='normal', size='scalable')) = 10.335
2025-07-23 20:30:16,145 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\daunpenh.ttf', name='DaunPenh', style='normal', variant='normal', weight=400, stretch='normal', size='scalable')) = 10.05
2025-07-23 20:30:16,145 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\HTOWERT.TTF', name='High Tower Text', style='normal', variant='normal', weight=400, stretch='normal', size='scalable')) = 10.05
2025-07-23 20:30:16,145 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\david.ttf', name='David', style='normal', variant='normal', weight=400, stretch='normal', size='scalable')) = 10.05
2025-07-23 20:30:16,145 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\NotoSansKR-VF.ttf', name='Noto Sans KR', style='normal', variant='normal', weight=100, stretch='normal', size='scalable')) = 10.335
2025-07-23 20:30:16,145 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\ANTQUAI.TTF', name='Book Antiqua', style='italic', variant='normal', weight=400, stretch='normal', size='scalable')) = 11.05
2025-07-23 20:30:16,145 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\holomdl2.ttf', name='HoloLens MDL2 Assets', style='normal', variant='normal', weight=400, stretch='normal', size='scalable')) = 10.05
2025-07-23 20:30:16,145 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\corbeli.ttf', name='Corbel', style='italic', variant='normal', weight=400, stretch='normal', size='scalable')) = 11.05
2025-07-23 20:30:16,145 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\aparajb.ttf', name='Aparajita', style='normal', variant='normal', weight=700, stretch='normal', size='scalable')) = 10.335
2025-07-23 20:30:16,145 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\constanb.ttf', name='Constantia', style='normal', variant='normal', weight=700, stretch='normal', size='scalable')) = 10.335
2025-07-23 20:30:16,145 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\GeorgiaPro-LightItalic.ttf', name='Georgia Pro', style='italic', variant='normal', weight=300, stretch='normal', size='scalable')) = 11.145
2025-07-23 20:30:16,145 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\plantc.ttf', name='Plantagenet Cherokee', style='normal', variant='normal', weight=400, stretch='normal', size='scalable')) = 10.05
2025-07-23 20:30:16,145 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\HARNGTON.TTF', name='Harrington', style='normal', variant='normal', weight=400, stretch='normal', size='scalable')) = 10.05
2025-07-23 20:30:16,145 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\himalaya.ttf', name='Microsoft Himalaya', style='normal', variant='normal', weight=400, stretch='normal', size='scalable')) = 10.05
2025-07-23 20:30:16,145 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\TCBI____.TTF', name='Tw Cen MT', style='italic', variant='normal', weight=700, stretch='normal', size='scalable')) = 11.335
2025-07-23 20:30:16,145 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\leelawdb.ttf', name='Leelawadee', style='normal', variant='normal', weight=700, stretch='normal', size='scalable')) = 10.335
2025-07-23 20:30:16,145 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\ariali.ttf', name='Arial', style='italic', variant='normal', weight=400, stretch='normal', size='scalable')) = 1.05
2025-07-23 20:30:16,145 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\LSANS.TTF', name='Lucida Sans', style='normal', variant='normal', weight=400, stretch='normal', size='scalable')) = 10.05
2025-07-23 20:30:16,145 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\iskpota.ttf', name='Iskoola Pota', style='normal', variant='normal', weight=400, stretch='normal', size='scalable')) = 10.05
2025-07-23 20:30:16,145 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\raavi.ttf', name='Raavi', style='normal', variant='normal', weight=400, stretch='normal', size='scalable')) = 10.05
2025-07-23 20:30:16,145 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\ArialNovaCond-LightItalic.ttf', name='Arial Nova', style='italic', variant='normal', weight=300, stretch='condensed', size='scalable')) = 11.344999999999999
2025-07-23 20:30:16,145 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\PLAYBILL.TTF', name='Playbill', style='normal', variant='normal', weight=400, stretch='normal', size='scalable')) = 10.05
2025-07-23 20:30:16,145 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\vrindab.ttf', name='Vrinda', style='normal', variant='normal', weight=700, stretch='normal', size='scalable')) = 10.335
2025-07-23 20:30:16,145 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\LSANSD.TTF', name='Lucida Sans', style='normal', variant='normal', weight=600, stretch='normal', size='scalable')) = 10.24
2025-07-23 20:30:16,145 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\seguili.ttf', name='Segoe UI', style='italic', variant='normal', weight=300, stretch='normal', size='scalable')) = 11.145
2025-07-23 20:30:16,145 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\estre.ttf', name='Estrangelo Edessa', style='normal', variant='normal', weight=400, stretch='normal', size='scalable')) = 10.05
2025-07-23 20:30:16,145 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\upckl.ttf', name='KodchiangUPC', style='normal', variant='normal', weight=400, stretch='normal', size='scalable')) = 10.05
2025-07-23 20:30:16,145 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\arialbd.ttf', name='Arial', style='normal', variant='normal', weight=700, stretch='normal', size='scalable')) = 0.33499999999999996
2025-07-23 20:30:16,145 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\verdanab.ttf', name='Verdana', style='normal', variant='normal', weight=700, stretch='normal', size='scalable')) = 10.335
2025-07-23 20:30:16,145 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\Dengl.ttf', name='DengXian', style='normal', variant='normal', weight=300, stretch='normal', size='scalable')) = 10.145
2025-07-23 20:30:16,145 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\KhmerUIb.ttf', name='Khmer UI', style='normal', variant='normal', weight=700, stretch='normal', size='scalable')) = 10.335
2025-07-23 20:30:16,145 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\ArialNovaCond-BoldItalic.ttf', name='Arial Nova', style='italic', variant='normal', weight=700, stretch='condensed', size='scalable')) = 11.535
2025-07-23 20:30:16,145 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\meiryob.ttc', name='Meiryo', style='normal', variant='normal', weight=700, stretch='normal', size='scalable')) = 10.335
2025-07-23 20:30:16,145 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\BRLNSB.TTF', name='Berlin Sans FB', style='normal', variant='normal', weight=700, stretch='normal', size='scalable')) = 10.335
2025-07-23 20:30:16,145 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\verdanaz.ttf', name='Verdana', style='italic', variant='normal', weight=700, stretch='normal', size='scalable')) = 11.335
2025-07-23 20:30:16,145 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\GOUDOS.TTF', name='Goudy Old Style', style='normal', variant='normal', weight=400, stretch='normal', size='scalable')) = 10.05
2025-07-23 20:30:16,145 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\upcdbi.ttf', name='DilleniaUPC', style='italic', variant='normal', weight=700, stretch='normal', size='scalable')) = 11.335
2025-07-23 20:30:16,145 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\SitkaB.ttc', name='Sitka Small', style='normal', variant='normal', weight=700, stretch='normal', size='scalable')) = 10.335
2025-07-23 20:30:16,145 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\ARLRDBD.TTF', name='Arial Rounded MT Bold', style='normal', variant='normal', weight=400, stretch='normal', size='scalable')) = 10.05
2025-07-23 20:30:16,145 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\kartikab.ttf', name='Kartika', style='normal', variant='normal', weight=700, stretch='normal', size='scalable')) = 10.335
2025-07-23 20:30:16,145 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\BELLB.TTF', name='Bell MT', style='normal', variant='normal', weight=700, stretch='normal', size='scalable')) = 10.335
2025-07-23 20:30:16,145 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\wingding.ttf', name='Wingdings', style='normal', variant='normal', weight=400, stretch='normal', size='scalable')) = 10.05
2025-07-23 20:30:16,145 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\mangal.ttf', name='Mangal', style='normal', variant='normal', weight=400, stretch='normal', size='scalable')) = 10.05
2025-07-23 20:30:16,145 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\WINGDNG3.TTF', name='Wingdings 3', style='normal', variant='normal', weight=400, stretch='normal', size='scalable')) = 10.05
2025-07-23 20:30:16,145 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\RockwellNovaCond-LightItalic.ttf', name='Rockwell Nova', style='italic', variant='normal', weight=300, stretch='condensed', size='scalable')) = 11.344999999999999
2025-07-23 20:30:16,145 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\HPSimplified_LtIt.ttf', name='HP Simplified', style='italic', variant='normal', weight=300, stretch='normal', size='scalable')) = 11.145
2025-07-23 20:30:16,145 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\VerdanaPro-CondLight.ttf', name='Verdana Pro', style='normal', variant='normal', weight=300, stretch='condensed', size='scalable')) = 10.344999999999999
2025-07-23 20:30:16,145 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\cambria.ttc', name='Cambria', style='normal', variant='normal', weight=400, stretch='normal', size='scalable')) = 10.05
2025-07-23 20:30:16,145 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\Candarai.ttf', name='Candara', style='italic', variant='normal', weight=400, stretch='normal', size='scalable')) = 11.05
2025-07-23 20:30:16,145 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\CALIST.TTF', name='Calisto MT', style='normal', variant='normal', weight=400, stretch='normal', size='scalable')) = 10.05
2025-07-23 20:30:16,145 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\ebrimabd.ttf', name='Ebrima', style='normal', variant='normal', weight=700, stretch='normal', size='scalable')) = 10.335
2025-07-23 20:30:16,145 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\CALIFB.TTF', name='Californian FB', style='normal', variant='normal', weight=700, stretch='normal', size='scalable')) = 10.335
2025-07-23 20:30:16,145 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\framd.ttf', name='Franklin Gothic Medium', style='normal', variant='normal', weight=400, stretch='normal', size='scalable')) = 10.05
2025-07-23 20:30:16,145 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\constani.ttf', name='Constantia', style='italic', variant='normal', weight=400, stretch='normal', size='scalable')) = 11.05
2025-07-23 20:30:16,145 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\LTYPE.TTF', name='Lucida Sans Typewriter', style='normal', variant='normal', weight=400, stretch='normal', size='scalable')) = 10.05
2025-07-23 20:30:16,145 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\LFAXD.TTF', name='Lucida Fax', style='normal', variant='normal', weight=600, stretch='normal', size='scalable')) = 10.24
2025-07-23 20:30:16,145 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\symbol.ttf', name='Symbol', style='normal', variant='normal', weight=400, stretch='normal', size='scalable')) = 10.05
2025-07-23 20:30:16,145 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\BIZ-UDGothicB.ttc', name='BIZ UDGothic', style='normal', variant='normal', weight=700, stretch='normal', size='scalable')) = 10.335
2025-07-23 20:30:16,145 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\VerdanaPro-Italic.ttf', name='Verdana Pro', style='italic', variant='normal', weight=400, stretch='normal', size='scalable')) = 11.05
2025-07-23 20:30:16,145 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\corbell.ttf', name='Corbel', style='normal', variant='normal', weight=300, stretch='normal', size='scalable')) = 10.145
2025-07-23 20:30:16,145 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\simpbdo.ttf', name='Simplified Arabic', style='normal', variant='normal', weight=700, stretch='normal', size='scalable')) = 10.335
2025-07-23 20:30:16,145 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\PER_____.TTF', name='Perpetua', style='normal', variant='normal', weight=400, stretch='normal', size='scalable')) = 10.05
2025-07-23 20:30:16,145 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\LaoUIb.ttf', name='Lao UI', style='normal', variant='normal', weight=700, stretch='normal', size='scalable')) = 10.335
2025-07-23 20:30:16,145 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\FELIXTI.TTF', name='Felix Titling', style='normal', variant='normal', weight=400, stretch='normal', size='scalable')) = 10.05
2025-07-23 20:30:16,145 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\trebuc.ttf', name='Trebuchet MS', style='normal', variant='normal', weight=400, stretch='normal', size='scalable')) = 10.05
2025-07-23 20:30:16,145 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\HTOWERTI.TTF', name='High Tower Text', style='italic', variant='normal', weight=400, stretch='normal', size='scalable')) = 11.05
2025-07-23 20:30:16,145 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\BIZ-UDMinchoM.ttc', name='BIZ UDMincho', style='normal', variant='normal', weight=500, stretch='normal', size='scalable')) = 10.145
2025-07-23 20:30:16,145 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\UrdTypeb.ttf', name='Urdu Typesetting', style='normal', variant='normal', weight=700, stretch='normal', size='scalable')) = 10.335
2025-07-23 20:30:16,145 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\VerdanaPro-CondBlack.ttf', name='Verdana Pro', style='normal', variant='normal', weight=900, stretch='condensed', size='scalable')) = 10.725
2025-07-23 20:30:16,145 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\Candarali.ttf', name='Candara', style='italic', variant='normal', weight=400, stretch='normal', size='scalable')) = 11.05
2025-07-23 20:30:16,145 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\ROCKB.TTF', name='Rockwell', style='normal', variant='normal', weight=700, stretch='normal', size='scalable')) = 10.335
2025-07-23 20:30:16,145 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\msyi.ttf', name='Microsoft Yi Baiti', style='normal', variant='normal', weight=400, stretch='normal', size='scalable')) = 10.05
2025-07-23 20:30:16,145 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\comici.ttf', name='Comic Sans MS', style='italic', variant='normal', weight=400, stretch='normal', size='scalable')) = 11.05
2025-07-23 20:30:16,145 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\cour.ttf', name='Courier New', style='normal', variant='normal', weight=400, stretch='normal', size='scalable')) = 10.05
2025-07-23 20:30:16,145 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\CENTAUR.TTF', name='Centaur', style='normal', variant='normal', weight=400, stretch='normal', size='scalable')) = 10.05
2025-07-23 20:30:16,145 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\VerdanaPro-CondBold.ttf', name='Verdana Pro', style='normal', variant='normal', weight=700, stretch='condensed', size='scalable')) = 10.535
2025-07-23 20:30:16,145 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\GOUDOSB.TTF', name='Goudy Old Style', style='normal', variant='normal', weight=700, stretch='normal', size='scalable')) = 10.335
2025-07-23 20:30:16,145 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\MATURASC.TTF', name='Matura MT Script Capitals', style='normal', variant='normal', weight=400, stretch='normal', size='scalable')) = 10.05
2025-07-23 20:30:16,145 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\FRAHV.TTF', name='Franklin Gothic Heavy', style='normal', variant='normal', weight=400, stretch='normal', size='scalable')) = 10.05
2025-07-23 20:30:16,145 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\corbelb.ttf', name='Corbel', style='normal', variant='normal', weight=700, stretch='normal', size='scalable')) = 10.335
2025-07-23 20:30:16,145 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\ArialNova-Bold.ttf', name='Arial Nova', style='normal', variant='normal', weight=700, stretch='normal', size='scalable')) = 10.335
2025-07-23 20:30:16,145 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\timesbd.ttf', name='Times New Roman', style='normal', variant='normal', weight=700, stretch='normal', size='scalable')) = 10.335
2025-07-23 20:30:16,145 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\LSANSI.TTF', name='Lucida Sans', style='italic', variant='normal', weight=400, stretch='normal', size='scalable')) = 11.05
2025-07-23 20:30:16,145 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\utsaahi.ttf', name='Utsaah', style='italic', variant='normal', weight=400, stretch='normal', size='scalable')) = 11.05
2025-07-23 20:30:16,145 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\georgiaz.ttf', name='Georgia', style='italic', variant='normal', weight=700, stretch='normal', size='scalable')) = 11.335
2025-07-23 20:30:16,145 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\GillSansCondBoNova.ttf', name='Gill Sans Nova', style='normal', variant='normal', weight=700, stretch='condensed', size='scalable')) = 10.535
2025-07-23 20:30:16,145 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\lvnm.ttf', name='Levenim MT', style='normal', variant='normal', weight=400, stretch='normal', size='scalable')) = 10.05
2025-07-23 20:30:16,145 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\FRABKIT.TTF', name='Franklin Gothic Book', style='italic', variant='normal', weight=400, stretch='normal', size='scalable')) = 11.05
2025-07-23 20:30:16,145 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\PERBI___.TTF', name='Perpetua', style='italic', variant='normal', weight=700, stretch='normal', size='scalable')) = 11.335
2025-07-23 20:30:16,145 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\Vanib.ttf', name='Vani', style='normal', variant='normal', weight=700, stretch='normal', size='scalable')) = 10.335
2025-07-23 20:30:16,145 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\segoesc.ttf', name='Segoe Script', style='normal', variant='normal', weight=400, stretch='normal', size='scalable')) = 10.05
2025-07-23 20:30:16,145 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\TCMI____.TTF', name='Tw Cen MT', style='italic', variant='normal', weight=400, stretch='normal', size='scalable')) = 11.05
2025-07-23 20:30:16,160 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\BELLI.TTF', name='Bell MT', style='italic', variant='normal', weight=400, stretch='normal', size='scalable')) = 11.05
2025-07-23 20:30:16,160 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\javatext.ttf', name='Javanese Text', style='normal', variant='normal', weight=400, stretch='normal', size='scalable')) = 10.05
2025-07-23 20:30:16,160 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\HPSimplified.ttf', name='HP Simplified', style='normal', variant='normal', weight=700, stretch='normal', size='scalable')) = 10.335
2025-07-23 20:30:16,160 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\HPSimplified_Lt.ttf', name='HP Simplified', style='normal', variant='normal', weight=300, stretch='normal', size='scalable')) = 10.145
2025-07-23 20:30:16,160 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\FRADM.TTF', name='Franklin Gothic Demi', style='normal', variant='normal', weight=400, stretch='normal', size='scalable')) = 10.05
2025-07-23 20:30:16,160 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\TCCM____.TTF', name='Tw Cen MT Condensed', style='normal', variant='normal', weight=400, stretch='condensed', size='scalable')) = 10.25
2025-07-23 20:30:16,160 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\constan.ttf', name='Constantia', style='normal', variant='normal', weight=400, stretch='normal', size='scalable')) = 10.05
2025-07-23 20:30:16,160 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\HPSimplified_BdIt.ttf', name='HP Simplified', style='italic', variant='normal', weight=700, stretch='normal', size='scalable')) = 11.335
2025-07-23 20:30:16,160 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\Candaraz.ttf', name='Candara', style='italic', variant='normal', weight=700, stretch='normal', size='scalable')) = 11.335
2025-07-23 20:30:16,160 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\ArialNovaCond.ttf', name='Arial Nova', style='normal', variant='normal', weight=400, stretch='condensed', size='scalable')) = 10.25
2025-07-23 20:30:16,160 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\Sitka.ttc', name='Sitka Small', style='normal', variant='normal', weight=400, stretch='normal', size='scalable')) = 10.05
2025-07-23 20:30:16,160 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\consolab.ttf', name='Consolas', style='normal', variant='normal', weight=700, stretch='normal', size='scalable')) = 10.335
2025-07-23 20:30:16,160 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\NHaasGroteskTXPro-66MdIt.ttf', name='Neue Haas Grotesk Text Pro', style='italic', variant='normal', weight=500, stretch='normal', size='scalable')) = 11.145
2025-07-23 20:30:16,160 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\PERTILI.TTF', name='Perpetua Titling MT', style='normal', variant='normal', weight=300, stretch='normal', size='scalable')) = 10.145
2025-07-23 20:30:16,160 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\leelawad.ttf', name='Leelawadee', style='normal', variant='normal', weight=400, stretch='normal', size='scalable')) = 10.05
2025-07-23 20:30:16,160 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\VerdanaPro-CondLightItalic.ttf', name='Verdana Pro', style='italic', variant='normal', weight=300, stretch='condensed', size='scalable')) = 11.344999999999999
2025-07-23 20:30:16,160 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\VerdanaPro-CondItalic.ttf', name='Verdana Pro', style='italic', variant='normal', weight=400, stretch='condensed', size='scalable')) = 11.25
2025-07-23 20:30:16,160 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\calibrib.ttf', name='Calibri', style='normal', variant='normal', weight=700, stretch='normal', size='scalable')) = 10.335
2025-07-23 20:30:16,160 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\vrinda.ttf', name='Vrinda', style='normal', variant='normal', weight=400, stretch='normal', size='scalable')) = 10.05
2025-07-23 20:30:16,160 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\BOD_PSTC.TTF', name='Bodoni MT', style='normal', variant='normal', weight=300, stretch='normal', size='scalable')) = 10.145
2025-07-23 20:30:16,160 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\segoescb.ttf', name='Segoe Script', style='normal', variant='normal', weight=700, stretch='normal', size='scalable')) = 10.335
2025-07-23 20:30:16,160 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\trebucbd.ttf', name='Trebuchet MS', style='normal', variant='normal', weight=700, stretch='normal', size='scalable')) = 10.335
2025-07-23 20:30:16,160 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\KhmerUI.ttf', name='Khmer UI', style='normal', variant='normal', weight=400, stretch='normal', size='scalable')) = 10.05
2025-07-23 20:30:16,160 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\AGENCYB.TTF', name='Agency FB', style='normal', variant='normal', weight=700, stretch='normal', size='scalable')) = 10.335
2025-07-23 20:30:16,160 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\BOD_BLAR.TTF', name='Bodoni MT', style='normal', variant='normal', weight=900, stretch='normal', size='scalable')) = 10.525
2025-07-23 20:30:16,160 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\upclb.ttf', name='LilyUPC', style='normal', variant='normal', weight=700, stretch='normal', size='scalable')) = 10.335
2025-07-23 20:30:16,160 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\seguisym.ttf', name='Segoe UI Symbol', style='normal', variant='normal', weight=400, stretch='normal', size='scalable')) = 10.05
2025-07-23 20:30:16,160 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\couri.ttf', name='Courier New', style='italic', variant='normal', weight=400, stretch='normal', size='scalable')) = 11.05
2025-07-23 20:30:16,160 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\angsana.ttc', name='Angsana New', style='normal', variant='normal', weight=400, stretch='normal', size='scalable')) = 10.05
2025-07-23 20:30:16,160 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\lathab.ttf', name='Latha', style='normal', variant='normal', weight=700, stretch='normal', size='scalable')) = 10.335
2025-07-23 20:30:16,160 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\GillSansBoNova.ttf', name='Gill Sans Nova', style='normal', variant='normal', weight=700, stretch='normal', size='scalable')) = 10.335
2025-07-23 20:30:16,160 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\GeorgiaPro-Light.ttf', name='Georgia Pro', style='normal', variant='normal', weight=300, stretch='normal', size='scalable')) = 10.145
2025-07-23 20:30:16,160 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\ELEPHNTI.TTF', name='Elephant', style='italic', variant='normal', weight=400, stretch='normal', size='scalable')) = 11.05
2025-07-23 20:30:16,160 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\simhei.ttf', name='SimHei', style='normal', variant='normal', weight=400, stretch='normal', size='scalable')) = 10.05
2025-07-23 20:30:16,160 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\LeelUIsl.ttf', name='Leelawadee UI', style='normal', variant='normal', weight=350, stretch='normal', size='scalable')) = 10.0975
2025-07-23 20:30:16,160 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\CURLZ___.TTF', name='Curlz MT', style='normal', variant='normal', weight=400, stretch='normal', size='scalable')) = 10.05
2025-07-23 20:30:16,160 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\upcil.ttf', name='IrisUPC', style='normal', variant='normal', weight=400, stretch='normal', size='scalable')) = 10.05
2025-07-23 20:30:16,160 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\BRITANIC.TTF', name='Britannic Bold', style='normal', variant='normal', weight=400, stretch='normal', size='scalable')) = 10.05
2025-07-23 20:30:16,160 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\aparaj.ttf', name='Aparajita', style='normal', variant='normal', weight=400, stretch='normal', size='scalable')) = 10.05
2025-07-23 20:30:16,160 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\LeelawUI.ttf', name='Leelawadee UI', style='normal', variant='normal', weight=400, stretch='normal', size='scalable')) = 10.05
2025-07-23 20:30:16,160 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\upcei.ttf', name='EucrosiaUPC', style='italic', variant='normal', weight=400, stretch='normal', size='scalable')) = 11.05
2025-07-23 20:30:16,160 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\seguiemj.ttf', name='Segoe UI Emoji', style='normal', variant='normal', weight=400, stretch='normal', size='scalable')) = 10.05
2025-07-23 20:30:16,160 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\GeorgiaPro-CondBoldItalic.ttf', name='Georgia Pro', style='italic', variant='normal', weight=700, stretch='condensed', size='scalable')) = 11.535
2025-07-23 20:30:16,160 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\iskpotab.ttf', name='Iskoola Pota', style='normal', variant='normal', weight=700, stretch='normal', size='scalable')) = 10.335
2025-07-23 20:30:16,160 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\LeelaUIb.ttf', name='Leelawadee UI', style='normal', variant='normal', weight=700, stretch='normal', size='scalable')) = 10.335
2025-07-23 20:30:16,160 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\GARAIT.TTF', name='Garamond', style='italic', variant='normal', weight=400, stretch='normal', size='scalable')) = 11.05
2025-07-23 20:30:16,160 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\mriam.ttf', name='Miriam', style='normal', variant='normal', weight=400, stretch='normal', size='scalable')) = 10.05
2025-07-23 20:30:16,160 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\LHANDW.TTF', name='Lucida Handwriting', style='italic', variant='normal', weight=400, stretch='normal', size='scalable')) = 11.05
2025-07-23 20:30:16,160 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\timesbi.ttf', name='Times New Roman', style='italic', variant='normal', weight=700, stretch='normal', size='scalable')) = 11.335
2025-07-23 20:30:16,160 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\sylfaen.ttf', name='Sylfaen', style='normal', variant='normal', weight=400, stretch='normal', size='scalable')) = 10.05
2025-07-23 20:30:16,160 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\ROCCB___.TTF', name='Rockwell Condensed', style='normal', variant='normal', weight=700, stretch='condensed', size='scalable')) = 10.535
2025-07-23 20:30:16,160 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\RockwellNovaCond-Italic.ttf', name='Rockwell Nova', style='italic', variant='normal', weight=400, stretch='condensed', size='scalable')) = 11.25
2025-07-23 20:30:16,160 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\upcjb.ttf', name='JasmineUPC', style='normal', variant='normal', weight=700, stretch='normal', size='scalable')) = 10.335
2025-07-23 20:30:16,160 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\HPSimplified_Rg.ttf', name='HP Simplified', style='normal', variant='normal', weight=400, stretch='normal', size='scalable')) = 10.05
2025-07-23 20:30:16,160 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\timesi.ttf', name='Times New Roman', style='italic', variant='normal', weight=400, stretch='normal', size='scalable')) = 11.05
2025-07-23 20:30:16,160 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\gautamib.ttf', name='Gautami', style='normal', variant='normal', weight=700, stretch='normal', size='scalable')) = 10.335
2025-07-23 20:30:16,160 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\comicbd.ttf', name='Comic Sans MS', style='normal', variant='normal', weight=700, stretch='normal', size='scalable')) = 10.335
2025-07-23 20:30:16,160 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\yumin.ttf', name='Yu Mincho', style='normal', variant='normal', weight=400, stretch='normal', size='scalable')) = 10.05
2025-07-23 20:30:16,160 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\taileb.ttf', name='Microsoft Tai Le', style='normal', variant='normal', weight=700, stretch='normal', size='scalable')) = 10.335
2025-07-23 20:30:16,160 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\ENGR.TTF', name='Engravers MT', style='normal', variant='normal', weight=500, stretch='normal', size='scalable')) = 10.145
2025-07-23 20:30:16,160 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\lucon.ttf', name='Lucida Console', style='normal', variant='normal', weight=400, stretch='normal', size='scalable')) = 10.05
2025-07-23 20:30:16,160 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\GILBI___.TTF', name='Gill Sans MT', style='italic', variant='normal', weight=700, stretch='normal', size='scalable')) = 11.335
2025-07-23 20:30:16,160 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\msgothic.ttc', name='MS Gothic', style='normal', variant='normal', weight=400, stretch='normal', size='scalable')) = 10.05
2025-07-23 20:30:16,160 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\ArialNova-Italic.ttf', name='Arial Nova', style='italic', variant='normal', weight=400, stretch='normal', size='scalable')) = 11.05
2025-07-23 20:30:16,160 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\trebucit.ttf', name='Trebuchet MS', style='italic', variant='normal', weight=400, stretch='normal', size='scalable')) = 11.05
2025-07-23 20:30:16,160 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\segmdl2.ttf', name='Segoe MDL2 Assets', style='normal', variant='normal', weight=400, stretch='normal', size='scalable')) = 10.05
2025-07-23 20:30:16,160 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\Candarab.ttf', name='Candara', style='normal', variant='normal', weight=700, stretch='normal', size='scalable')) = 10.335
2025-07-23 20:30:16,160 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\kaiu.ttf', name='DFKai-SB', style='normal', variant='normal', weight=400, stretch='normal', size='scalable')) = 10.05
2025-07-23 20:30:16,160 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\GeorgiaPro-CondBlackItalic.ttf', name='Georgia Pro', style='italic', variant='normal', weight=900, stretch='condensed', size='scalable')) = 11.725
2025-07-23 20:30:16,160 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\malgunsl.ttf', name='Malgun Gothic', style='normal', variant='normal', weight=300, stretch='normal', size='scalable')) = 10.145
2025-07-23 20:30:16,160 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\RockwellNova-Light.ttf', name='Rockwell Nova', style='normal', variant='normal', weight=300, stretch='normal', size='scalable')) = 10.145
2025-07-23 20:30:16,160 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\utsaahbi.ttf', name='Utsaah', style='italic', variant='normal', weight=700, stretch='normal', size='scalable')) = 11.335
2025-07-23 20:30:16,160 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\moolbor.ttf', name='MoolBoran', style='normal', variant='normal', weight=400, stretch='normal', size='scalable')) = 10.05
2025-07-23 20:30:16,160 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\GeorgiaPro-BlackItalic.ttf', name='Georgia Pro', style='italic', variant='normal', weight=900, stretch='normal', size='scalable')) = 11.525
2025-07-23 20:30:16,160 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\palabi.ttf', name='Palatino Linotype', style='italic', variant='normal', weight=700, stretch='normal', size='scalable')) = 11.335
2025-07-23 20:30:16,160 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\pala.ttf', name='Palatino Linotype', style='normal', variant='normal', weight=400, stretch='normal', size='scalable')) = 10.05
2025-07-23 20:30:16,160 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\NHaasGroteskTXPro-56It.ttf', name='Neue Haas Grotesk Text Pro', style='italic', variant='normal', weight=400, stretch='normal', size='scalable')) = 11.05
2025-07-23 20:30:16,160 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\HATTEN.TTF', name='Haettenschweiler', style='normal', variant='normal', weight=400, stretch='normal', size='scalable')) = 10.05
2025-07-23 20:30:16,160 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\BOD_CB.TTF', name='Bodoni MT', style='normal', variant='normal', weight=700, stretch='condensed', size='scalable')) = 10.535
2025-07-23 20:30:16,160 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\CALISTI.TTF', name='Calisto MT', style='italic', variant='normal', weight=400, stretch='normal', size='scalable')) = 11.05
2025-07-23 20:30:16,160 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\NHaasGroteskTXPro-55Rg.ttf', name='Neue Haas Grotesk Text Pro', style='normal', variant='normal', weight=400, stretch='normal', size='scalable')) = 10.05
2025-07-23 20:30:16,160 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\kokilab.ttf', name='Kokila', style='normal', variant='normal', weight=700, stretch='normal', size='scalable')) = 10.335
2025-07-23 20:30:16,160 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\BKANT.TTF', name='Book Antiqua', style='normal', variant='normal', weight=400, stretch='normal', size='scalable')) = 10.05
2025-07-23 20:30:16,160 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\ROCKBI.TTF', name='Rockwell', style='italic', variant='normal', weight=700, stretch='normal', size='scalable')) = 11.335
2025-07-23 20:30:16,160 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\BOD_CBI.TTF', name='Bodoni MT', style='italic', variant='normal', weight=700, stretch='condensed', size='scalable')) = 11.535
2025-07-23 20:30:16,160 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\Nirmala.ttf', name='Nirmala UI', style='normal', variant='normal', weight=400, stretch='normal', size='scalable')) = 10.05
2025-07-23 20:30:16,160 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\malgunbd.ttf', name='Malgun Gothic', style='normal', variant='normal', weight=700, stretch='normal', size='scalable')) = 10.335
2025-07-23 20:30:16,160 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\SitkaI.ttc', name='Sitka Small', style='italic', variant='normal', weight=400, stretch='normal', size='scalable')) = 11.05
2025-07-23 20:30:16,160 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\ERASBD.TTF', name='Eras Bold ITC', style='normal', variant='normal', weight=400, stretch='normal', size='scalable')) = 10.05
2025-07-23 20:30:16,160 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\seguisb.ttf', name='Segoe UI', style='normal', variant='normal', weight=600, stretch='normal', size='scalable')) = 10.24
2025-07-23 20:30:16,160 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\simpfxo.ttf', name='Simplified Arabic Fixed', style='normal', variant='normal', weight=400, stretch='normal', size='scalable')) = 10.05
2025-07-23 20:30:16,160 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\SCRIPTBL.TTF', name='Script MT Bold', style='normal', variant='normal', weight=700, stretch='normal', size='scalable')) = 10.335
2025-07-23 20:30:16,160 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\Deng.ttf', name='DengXian', style='normal', variant='normal', weight=400, stretch='normal', size='scalable')) = 10.05
2025-07-23 20:30:16,160 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\Vani.ttf', name='Vani', style='normal', variant='normal', weight=400, stretch='normal', size='scalable')) = 10.05
2025-07-23 20:30:16,160 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\comic.ttf', name='Comic Sans MS', style='normal', variant='normal', weight=400, stretch='normal', size='scalable')) = 10.05
2025-07-23 20:30:16,160 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\msyh.ttc', name='Microsoft YaHei', style='normal', variant='normal', weight=400, stretch='normal', size='scalable')) = 10.05
2025-07-23 20:30:16,176 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\BRUSHSCI.TTF', name='Brush Script MT', style='italic', variant='normal', weight=400, stretch='normal', size='scalable')) = 11.05
2025-07-23 20:30:16,176 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\GOTHIC.TTF', name='Century Gothic', style='normal', variant='normal', weight=400, stretch='normal', size='scalable')) = 10.05
2025-07-23 20:30:16,176 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\GOTHICB.TTF', name='Century Gothic', style='normal', variant='normal', weight=700, stretch='normal', size='scalable')) = 10.335
2025-07-23 20:30:16,176 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\msuighub.ttf', name='Microsoft Uighur', style='normal', variant='normal', weight=700, stretch='normal', size='scalable')) = 10.335
2025-07-23 20:30:16,176 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\kokila.ttf', name='Kokila', style='normal', variant='normal', weight=400, stretch='normal', size='scalable')) = 10.05
2025-07-23 20:30:16,176 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\VerdanaPro-CondSemiBoldItalic.ttf', name='Verdana Pro', style='italic', variant='normal', weight=600, stretch='condensed', size='scalable')) = 11.44
2025-07-23 20:30:16,176 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\browalia.ttc', name='Browallia New', style='normal', variant='normal', weight=400, stretch='normal', size='scalable')) = 10.05
2025-07-23 20:30:16,176 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\SCHLBKI.TTF', name='Century Schoolbook', style='italic', variant='normal', weight=400, stretch='normal', size='scalable')) = 11.05
2025-07-23 20:30:16,176 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\PERB____.TTF', name='Perpetua', style='normal', variant='normal', weight=700, stretch='normal', size='scalable')) = 10.335
2025-07-23 20:30:16,176 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\RockwellNovaCond-Bold.ttf', name='Rockwell Nova', style='normal', variant='normal', weight=700, stretch='condensed', size='scalable')) = 10.535
2025-07-23 20:30:16,176 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\ELEPHNT.TTF', name='Elephant', style='normal', variant='normal', weight=400, stretch='normal', size='scalable')) = 10.05
2025-07-23 20:30:16,176 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\NIAGENG.TTF', name='Niagara Engraved', style='normal', variant='normal', weight=400, stretch='normal', size='scalable')) = 10.05
2025-07-23 20:30:16,176 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\courbi.ttf', name='Courier New', style='italic', variant='normal', weight=700, stretch='normal', size='scalable')) = 11.335
2025-07-23 20:30:16,176 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\impact.ttf', name='Impact', style='normal', variant='normal', weight=400, stretch='normal', size='scalable')) = 10.05
2025-07-23 20:30:16,176 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\trebucbi.ttf', name='Trebuchet MS', style='italic', variant='normal', weight=700, stretch='normal', size='scalable')) = 11.335
2025-07-23 20:30:16,176 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\upcdb.ttf', name='DilleniaUPC', style='normal', variant='normal', weight=700, stretch='normal', size='scalable')) = 10.335
2025-07-23 20:30:16,176 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\LTYPEB.TTF', name='Lucida Sans Typewriter', style='normal', variant='normal', weight=600, stretch='normal', size='scalable')) = 10.24
2025-07-23 20:30:16,176 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\NHaasGroteskTXPro-75Bd.ttf', name='Neue Haas Grotesk Text Pro', style='normal', variant='normal', weight=700, stretch='normal', size='scalable')) = 10.335
2025-07-23 20:30:16,176 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\BRADHITC.TTF', name='Bradley Hand ITC', style='normal', variant='normal', weight=400, stretch='normal', size='scalable')) = 10.05
2025-07-23 20:30:16,176 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\GeorgiaPro-BoldItalic.ttf', name='Georgia Pro', style='italic', variant='normal', weight=700, stretch='normal', size='scalable')) = 11.335
2025-07-23 20:30:16,176 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\palab.ttf', name='Palatino Linotype', style='normal', variant='normal', weight=700, stretch='normal', size='scalable')) = 10.335
2025-07-23 20:30:16,176 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\malgun.ttf', name='Malgun Gothic', style='normal', variant='normal', weight=400, stretch='normal', size='scalable')) = 10.05
2025-07-23 20:30:16,176 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\NirmalaS.ttf', name='Nirmala UI', style='normal', variant='normal', weight=350, stretch='normal', size='scalable')) = 10.0975
2025-07-23 20:30:16,176 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\upcii.ttf', name='IrisUPC', style='italic', variant='normal', weight=400, stretch='normal', size='scalable')) = 11.05
2025-07-23 20:30:16,176 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\calibriz.ttf', name='Calibri', style='italic', variant='normal', weight=700, stretch='normal', size='scalable')) = 11.335
2025-07-23 20:30:16,176 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\tahomabd.ttf', name='Tahoma', style='normal', variant='normal', weight=700, stretch='normal', size='scalable')) = 10.335
2025-07-23 20:30:16,176 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\msmincho.ttc', name='MS Mincho', style='normal', variant='normal', weight=400, stretch='normal', size='scalable')) = 10.05
2025-07-23 20:30:16,176 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\segoeuii.ttf', name='Segoe UI', style='italic', variant='normal', weight=400, stretch='normal', size='scalable')) = 11.05
2025-07-23 20:30:16,176 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\GARA.TTF', name='Garamond', style='normal', variant='normal', weight=400, stretch='normal', size='scalable')) = 10.05
2025-07-23 20:30:16,176 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\STENCIL.TTF', name='Stencil', style='normal', variant='normal', weight=400, stretch='normal', size='scalable')) = 10.05
2025-07-23 20:30:16,176 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\ebrima.ttf', name='Ebrima', style='normal', variant='normal', weight=400, stretch='normal', size='scalable')) = 10.05
2025-07-23 20:30:16,176 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\tahoma.ttf', name='Tahoma', style='normal', variant='normal', weight=400, stretch='normal', size='scalable')) = 10.05
2025-07-23 20:30:16,176 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\CASTELAR.TTF', name='Castellar', style='normal', variant='normal', weight=400, stretch='normal', size='scalable')) = 10.05
2025-07-23 20:30:16,176 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\CENTURY.TTF', name='Century', style='normal', variant='normal', weight=400, stretch='normal', size='scalable')) = 10.05
2025-07-23 20:30:16,176 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\segoeuiz.ttf', name='Segoe UI', style='italic', variant='normal', weight=700, stretch='normal', size='scalable')) = 11.335
2025-07-23 20:30:16,176 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\GeorgiaPro-SemiBoldItalic.ttf', name='Georgia Pro', style='italic', variant='normal', weight=600, stretch='normal', size='scalable')) = 11.24
2025-07-23 20:30:16,176 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\GILLUBCD.TTF', name='Gill Sans Ultra Bold Condensed', style='normal', variant='normal', weight=400, stretch='condensed', size='scalable')) = 10.25
2025-07-23 20:30:16,176 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\upcfb.ttf', name='FreesiaUPC', style='normal', variant='normal', weight=700, stretch='normal', size='scalable')) = 10.335
2025-07-23 20:30:16,176 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\GillSansCondLightItNova.ttf', name='Gill Sans Nova', style='italic', variant='normal', weight=300, stretch='condensed', size='scalable')) = 11.344999999999999
2025-07-23 20:30:16,176 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\VerdanaPro-Black.ttf', name='Verdana Pro', style='normal', variant='normal', weight=900, stretch='normal', size='scalable')) = 10.525
2025-07-23 20:30:16,176 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\arialnbi.ttf', name='Arial', style='italic', variant='normal', weight=700, stretch='condensed', size='scalable')) = 1.535
2025-07-23 20:30:16,176 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\upcibi.ttf', name='IrisUPC', style='italic', variant='normal', weight=700, stretch='normal', size='scalable')) = 11.335
2025-07-23 20:30:16,176 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\BOOKOS.TTF', name='Bookman Old Style', style='normal', variant='normal', weight=300, stretch='normal', size='scalable')) = 10.145
2025-07-23 20:30:16,176 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\GillSansCondLightNova.ttf', name='Gill Sans Nova', style='normal', variant='normal', weight=300, stretch='condensed', size='scalable')) = 10.344999999999999
2025-07-23 20:30:16,176 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\BELL.TTF', name='Bell MT', style='normal', variant='normal', weight=400, stretch='normal', size='scalable')) = 10.05
2025-07-23 20:30:16,176 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\OUTLOOK.TTF', name='MS Outlook', style='normal', variant='normal', weight=400, stretch='normal', size='scalable')) = 10.05
2025-07-23 20:30:16,176 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\ITCKRIST.TTF', name='Kristen ITC', style='normal', variant='normal', weight=400, stretch='normal', size='scalable')) = 10.05
2025-07-23 20:30:16,176 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\simkai.ttf', name='KaiTi', style='normal', variant='normal', weight=400, stretch='normal', size='scalable')) = 10.05
2025-07-23 20:30:16,176 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\aldhabi.ttf', name='Aldhabi', style='normal', variant='normal', weight=400, stretch='normal', size='scalable')) = 10.05
2025-07-23 20:30:16,176 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\FREESCPT.TTF', name='Freestyle Script', style='normal', variant='normal', weight=400, stretch='normal', size='scalable')) = 10.05
2025-07-23 20:30:16,176 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\PERI____.TTF', name='Perpetua', style='italic', variant='normal', weight=400, stretch='normal', size='scalable')) = 11.05
2025-07-23 20:30:16,176 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\arialn.ttf', name='Arial', style='normal', variant='normal', weight=400, stretch='condensed', size='scalable')) = 0.25
2025-07-23 20:30:16,176 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\YuGothM.ttc', name='Yu Gothic', style='normal', variant='normal', weight=500, stretch='normal', size='scalable')) = 10.145
2025-07-23 20:30:16,176 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\batang.ttc', name='Batang', style='normal', variant='normal', weight=400, stretch='normal', size='scalable')) = 10.05
2025-07-23 20:30:16,176 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\meiryo.ttc', name='Meiryo', style='normal', variant='normal', weight=400, stretch='normal', size='scalable')) = 10.05
2025-07-23 20:30:16,176 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\shrutib.ttf', name='Shruti', style='normal', variant='normal', weight=700, stretch='normal', size='scalable')) = 10.335
2025-07-23 20:30:16,176 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\kokilabi.ttf', name='Kokila', style='italic', variant='normal', weight=700, stretch='normal', size='scalable')) = 11.335
2025-07-23 20:30:16,176 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\GOUDYSTO.TTF', name='Goudy Stout', style='normal', variant='normal', weight=400, stretch='normal', size='scalable')) = 10.05
2025-07-23 20:30:16,176 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\seguihis.ttf', name='Segoe UI Historic', style='normal', variant='normal', weight=400, stretch='normal', size='scalable')) = 10.05
2025-07-23 20:30:16,176 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\majallab.ttf', name='Sakkal Majalla', style='normal', variant='normal', weight=700, stretch='normal', size='scalable')) = 10.335
2025-07-23 20:30:16,176 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\GILC____.TTF', name='Gill Sans MT Condensed', style='normal', variant='normal', weight=400, stretch='condensed', size='scalable')) = 10.25
2025-07-23 20:30:16,176 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\corbelli.ttf', name='Corbel', style='italic', variant='normal', weight=300, stretch='normal', size='scalable')) = 11.145
2025-07-23 20:30:16,176 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\seguisbi.ttf', name='Segoe UI', style='italic', variant='normal', weight=600, stretch='normal', size='scalable')) = 11.24
2025-07-23 20:30:16,176 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\BOD_CI.TTF', name='Bodoni MT', style='italic', variant='normal', weight=400, stretch='condensed', size='scalable')) = 11.25
2025-07-23 20:30:16,176 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\VerdanaPro-CondSemiBold.ttf', name='Verdana Pro', style='normal', variant='normal', weight=600, stretch='condensed', size='scalable')) = 10.44
2025-07-23 20:30:16,176 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\YuGothR.ttc', name='Yu Gothic', style='normal', variant='normal', weight=400, stretch='normal', size='scalable')) = 10.05
2025-07-23 20:30:16,176 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\ONYX.TTF', name='Onyx', style='normal', variant='normal', weight=400, stretch='normal', size='scalable')) = 10.05
2025-07-23 20:30:16,191 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\ArialNova-LightItalic.ttf', name='Arial Nova', style='italic', variant='normal', weight=300, stretch='normal', size='scalable')) = 11.145
2025-07-23 20:30:16,191 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\phagspab.ttf', name='Microsoft PhagsPa', style='normal', variant='normal', weight=700, stretch='normal', size='scalable')) = 10.335
2025-07-23 20:30:16,191 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\BOD_R.TTF', name='Bodoni MT', style='normal', variant='normal', weight=400, stretch='normal', size='scalable')) = 10.05
2025-07-23 20:30:16,191 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\REFSPCL.TTF', name='MS Reference Specialty', style='normal', variant='normal', weight=400, stretch='normal', size='scalable')) = 10.05
2025-07-23 20:30:16,191 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\gulim.ttc', name='Gulim', style='normal', variant='normal', weight=400, stretch='normal', size='scalable')) = 10.05
2025-07-23 20:30:16,191 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\ANTQUAB.TTF', name='Book Antiqua', style='normal', variant='normal', weight=700, stretch='normal', size='scalable')) = 10.335
2025-07-23 20:30:16,191 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\gisha.ttf', name='Gisha', style='normal', variant='normal', weight=400, stretch='normal', size='scalable')) = 10.05
2025-07-23 20:30:16,191 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\LBRITE.TTF', name='Lucida Bright', style='normal', variant='normal', weight=400, stretch='normal', size='scalable')) = 10.05
2025-07-23 20:30:16,191 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\calibrii.ttf', name='Calibri', style='italic', variant='normal', weight=400, stretch='normal', size='scalable')) = 11.05
2025-07-23 20:30:16,191 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\LTYPEO.TTF', name='Lucida Sans Typewriter', style='oblique', variant='normal', weight=400, stretch='normal', size='scalable')) = 11.05
2025-07-23 20:30:16,191 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\webdings.ttf', name='Webdings', style='normal', variant='normal', weight=400, stretch='normal', size='scalable')) = 10.05
2025-07-23 20:30:16,191 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\WINGDNG2.TTF', name='Wingdings 2', style='normal', variant='normal', weight=400, stretch='normal', size='scalable')) = 10.05
2025-07-23 20:30:16,191 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\corbel.ttf', name='Corbel', style='normal', variant='normal', weight=400, stretch='normal', size='scalable')) = 10.05
2025-07-23 20:30:16,191 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\MAIAN.TTF', name='Maiandra GD', style='normal', variant='normal', weight=400, stretch='normal', size='scalable')) = 10.05
2025-07-23 20:30:16,191 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\YuGothL.ttc', name='Yu Gothic', style='normal', variant='normal', weight=300, stretch='normal', size='scalable')) = 10.145
2025-07-23 20:30:16,191 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\ERASLGHT.TTF', name='Eras Light ITC', style='normal', variant='normal', weight=400, stretch='normal', size='scalable')) = 10.05
2025-07-23 20:30:16,191 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\segoeuisl.ttf', name='Segoe UI', style='normal', variant='normal', weight=350, stretch='normal', size='scalable')) = 10.0975
2025-07-23 20:30:16,191 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\RockwellNova-ExtraBold.ttf', name='Rockwell Nova', style='normal', variant='normal', weight=800, stretch='normal', size='scalable')) = 10.43
2025-07-23 20:30:16,191 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\RAGE.TTF', name='Rage Italic', style='italic', variant='normal', weight=400, stretch='normal', size='scalable')) = 11.05
2025-07-23 20:30:16,191 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\GillSansLightNova.ttf', name='Gill Sans Nova', style='normal', variant='normal', weight=300, stretch='normal', size='scalable')) = 10.145
2025-07-23 20:30:16,191 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\RockwellNova-BoldItalic.ttf', name='Rockwell Nova', style='italic', variant='normal', weight=700, stretch='normal', size='scalable')) = 11.335
2025-07-23 20:30:16,191 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\calibril.ttf', name='Calibri', style='normal', variant='normal', weight=300, stretch='normal', size='scalable')) = 10.145
2025-07-23 20:30:16,191 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\NHaasGroteskTXPro-65Md.ttf', name='Neue Haas Grotesk Text Pro', style='normal', variant='normal', weight=500, stretch='normal', size='scalable')) = 10.145
2025-07-23 20:30:16,191 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\OLDENGL.TTF', name='Old English Text MT', style='normal', variant='normal', weight=400, stretch='normal', size='scalable')) = 10.05
2025-07-23 20:30:16,191 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\BIZ-UDGothicR.ttc', name='BIZ UDGothic', style='normal', variant='normal', weight=400, stretch='normal', size='scalable')) = 10.05
2025-07-23 20:30:16,191 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\trado.ttf', name='Traditional Arabic', style='normal', variant='normal', weight=400, stretch='normal', size='scalable')) = 10.05
2025-07-23 20:30:16,191 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\upcdl.ttf', name='DilleniaUPC', style='normal', variant='normal', weight=400, stretch='normal', size='scalable')) = 10.05
2025-07-23 20:30:16,191 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\GeorgiaPro-CondBold.ttf', name='Georgia Pro', style='normal', variant='normal', weight=700, stretch='condensed', size='scalable')) = 10.535
2025-07-23 20:30:16,191 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\RockwellNova-ExtraBoldItalic.ttf', name='Rockwell Nova', style='italic', variant='normal', weight=800, stretch='normal', size='scalable')) = 11.43
2025-07-23 20:30:16,191 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\SCHLBKBI.TTF', name='Century Schoolbook', style='italic', variant='normal', weight=700, stretch='normal', size='scalable')) = 11.335
2025-07-23 20:30:16,191 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\shruti.ttf', name='Shruti', style='normal', variant='normal', weight=400, stretch='normal', size='scalable')) = 10.05
2025-07-23 20:30:16,191 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\RockwellNova.ttf', name='Rockwell Nova', style='normal', variant='normal', weight=400, stretch='normal', size='scalable')) = 10.05
2025-07-23 20:30:16,191 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\ArialNovaCond-Bold.ttf', name='Arial Nova', style='normal', variant='normal', weight=700, stretch='condensed', size='scalable')) = 10.535
2025-07-23 20:30:16,191 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\Sanskr.ttf', name='Sanskrit Text', style='normal', variant='normal', weight=400, stretch='normal', size='scalable')) = 10.05
2025-07-23 20:30:16,191 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\upcfbi.ttf', name='FreesiaUPC', style='italic', variant='normal', weight=700, stretch='normal', size='scalable')) = 11.335
2025-07-23 20:30:16,191 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\gadugi.ttf', name='Gadugi', style='normal', variant='normal', weight=400, stretch='normal', size='scalable')) = 10.05
2025-07-23 20:30:16,191 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\times.ttf', name='Times New Roman', style='normal', variant='normal', weight=400, stretch='normal', size='scalable')) = 10.05
2025-07-23 20:30:16,191 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\RockwellNova-Bold.ttf', name='Rockwell Nova', style='normal', variant='normal', weight=700, stretch='normal', size='scalable')) = 10.335
2025-07-23 20:30:16,191 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\VINERITC.TTF', name='Viner Hand ITC', style='normal', variant='normal', weight=400, stretch='normal', size='scalable')) = 10.05
2025-07-23 20:30:16,191 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\gautami.ttf', name='Gautami', style='normal', variant='normal', weight=400, stretch='normal', size='scalable')) = 10.05
2025-07-23 20:30:16,191 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\raavib.ttf', name='Raavi', style='normal', variant='normal', weight=700, stretch='normal', size='scalable')) = 10.335
2025-07-23 20:30:16,191 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\arialbi.ttf', name='Arial', style='italic', variant='normal', weight=700, stretch='normal', size='scalable')) = 1.335
2025-07-23 20:30:16,191 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\SHOWG.TTF', name='Showcard Gothic', style='normal', variant='normal', weight=400, stretch='normal', size='scalable')) = 10.05
2025-07-23 20:30:16,191 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\GeorgiaPro-CondSemiBold.ttf', name='Georgia Pro', style='normal', variant='normal', weight=600, stretch='condensed', size='scalable')) = 10.44
2025-07-23 20:30:16,191 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\GeorgiaPro-Regular.ttf', name='Georgia Pro', style='normal', variant='normal', weight=400, stretch='normal', size='scalable')) = 10.05
2025-07-23 20:30:16,191 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\NHaasGroteskTXPro-76BdIt.ttf', name='Neue Haas Grotesk Text Pro', style='italic', variant='normal', weight=700, stretch='normal', size='scalable')) = 11.335
2025-07-23 20:30:16,191 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\msuighur.ttf', name='Microsoft Uighur', style='normal', variant='normal', weight=400, stretch='normal', size='scalable')) = 10.05
2025-07-23 20:30:16,191 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\LTYPEBO.TTF', name='Lucida Sans Typewriter', style='oblique', variant='normal', weight=600, stretch='normal', size='scalable')) = 11.24
2025-07-23 20:30:16,191 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\LCALLIG.TTF', name='Lucida Calligraphy', style='italic', variant='normal', weight=400, stretch='normal', size='scalable')) = 11.05
2025-07-23 20:30:16,191 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\LBRITEI.TTF', name='Lucida Bright', style='italic', variant='normal', weight=400, stretch='normal', size='scalable')) = 11.05
2025-07-23 20:30:16,191 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\mingliu.ttc', name='MingLiU', style='normal', variant='normal', weight=400, stretch='normal', size='scalable')) = 10.05
2025-07-23 20:30:16,191 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\BOOKOSBI.TTF', name='Bookman Old Style', style='italic', variant='normal', weight=600, stretch='normal', size='scalable')) = 11.24
2025-07-23 20:30:16,191 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\ERASDEMI.TTF', name='Eras Demi ITC', style='normal', variant='normal', weight=400, stretch='normal', size='scalable')) = 10.05
2025-07-23 20:30:16,191 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\constanz.ttf', name='Constantia', style='italic', variant='normal', weight=700, stretch='normal', size='scalable')) = 11.335
2025-07-23 20:30:16,191 - DEBUG - findfont: Matching Arial:style=normal:variant=normal:weight=normal:stretch=normal:size=12.0 to Arial ('C:\\Windows\\Fonts\\arial.ttf') with score of 0.050000.
2025-07-23 20:30:16,610 - INFO - Using categorical units to plot a list of strings that are all parsable as floats or dates. If these strings should be plotted as numbers, cast to the appropriate data type before plotting.
2025-07-23 20:30:16,610 - INFO - Using categorical units to plot a list of strings that are all parsable as floats or dates. If these strings should be plotted as numbers, cast to the appropriate data type before plotting.
2025-07-23 20:30:16,876 - DEBUG - Database path: c:\Users\<USER>\Desktop\SupermarketSystem\supermarket.db
2025-07-23 20:30:17,266 - DEBUG - Database path: c:\Users\<USER>\Desktop\SupermarketSystem\supermarket.db
2025-07-23 20:30:17,844 - DEBUG - Database path: c:\Users\<USER>\Desktop\SupermarketSystem\supermarket.db
2025-07-23 20:30:18,204 - DEBUG - Database path: c:\Users\<USER>\Desktop\SupermarketSystem\supermarket.db
2025-07-23 20:30:18,266 - ERROR - Error creating page reports: 'ReportsPage' object has no attribute 'report_type'
2025-07-23 20:35:20,272 - DEBUG - Database path: c:\Users\<USER>\Desktop\SupermarketSystem\supermarket.db
2025-07-23 20:35:21,936 - DEBUG - Database path: c:\Users\<USER>\Desktop\SupermarketSystem\supermarket.db
2025-07-23 20:35:22,053 - DEBUG - Database path: c:\Users\<USER>\Desktop\SupermarketSystem\supermarket.db
2025-07-23 20:35:22,341 - DEBUG - Database path: c:\Users\<USER>\Desktop\SupermarketSystem\supermarket.db
2025-07-23 20:35:22,905 - INFO - Using categorical units to plot a list of strings that are all parsable as floats or dates. If these strings should be plotted as numbers, cast to the appropriate data type before plotting.
2025-07-23 20:35:22,908 - INFO - Using categorical units to plot a list of strings that are all parsable as floats or dates. If these strings should be plotted as numbers, cast to the appropriate data type before plotting.
2025-07-23 20:35:25,985 - ERROR - Error showing page reports: 'reports'
