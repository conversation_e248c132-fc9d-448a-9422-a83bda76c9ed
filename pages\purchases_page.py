import customtkinter as ctk
from tkinter import messagebox
from database import Database
from pdf_utils import PDFGenerator
from datetime import datetime
import os
import subprocess
import sys
import tkinter as tk


class PurchasesPage(ctk.CTkFrame):
    def __init__(self, parent, app):
        super().__init__(parent, fg_color="transparent")
        self.app = app
        self.db = Database()
        self.cart = []
        self.selected_product_id = None
        
        # Responsive settings
        self.grid_columnconfigure(0, weight=1)
        self.grid_rowconfigure(0, weight=1)
        
        self.create_widgets()
    
    def create_widgets(self):
        # Main frame
        main_frame = ctk.CTkFrame(self)
        main_frame.grid(row=0, column=0, sticky="nsew", padx=10, pady=10)
        main_frame.grid_columnconfigure(0, weight=1)
        main_frame.grid_rowconfigure(4, weight=1)
        
        # Title
        title = ctk.CTkLabel(
            main_frame, 
            text="Purchases Management",
            font=("Arial", 24, "bold"),
            text_color="#2c3e50"
        )
        title.grid(row=0, column=0, pady=20, sticky="n")
        
        # Product search section
        search_frame = ctk.CTkFrame(
            main_frame, 
            corner_radius=12,
            border_width=1,
            border_color="#e0e0e0"
        )
        search_frame.grid(row=1, column=0, sticky="ew", pady=10)
        search_frame.grid_columnconfigure(0, weight=1)
        
        ctk.CTkLabel(
            search_frame, 
            text="Product Search",
            font=("Arial", 16, "bold"),
            text_color="#2c3e50"
        ).grid(row=0, column=0, pady=10)
        
        # Search controls
        search_controls = ctk.CTkFrame(search_frame, fg_color="transparent")
        search_controls.grid(row=1, column=0, sticky="ew", padx=10, pady=5)
        search_controls.grid_columnconfigure(1, weight=1)
        
        ctk.CTkLabel(search_controls, text="Search:").grid(row=0, column=0, padx=5)
        self.search_entry = ctk.CTkEntry(search_controls)
        self.search_entry.grid(row=0, column=1, padx=5, sticky="ew")
        self.search_entry.bind("<Return>", lambda e: self.search_products())
        
        search_btn = ctk.CTkButton(
            search_controls, 
            text="Search", 
            width=80,
            corner_radius=8,
            command=self.search_products
        )
        search_btn.grid(row=0, column=2, padx=5)
        
        # Search results
        results_frame = ctk.CTkFrame(search_frame, fg_color="transparent")
        results_frame.grid(row=2, column=0, sticky="ew", padx=10, pady=10)
        
        # Table headers
        headers = ctk.CTkFrame(results_frame, fg_color="#f5f5f5", height=40)
        headers.pack(fill="x")
        
        columns = ["Product", "Current Qty", "Price"]
        for i, col in enumerate(columns):
            ctk.CTkLabel(
                headers, 
                text=col,
                font=("Arial", 14, "bold"),
                text_color="#2c3e50"
            ).place(relx=0.2 + i*0.3, rely=0.5, anchor="center")
        
        # Scrollable results
        scrollable_results = ctk.CTkScrollableFrame(results_frame, height=150)
        scrollable_results.pack(fill="x", pady=2)
        
        self.search_results_frame = ctk.CTkFrame(scrollable_results, fg_color="transparent")
        self.search_results_frame.pack(fill="x", pady=2)
        
        # Cart section
        cart_frame = ctk.CTkFrame(
            main_frame, 
            corner_radius=12,
            border_width=1,
            border_color="#e0e0e0"
        )
        cart_frame.grid(row=2, column=0, sticky="nsew", pady=10)
        cart_frame.grid_columnconfigure(0, weight=1)
        cart_frame.grid_rowconfigure(1, weight=1)
        
        ctk.CTkLabel(
            cart_frame, 
            text="Purchase Cart",
            font=("Arial", 16, "bold"),
            text_color="#2c3e50"
        ).grid(row=0, column=0, pady=10)
        
        # Cart table
        cart_table_container = ctk.CTkFrame(cart_frame, fg_color="transparent")
        cart_table_container.grid(row=1, column=0, sticky="nsew", padx=10, pady=5)
        cart_table_container.grid_columnconfigure(0, weight=1)
        cart_table_container.grid_rowconfigure(0, weight=1)
        
        self.cart_table = ctk.CTkScrollableFrame(
            cart_table_container, 
            fg_color="transparent",
            height=200
        )
        self.cart_table.grid(row=0, column=0, sticky="nsew")
        
        # Cart controls
        cart_controls = ctk.CTkFrame(cart_frame, fg_color="transparent")
        cart_controls.grid(row=2, column=0, sticky="ew", pady=10, padx=10)
        
        ctk.CTkLabel(cart_controls, text="Qty:").grid(row=0, column=0, padx=5)
        self.quantity_entry = ctk.CTkEntry(cart_controls, width=60)
        self.quantity_entry.grid(row=0, column=1, padx=5)
        self.quantity_entry.insert(0, "1")
        
        ctk.CTkLabel(cart_controls, text="Price:").grid(row=0, column=2, padx=5)
        self.price_entry = ctk.CTkEntry(cart_controls, width=80)
        self.price_entry.grid(row=0, column=3, padx=5)
        
        add_btn = ctk.CTkButton(
            cart_controls, 
            text="Add to Cart", 
            width=120,
            corner_radius=8,
            command=self.add_to_cart
        )
        add_btn.grid(row=0, column=4, padx=10)
        
        remove_btn = ctk.CTkButton(
            cart_controls, 
            text="Remove from Cart", 
            width=120,
            corner_radius=8,
            fg_color="#e74c3c",
            hover_color="#c0392b",
            command=self.remove_from_cart
        )
        remove_btn.grid(row=0, column=5, padx=5)
        
        # Supplier info
        supplier_frame = ctk.CTkFrame(main_frame, fg_color="transparent")
        supplier_frame.grid(row=3, column=0, sticky="ew", pady=10)
        
        ctk.CTkLabel(supplier_frame, text="Supplier:").grid(row=0, column=0, padx=5)
        self.supplier_name = ctk.CTkEntry(supplier_frame, width=250)
        self.supplier_name.grid(row=0, column=1, padx=5)
        
        # Payment section
        payment_frame = ctk.CTkFrame(main_frame, fg_color="transparent")
        payment_frame.grid(row=4, column=0, sticky="ew", pady=10)
        
        # Total frame
        total_frame = ctk.CTkFrame(payment_frame, fg_color="transparent")
        total_frame.grid(row=0, column=0, sticky="w")
        
        ctk.CTkLabel(
            total_frame, 
            text="Invoice Total:",
            font=("Arial", 16, "bold")
        ).grid(row=0, column=0, padx=5)
        
        self.total_label = ctk.CTkLabel(
            total_frame, 
            text="0.00 $",
            font=("Arial", 18, "bold"),
            text_color="#27ae60"
        )
        self.total_label.grid(row=0, column=1, padx=5)
        
        # Invoice option
        self.invoice_option = ctk.BooleanVar(value=True)
        invoice_check = ctk.CTkCheckBox(
            payment_frame,
            text="Create Invoice",
            variable=self.invoice_option
        )
        invoice_check.grid(row=1, column=0, pady=5, sticky="w")
        
        # Action buttons
        btn_frame = ctk.CTkFrame(main_frame, fg_color="transparent")
        btn_frame.grid(row=5, column=0, sticky="ew", pady=10)
        btn_frame.grid_columnconfigure(0, weight=1)
        btn_frame.grid_columnconfigure(1, weight=1)
        
        clear_btn = ctk.CTkButton(
            btn_frame, 
            text="Clear Cart", 
            width=150,
            height=40,
            corner_radius=8,
            font=("Arial", 14, "bold"),
            fg_color="#e67e22",
            hover_color="#d35400",
            command=self.clear_cart
        )
        clear_btn.grid(row=0, column=0, padx=5)
        
        print_btn = ctk.CTkButton(
            btn_frame, 
            text="Print Invoice", 
            width=150,
            height=40,
            corner_radius=8,
            font=("Arial", 14, "bold"),
            command=self.print_invoice
        )
        print_btn.grid(row=0, column=1, padx=5)
        
        save_btn = ctk.CTkButton(
            btn_frame, 
            text=self.app.lang.get("complete_purchase"), 
            width=150,
            height=40,
            corner_radius=8,
            font=("Arial", 14, "bold"),
            fg_color="#27ae60",
            hover_color="#219653",
            command=self.save_invoice
        )
        save_btn.grid(row=0, column=2, padx=5)
        
        # Load initial products
        self.search_products()
    
    def search_products(self):
        """Search for products"""
        query = self.search_entry.get()
        products = self.db.get_products_by_name_or_barcode(query)
        
        # Clear previous results
        for widget in self.search_results_frame.winfo_children():
            widget.destroy()
        
        # Display results
        for product in products:
            row = ctk.CTkFrame(self.search_results_frame, height=40)
            row.pack(fill="x", pady=2)
            
            # Store product ID
            row.product_id = product[0]
            
            # Make row selectable
            row.bind("<Button-1>", lambda e, r=row: self.select_product(r))
            
            # Product details
            values = (product[1], str(product[3]), f"{product[2]:.2f} $")
            for i, value in enumerate(values):
                ctk.CTkLabel(
                    row, 
                    text=value,
                    font=("Arial", 13),
                    text_color="#555555"
                ).place(relx=0.2 + i*0.3, rely=0.5, anchor="center")
            
            # Add to cart button
            add_btn = ctk.CTkButton(
                row, 
                text="+",
                width=30,
                height=30,
                corner_radius=15,
                fg_color="#27ae60",
                hover_color="#219653",
                command=lambda r=row: self.add_product_to_cart(r.product_id)
            )
            add_btn.place(relx=0.95, rely=0.5, anchor="center")
    
    def select_product(self, row):
        """Select a product"""
        for widget in self.search_results_frame.winfo_children():
            widget.configure(fg_color="transparent")
        row.configure(fg_color="#e3f2fd")
        self.selected_product_id = row.product_id
    
    def add_product_to_cart(self, product_id):
        """Add product to cart"""
        try:
            quantity = int(self.quantity_entry.get())
            price = float(self.price_entry.get())
            if quantity <= 0 or price <= 0:
                raise ValueError("Values must be positive")
        except Exception as e:
            messagebox.showerror("Error", "Quantity and Price must be positive numbers")
            return
        
        product = self.db.get_product_by_id(product_id)
        if not product:
            return
        
        # Check if product already in cart
        for item in self.cart:
            if item["id"] == product_id:
                item["quantity"] += quantity
                item["price"] = price
                item["total"] = price * item["quantity"]
                self.update_cart_table()
                return
        
        # Add new item to cart
        self.cart.append({
            "id": product_id,
            "name": product[1],
            "price": price,
            "quantity": quantity,
            "total": price * quantity
        })
        
        self.update_cart_table()
    
    def add_to_cart(self):
        """Add selected product to cart"""
        if not self.selected_product_id:
            messagebox.showwarning("Warning", "Please select a product")
            return
        
        self.add_product_to_cart(self.selected_product_id)
    
    def update_cart_table(self):
        """Update cart display"""
        for widget in self.cart_table.winfo_children():
            widget.destroy()
        
        total_invoice = 0
        for item in self.cart:
            row = ctk.CTkFrame(self.cart_table, height=40)
            row.pack(fill="x", pady=2)
            
            # Store item ID
            row.item_id = item['id']
            
            # Display item details
            values = (item['name'], item['quantity'], f"{item['price']:.2f} $", f"{item['total']:.2f} $")
            for i, value in enumerate(values):
                ctk.CTkLabel(
                    row, 
                    text=value,
                    font=("Arial", 13),
                    text_color="#555555"
                ).place(relx=0.15 + i*0.25, rely=0.5, anchor="center")
            
            # Remove button
            remove_btn = ctk.CTkButton(
                row, 
                text="x",
                width=30,
                height=30,
                corner_radius=15,
                fg_color="#e74c3c",
                hover_color="#c0392b",
                command=lambda r=row: self.remove_item_from_cart(r.item_id)
            )
            remove_btn.place(relx=0.95, rely=0.5, anchor="center")
            
            total_invoice += item['total']
        
        self.total_label.configure(text=f"{total_invoice:.2f} $")
    
    def remove_item_from_cart(self, product_id):
        """Remove item from cart"""
        for i, item in enumerate(self.cart):
            if item['id'] == product_id:
                del self.cart[i]
                break
        
        self.update_cart_table()
    
    def remove_from_cart(self):
        """Remove selected item from cart"""
        if not self.cart:
            return
        
        if self.selected_product_id:
            self.remove_item_from_cart(self.selected_product_id)
    
    def clear_cart(self):
        """Clear the cart"""
        self.cart = []
        self.update_cart_table()
        messagebox.showinfo("Success", "Cart cleared")
    
    def save_invoice(self):
        """Save the invoice"""
        if not self.cart:
            messagebox.showwarning("Warning", "Cart is empty")
            return
        
        supplier = self.supplier_name.get() or "Unknown Supplier"
        
        try:
            # Record purchases
            for item in self.cart:
                self.db.record_purchase(
                    item['id'], 
                    item['quantity'], 
                    item['price'], 
                    supplier
                )
            
            # Create invoice if requested
            if self.invoice_option.get():
                total = sum(item['total'] for item in self.cart)
                invoice_id = self.db.create_purchase_invoice(total, f"Purchase from {supplier}")
                if invoice_id:
                    messagebox.showinfo("Success", f"Invoice #{invoice_id} saved successfully")
                else:
                    messagebox.showerror("Error", "Failed to create invoice")
            else:
                messagebox.showinfo("Success", "Purchase completed without invoice")
            
            # Reset form
            self.clear_cart()
            self.supplier_name.delete(0, tk.END)
        except Exception as e:
            messagebox.showerror("Error", f"Failed to save invoice: {str(e)}")
    
    def print_invoice(self):
        """Print the invoice"""
        if not self.cart:
            messagebox.showwarning("Warning", "Cart is empty")
            return
    
        supplier = self.supplier_name.get() or "Unknown Supplier"
        total = sum(item['total'] for item in self.cart)
    
        try:
            # Create PDF invoice
            invoice_id = datetime.now().strftime("%Y%m%d%H%M%S")
            filename = PDFGenerator.create_purchase_invoice(
                invoice_id,
                datetime.now().strftime("%Y-%m-%d %H:%M:%S"),
                supplier,
                self.cart,
                total,
                self.db
            )
        
            if os.path.exists(filename):
                # Open the invoice automatically
                if os.name == 'nt':  # Windows
                    os.startfile(filename)
                elif os.name == 'posix':  # macOS, Linux
                    if sys.platform == 'darwin':
                        subprocess.run(['open', filename])
                    else:
                        subprocess.run(['xdg-open', filename])
                else:
                    messagebox.showinfo("Open Invoice", f"Invoice saved at: {filename}")
                
                messagebox.showinfo("Success", "Invoice created and opened")
            else:
                messagebox.showerror("Error", f"Failed to create invoice file: {filename}")
        except Exception as e:
            messagebox.showerror("Error", f"Failed to create invoice: {str(e)}")            
    
    def refresh_data(self):
        """Refresh data"""
        self.clear_cart()
        self.search_products()
    
    def update_language(self):
        """Update all text elements when language changes"""
        # إعادة إنشاء الصفحة بالكامل مع الترجمة الجديدة
        for widget in self.winfo_children():
            widget.destroy()
        self.create_widgets()
