2025-07-23 19:47:46,050 - INFO - Operating System: win32
2025-07-23 19:47:46,051 - INFO - Python Version: 3.12.8 (tags/v3.12.8:2dc476b, Dec  3 2024, 19:30:04) [MSC v.1942 64 bit (AMD64)]
2025-07-23 19:47:46,053 - INFO - Current Directory: C:\Users\<USER>\Desktop\SupermarketSystem
2025-07-23 19:47:46,054 - INFO - Executable Path: C:\Users\<USER>\AppData\Local\Programs\Python\Python312\python.exe
2025-07-23 19:47:46,054 - INFO - Application started
2025-07-23 19:47:51,211 - DEBUG - Database path: c:\Users\<USER>\Desktop\SupermarketSystem\supermarket.db
2025-07-23 19:47:51,841 - DEBUG - Loaded backend qtagg version 6.9.0.
2025-07-23 19:47:52,397 - DEBUG - findfont: Matching sans\-serif:style=normal:variant=normal:weight=normal:stretch=normal:size=10.0.
2025-07-23 19:47:52,399 - DEBUG - findfont: score(FontEntry(fname='C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\site-packages\\matplotlib\\mpl-data\\fonts\\ttf\\cmss10.ttf', name='cmss10', style='normal', variant='normal', weight=400, stretch='normal', size='scalable')) = 10.05
2025-07-23 19:47:52,399 - DEBUG - findfont: score(FontEntry(fname='C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\site-packages\\matplotlib\\mpl-data\\fonts\\ttf\\DejaVuSansMono.ttf', name='DejaVu Sans Mono', style='normal', variant='normal', weight=400, stretch='normal', size='scalable')) = 10.05
2025-07-23 19:47:52,399 - DEBUG - findfont: score(FontEntry(fname='C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\site-packages\\matplotlib\\mpl-data\\fonts\\ttf\\STIXGeneral.ttf', name='STIXGeneral', style='normal', variant='normal', weight=400, stretch='normal', size='scalable')) = 10.05
2025-07-23 19:47:52,400 - DEBUG - findfont: score(FontEntry(fname='C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\site-packages\\matplotlib\\mpl-data\\fonts\\ttf\\STIXGeneralItalic.ttf', name='STIXGeneral', style='italic', variant='normal', weight=400, stretch='normal', size='scalable')) = 11.05
2025-07-23 19:47:52,400 - DEBUG - findfont: score(FontEntry(fname='C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\site-packages\\matplotlib\\mpl-data\\fonts\\ttf\\STIXNonUniBol.ttf', name='STIXNonUnicode', style='normal', variant='normal', weight=700, stretch='normal', size='scalable')) = 10.335
2025-07-23 19:47:52,400 - DEBUG - findfont: score(FontEntry(fname='C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\site-packages\\matplotlib\\mpl-data\\fonts\\ttf\\cmsy10.ttf', name='cmsy10', style='normal', variant='normal', weight=400, stretch='normal', size='scalable')) = 10.05
2025-07-23 19:47:52,400 - DEBUG - findfont: score(FontEntry(fname='C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\site-packages\\matplotlib\\mpl-data\\fonts\\ttf\\DejaVuSansMono-Oblique.ttf', name='DejaVu Sans Mono', style='oblique', variant='normal', weight=400, stretch='normal', size='scalable')) = 11.05
2025-07-23 19:47:52,400 - DEBUG - findfont: score(FontEntry(fname='C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\site-packages\\matplotlib\\mpl-data\\fonts\\ttf\\DejaVuSansMono-Bold.ttf', name='DejaVu Sans Mono', style='normal', variant='normal', weight=700, stretch='normal', size='scalable')) = 10.335
2025-07-23 19:47:52,400 - DEBUG - findfont: score(FontEntry(fname='C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\site-packages\\matplotlib\\mpl-data\\fonts\\ttf\\DejaVuSerif-Italic.ttf', name='DejaVu Serif', style='italic', variant='normal', weight=400, stretch='normal', size='scalable')) = 11.05
2025-07-23 19:47:52,401 - DEBUG - findfont: score(FontEntry(fname='C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\site-packages\\matplotlib\\mpl-data\\fonts\\ttf\\cmtt10.ttf', name='cmtt10', style='normal', variant='normal', weight=400, stretch='normal', size='scalable')) = 10.05
2025-07-23 19:47:52,401 - DEBUG - findfont: score(FontEntry(fname='C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\site-packages\\matplotlib\\mpl-data\\fonts\\ttf\\STIXSizThreeSymReg.ttf', name='STIXSizeThreeSym', style='normal', variant='normal', weight=400, stretch='normal', size='scalable')) = 10.05
2025-07-23 19:47:52,401 - DEBUG - findfont: score(FontEntry(fname='C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\site-packages\\matplotlib\\mpl-data\\fonts\\ttf\\cmex10.ttf', name='cmex10', style='normal', variant='normal', weight=400, stretch='normal', size='scalable')) = 10.05
2025-07-23 19:47:52,401 - DEBUG - findfont: score(FontEntry(fname='C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\site-packages\\matplotlib\\mpl-data\\fonts\\ttf\\STIXSizFourSymBol.ttf', name='STIXSizeFourSym', style='normal', variant='normal', weight=700, stretch='normal', size='scalable')) = 10.335
2025-07-23 19:47:52,402 - DEBUG - findfont: score(FontEntry(fname='C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\site-packages\\matplotlib\\mpl-data\\fonts\\ttf\\STIXSizFourSymReg.ttf', name='STIXSizeFourSym', style='normal', variant='normal', weight=400, stretch='normal', size='scalable')) = 10.05
2025-07-23 19:47:52,402 - DEBUG - findfont: score(FontEntry(fname='C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\site-packages\\matplotlib\\mpl-data\\fonts\\ttf\\STIXSizTwoSymBol.ttf', name='STIXSizeTwoSym', style='normal', variant='normal', weight=700, stretch='normal', size='scalable')) = 10.335
2025-07-23 19:47:52,402 - DEBUG - findfont: score(FontEntry(fname='C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\site-packages\\matplotlib\\mpl-data\\fonts\\ttf\\STIXSizTwoSymReg.ttf', name='STIXSizeTwoSym', style='normal', variant='normal', weight=400, stretch='normal', size='scalable')) = 10.05
2025-07-23 19:47:52,402 - DEBUG - findfont: score(FontEntry(fname='C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\site-packages\\matplotlib\\mpl-data\\fonts\\ttf\\STIXSizFiveSymReg.ttf', name='STIXSizeFiveSym', style='normal', variant='normal', weight=400, stretch='normal', size='scalable')) = 10.05
2025-07-23 19:47:52,402 - DEBUG - findfont: score(FontEntry(fname='C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\site-packages\\matplotlib\\mpl-data\\fonts\\ttf\\cmmi10.ttf', name='cmmi10', style='normal', variant='normal', weight=400, stretch='normal', size='scalable')) = 10.05
2025-07-23 19:47:52,402 - DEBUG - findfont: score(FontEntry(fname='C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\site-packages\\matplotlib\\mpl-data\\fonts\\ttf\\STIXSizOneSymReg.ttf', name='STIXSizeOneSym', style='normal', variant='normal', weight=400, stretch='normal', size='scalable')) = 10.05
2025-07-23 19:47:52,403 - DEBUG - findfont: score(FontEntry(fname='C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\site-packages\\matplotlib\\mpl-data\\fonts\\ttf\\DejaVuSansMono-BoldOblique.ttf', name='DejaVu Sans Mono', style='oblique', variant='normal', weight=700, stretch='normal', size='scalable')) = 11.335
2025-07-23 19:47:52,403 - DEBUG - findfont: score(FontEntry(fname='C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\site-packages\\matplotlib\\mpl-data\\fonts\\ttf\\STIXNonUni.ttf', name='STIXNonUnicode', style='normal', variant='normal', weight=400, stretch='normal', size='scalable')) = 10.05
2025-07-23 19:47:52,403 - DEBUG - findfont: score(FontEntry(fname='C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\site-packages\\matplotlib\\mpl-data\\fonts\\ttf\\DejaVuSerif-BoldItalic.ttf', name='DejaVu Serif', style='italic', variant='normal', weight=700, stretch='normal', size='scalable')) = 11.335
2025-07-23 19:47:52,403 - DEBUG - findfont: score(FontEntry(fname='C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\site-packages\\matplotlib\\mpl-data\\fonts\\ttf\\STIXGeneralBol.ttf', name='STIXGeneral', style='normal', variant='normal', weight=700, stretch='normal', size='scalable')) = 10.335
2025-07-23 19:47:52,403 - DEBUG - findfont: score(FontEntry(fname='C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\site-packages\\matplotlib\\mpl-data\\fonts\\ttf\\DejaVuSerif.ttf', name='DejaVu Serif', style='normal', variant='normal', weight=400, stretch='normal', size='scalable')) = 10.05
2025-07-23 19:47:52,404 - DEBUG - findfont: score(FontEntry(fname='C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\site-packages\\matplotlib\\mpl-data\\fonts\\ttf\\STIXNonUniBolIta.ttf', name='STIXNonUnicode', style='italic', variant='normal', weight=700, stretch='normal', size='scalable')) = 11.335
2025-07-23 19:47:52,404 - DEBUG - findfont: score(FontEntry(fname='C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\site-packages\\matplotlib\\mpl-data\\fonts\\ttf\\STIXSizOneSymBol.ttf', name='STIXSizeOneSym', style='normal', variant='normal', weight=700, stretch='normal', size='scalable')) = 10.335
2025-07-23 19:47:52,404 - DEBUG - findfont: score(FontEntry(fname='C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\site-packages\\matplotlib\\mpl-data\\fonts\\ttf\\DejaVuSerif-Bold.ttf', name='DejaVu Serif', style='normal', variant='normal', weight=700, stretch='normal', size='scalable')) = 10.335
2025-07-23 19:47:52,404 - DEBUG - findfont: score(FontEntry(fname='C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\site-packages\\matplotlib\\mpl-data\\fonts\\ttf\\DejaVuSans-Oblique.ttf', name='DejaVu Sans', style='oblique', variant='normal', weight=400, stretch='normal', size='scalable')) = 1.05
2025-07-23 19:47:52,404 - DEBUG - findfont: score(FontEntry(fname='C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\site-packages\\matplotlib\\mpl-data\\fonts\\ttf\\DejaVuSans-Bold.ttf', name='DejaVu Sans', style='normal', variant='normal', weight=700, stretch='normal', size='scalable')) = 0.33499999999999996
2025-07-23 19:47:52,405 - DEBUG - findfont: score(FontEntry(fname='C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\site-packages\\matplotlib\\mpl-data\\fonts\\ttf\\cmr10.ttf', name='cmr10', style='normal', variant='normal', weight=400, stretch='normal', size='scalable')) = 10.05
2025-07-23 19:47:52,405 - DEBUG - findfont: score(FontEntry(fname='C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\site-packages\\matplotlib\\mpl-data\\fonts\\ttf\\cmb10.ttf', name='cmb10', style='normal', variant='normal', weight=400, stretch='normal', size='scalable')) = 10.05
2025-07-23 19:47:52,405 - DEBUG - findfont: score(FontEntry(fname='C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\site-packages\\matplotlib\\mpl-data\\fonts\\ttf\\DejaVuSansDisplay.ttf', name='DejaVu Sans Display', style='normal', variant='normal', weight=400, stretch='normal', size='scalable')) = 10.05
2025-07-23 19:47:52,406 - DEBUG - findfont: score(FontEntry(fname='C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\site-packages\\matplotlib\\mpl-data\\fonts\\ttf\\DejaVuSans.ttf', name='DejaVu Sans', style='normal', variant='normal', weight=400, stretch='normal', size='scalable')) = 0.05
2025-07-23 19:47:52,406 - DEBUG - findfont: score(FontEntry(fname='C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\site-packages\\matplotlib\\mpl-data\\fonts\\ttf\\STIXSizThreeSymBol.ttf', name='STIXSizeThreeSym', style='normal', variant='normal', weight=700, stretch='normal', size='scalable')) = 10.335
2025-07-23 19:47:52,406 - DEBUG - findfont: score(FontEntry(fname='C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\site-packages\\matplotlib\\mpl-data\\fonts\\ttf\\STIXGeneralBolIta.ttf', name='STIXGeneral', style='italic', variant='normal', weight=700, stretch='normal', size='scalable')) = 11.335
2025-07-23 19:47:52,406 - DEBUG - findfont: score(FontEntry(fname='C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\site-packages\\matplotlib\\mpl-data\\fonts\\ttf\\DejaVuSerifDisplay.ttf', name='DejaVu Serif Display', style='normal', variant='normal', weight=400, stretch='normal', size='scalable')) = 10.05
2025-07-23 19:47:52,406 - DEBUG - findfont: score(FontEntry(fname='C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\site-packages\\matplotlib\\mpl-data\\fonts\\ttf\\DejaVuSans-BoldOblique.ttf', name='DejaVu Sans', style='oblique', variant='normal', weight=700, stretch='normal', size='scalable')) = 1.335
2025-07-23 19:47:52,407 - DEBUG - findfont: score(FontEntry(fname='C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\site-packages\\matplotlib\\mpl-data\\fonts\\ttf\\STIXNonUniIta.ttf', name='STIXNonUnicode', style='italic', variant='normal', weight=400, stretch='normal', size='scalable')) = 11.05
2025-07-23 19:47:52,407 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\VerdanaPro-Regular.ttf', name='Verdana Pro', style='normal', variant='normal', weight=400, stretch='normal', size='scalable')) = 10.05
2025-07-23 19:47:52,407 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\ArialNova.ttf', name='Arial Nova', style='normal', variant='normal', weight=400, stretch='normal', size='scalable')) = 10.05
2025-07-23 19:47:52,407 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\dokchamp.ttf', name='DokChampa', style='normal', variant='normal', weight=400, stretch='normal', size='scalable')) = 10.05
2025-07-23 19:47:52,407 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\ROCKI.TTF', name='Rockwell', style='italic', variant='normal', weight=400, stretch='normal', size='scalable')) = 11.05
2025-07-23 19:47:52,407 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\upcebi.ttf', name='EucrosiaUPC', style='italic', variant='normal', weight=700, stretch='normal', size='scalable')) = 11.335
2025-07-23 19:47:52,408 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\LFAXDI.TTF', name='Lucida Fax', style='italic', variant='normal', weight=600, stretch='normal', size='scalable')) = 11.24
2025-07-23 19:47:52,408 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\BOD_I.TTF', name='Bodoni MT', style='italic', variant='normal', weight=400, stretch='normal', size='scalable')) = 11.05
2025-07-23 19:47:52,408 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\comicz.ttf', name='Comic Sans MS', style='italic', variant='normal', weight=700, stretch='normal', size='scalable')) = 11.335
2025-07-23 19:47:52,408 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\VerdanaPro-CondRegular.ttf', name='Verdana Pro', style='normal', variant='normal', weight=400, stretch='condensed', size='scalable')) = 10.25
2025-07-23 19:47:52,408 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\GillSansLightItNova.ttf', name='Gill Sans Nova', style='italic', variant='normal', weight=300, stretch='normal', size='scalable')) = 11.145
2025-07-23 19:47:52,408 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\NotoSansTC-VF.ttf', name='Noto Sans TC', style='normal', variant='normal', weight=100, stretch='normal', size='scalable')) = 10.335
2025-07-23 19:47:52,409 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\upcll.ttf', name='LilyUPC', style='normal', variant='normal', weight=400, stretch='normal', size='scalable')) = 10.05
2025-07-23 19:47:52,409 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\REFSAN.TTF', name='MS Reference Sans Serif', style='normal', variant='normal', weight=400, stretch='normal', size='scalable')) = 10.05
2025-07-23 19:47:52,409 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\consolai.ttf', name='Consolas', style='italic', variant='normal', weight=400, stretch='normal', size='scalable')) = 11.05
2025-07-23 19:47:52,409 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\verdana.ttf', name='Verdana', style='normal', variant='normal', weight=400, stretch='normal', size='scalable')) = 3.6863636363636365
2025-07-23 19:47:52,409 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\SCHLBKB.TTF', name='Century Schoolbook', style='normal', variant='normal', weight=700, stretch='normal', size='scalable')) = 10.335
2025-07-23 19:47:52,410 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\SNAP____.TTF', name='Snap ITC', style='normal', variant='normal', weight=400, stretch='normal', size='scalable')) = 10.05
2025-07-23 19:47:52,410 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\upckbi.ttf', name='KodchiangUPC', style='italic', variant='normal', weight=700, stretch='normal', size='scalable')) = 11.335
2025-07-23 19:47:52,410 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\latha.ttf', name='Latha', style='normal', variant='normal', weight=400, stretch='normal', size='scalable')) = 10.05
2025-07-23 19:47:52,410 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\monbaiti.ttf', name='Mongolian Baiti', style='normal', variant='normal', weight=400, stretch='normal', size='scalable')) = 10.05
2025-07-23 19:47:52,410 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\JOKERMAN.TTF', name='Jokerman', style='normal', variant='normal', weight=400, stretch='normal', size='scalable')) = 10.05
2025-07-23 19:47:52,411 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\mangalb.ttf', name='Mangal', style='normal', variant='normal', weight=700, stretch='normal', size='scalable')) = 10.335
2025-07-23 19:47:52,411 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\GeorgiaPro-CondItalic.ttf', name='Georgia Pro', style='italic', variant='normal', weight=400, stretch='condensed', size='scalable')) = 11.25
2025-07-23 19:47:52,411 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\arialni.ttf', name='Arial', style='italic', variant='normal', weight=400, stretch='condensed', size='scalable')) = 7.613636363636363
2025-07-23 19:47:52,412 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\arialnb.ttf', name='Arial', style='normal', variant='normal', weight=700, stretch='condensed', size='scalable')) = 6.8986363636363635
2025-07-23 19:47:52,412 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\ANTQUABI.TTF', name='Book Antiqua', style='italic', variant='normal', weight=700, stretch='normal', size='scalable')) = 11.335
2025-07-23 19:47:52,412 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\FTLTLT.TTF', name='Footlight MT Light', style='normal', variant='normal', weight=300, stretch='normal', size='scalable')) = 10.145
2025-07-23 19:47:52,412 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\Shonarb.ttf', name='Shonar Bangla', style='normal', variant='normal', weight=700, stretch='normal', size='scalable')) = 10.335
2025-07-23 19:47:52,412 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\msyhbd.ttc', name='Microsoft YaHei', style='normal', variant='normal', weight=700, stretch='normal', size='scalable')) = 10.335
2025-07-23 19:47:52,412 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\GIL_____.TTF', name='Gill Sans MT', style='normal', variant='normal', weight=400, stretch='normal', size='scalable')) = 10.05
2025-07-23 19:47:52,412 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\FORTE.TTF', name='Forte', style='normal', variant='normal', weight=400, stretch='normal', size='scalable')) = 10.05
2025-07-23 19:47:52,413 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\cambriai.ttf', name='Cambria', style='italic', variant='normal', weight=400, stretch='normal', size='scalable')) = 11.05
2025-07-23 19:47:52,413 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\arial.ttf', name='Arial', style='normal', variant='normal', weight=400, stretch='normal', size='scalable')) = 6.413636363636363
2025-07-23 19:47:52,414 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\GillSansItNova.ttf', name='Gill Sans Nova', style='italic', variant='normal', weight=500, stretch='normal', size='scalable')) = 11.145
2025-07-23 19:47:52,414 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\kokilai.ttf', name='Kokila', style='italic', variant='normal', weight=400, stretch='normal', size='scalable')) = 11.05
2025-07-23 19:47:52,414 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\tunga.ttf', name='Tunga', style='normal', variant='normal', weight=400, stretch='normal', size='scalable')) = 10.05
2025-07-23 19:47:52,414 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\nyala.ttf', name='Nyala', style='normal', variant='normal', weight=400, stretch='normal', size='scalable')) = 10.05
2025-07-23 19:47:52,415 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\AGENCYR.TTF', name='Agency FB', style='normal', variant='normal', weight=400, stretch='normal', size='scalable')) = 10.05
2025-07-23 19:47:52,415 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\framdit.ttf', name='Franklin Gothic Medium', style='italic', variant='normal', weight=400, stretch='normal', size='scalable')) = 11.05
2025-07-23 19:47:52,415 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\bahnschrift.ttf', name='Bahnschrift', style='normal', variant='normal', weight=400, stretch='normal', size='scalable')) = 10.05
2025-07-23 19:47:52,415 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\GeorgiaPro-CondBlack.ttf', name='Georgia Pro', style='normal', variant='normal', weight=900, stretch='condensed', size='scalable')) = 10.725
2025-07-23 19:47:52,415 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\GillSansCondNova.ttf', name='Gill Sans Nova', style='normal', variant='normal', weight=500, stretch='condensed', size='scalable')) = 10.344999999999999
2025-07-23 19:47:52,415 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\GillSansNova.ttf', name='Gill Sans Nova', style='normal', variant='normal', weight=500, stretch='normal', size='scalable')) = 10.145
2025-07-23 19:47:52,416 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\msyhl.ttc', name='Microsoft YaHei', style='normal', variant='normal', weight=290, stretch='normal', size='scalable')) = 10.1545
2025-07-23 19:47:52,416 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\RockwellNova-Italic.ttf', name='Rockwell Nova', style='italic', variant='normal', weight=400, stretch='normal', size='scalable')) = 11.05
2025-07-23 19:47:52,416 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\BOD_BI.TTF', name='Bodoni MT', style='italic', variant='normal', weight=700, stretch='normal', size='scalable')) = 11.335
2025-07-23 19:47:52,416 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\COOPBL.TTF', name='Cooper Black', style='normal', variant='normal', weight=400, stretch='normal', size='scalable')) = 10.05
2025-07-23 19:47:52,416 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\CALIFI.TTF', name='Californian FB', style='italic', variant='normal', weight=400, stretch='normal', size='scalable')) = 11.05
2025-07-23 19:47:52,416 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\upclbi.ttf', name='LilyUPC', style='italic', variant='normal', weight=700, stretch='normal', size='scalable')) = 11.335
2025-07-23 19:47:52,417 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\RockwellNovaCond.ttf', name='Rockwell Nova', style='normal', variant='normal', weight=400, stretch='condensed', size='scalable')) = 10.25
2025-07-23 19:47:52,417 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\VerdanaPro-SemiBoldItalic.ttf', name='Verdana Pro', style='italic', variant='normal', weight=600, stretch='normal', size='scalable')) = 11.24
2025-07-23 19:47:52,417 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\GOUDOSI.TTF', name='Goudy Old Style', style='italic', variant='normal', weight=400, stretch='normal', size='scalable')) = 11.05
2025-07-23 19:47:52,417 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\andlso.ttf', name='Andalus', style='normal', variant='normal', weight=400, stretch='normal', size='scalable')) = 10.05
2025-07-23 19:47:52,417 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\SimsunExtG.ttf', name='SimSun-ExtG', style='normal', variant='normal', weight=400, stretch='normal', size='scalable')) = 10.05
2025-07-23 19:47:52,417 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\GILB____.TTF', name='Gill Sans MT', style='normal', variant='normal', weight=700, stretch='normal', size='scalable')) = 10.335
2025-07-23 19:47:52,418 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\kalingab.ttf', name='Kalinga', style='normal', variant='normal', weight=700, stretch='normal', size='scalable')) = 10.335
2025-07-23 19:47:52,418 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\Inkfree.ttf', name='Ink Free', style='normal', variant='normal', weight=400, stretch='normal', size='scalable')) = 10.05
2025-07-23 19:47:52,418 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\BSSYM7.TTF', name='Bookshelf Symbol 7', style='normal', variant='normal', weight=400, stretch='normal', size='scalable')) = 10.05
2025-07-23 19:47:52,418 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\INFROMAN.TTF', name='Informal Roman', style='normal', variant='normal', weight=400, stretch='normal', size='scalable')) = 10.05
2025-07-23 19:47:52,418 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\GillSansCondExtraItNova.ttf', name='Gill Sans Nova', style='italic', variant='normal', weight=800, stretch='condensed', size='scalable')) = 11.629999999999999
2025-07-23 19:47:52,419 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\BOD_CR.TTF', name='Bodoni MT', style='normal', variant='normal', weight=400, stretch='condensed', size='scalable')) = 10.25
2025-07-23 19:47:52,419 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\ITCBLKAD.TTF', name='Blackadder ITC', style='normal', variant='normal', weight=400, stretch='normal', size='scalable')) = 10.05
2025-07-23 19:47:52,419 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\ahronbd.ttf', name='Aharoni', style='normal', variant='normal', weight=700, stretch='normal', size='scalable')) = 10.335
2025-07-23 19:47:52,419 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\UDDigiKyokashoN-R.ttc', name='UD Digi Kyokasho N-R', style='normal', variant='normal', weight=400, stretch='normal', size='scalable')) = 10.05
2025-07-23 19:47:52,419 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\vijaya.ttf', name='Vijaya', style='normal', variant='normal', weight=400, stretch='normal', size='scalable')) = 10.05
2025-07-23 19:47:52,419 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\LaoUI.ttf', name='Lao UI', style='normal', variant='normal', weight=400, stretch='normal', size='scalable')) = 10.05
2025-07-23 19:47:52,420 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\GillSansUltraBoNova.ttf', name='Gill Sans Nova', style='normal', variant='normal', weight=900, stretch='normal', size='scalable')) = 10.525
2025-07-23 19:47:52,420 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\vijayab.ttf', name='Vijaya', style='normal', variant='normal', weight=700, stretch='normal', size='scalable')) = 10.335
2025-07-23 19:47:52,420 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\PRISTINA.TTF', name='Pristina', style='normal', variant='normal', weight=400, stretch='normal', size='scalable')) = 10.05
2025-07-23 19:47:52,420 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\msjh.ttc', name='Microsoft JhengHei', style='normal', variant='normal', weight=400, stretch='normal', size='scalable')) = 10.05
2025-07-23 19:47:52,420 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\mriamc.ttf', name='Miriam Fixed', style='normal', variant='normal', weight=400, stretch='normal', size='scalable')) = 10.05
2025-07-23 19:47:52,420 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\segoeuib.ttf', name='Segoe UI', style='normal', variant='normal', weight=700, stretch='normal', size='scalable')) = 10.335
2025-07-23 19:47:52,420 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\frank.ttf', name='FrankRuehl', style='normal', variant='normal', weight=400, stretch='normal', size='scalable')) = 10.05
2025-07-23 19:47:52,421 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\Dengb.ttf', name='DengXian', style='normal', variant='normal', weight=700, stretch='normal', size='scalable')) = 10.335
2025-07-23 19:47:52,422 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\LSANSDI.TTF', name='Lucida Sans', style='italic', variant='normal', weight=600, stretch='normal', size='scalable')) = 11.24
2025-07-23 19:47:52,422 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\CALISTBI.TTF', name='Calisto MT', style='italic', variant='normal', weight=700, stretch='normal', size='scalable')) = 11.335
2025-07-23 19:47:52,422 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\CALISTB.TTF', name='Calisto MT', style='normal', variant='normal', weight=700, stretch='normal', size='scalable')) = 10.335
2025-07-23 19:47:52,422 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\rod.ttf', name='Rod', style='normal', variant='normal', weight=400, stretch='normal', size='scalable')) = 10.05
2025-07-23 19:47:52,423 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\BROADW.TTF', name='Broadway', style='normal', variant='normal', weight=400, stretch='normal', size='scalable')) = 10.05
2025-07-23 19:47:52,423 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\lvnmbd.ttf', name='Levenim MT', style='normal', variant='normal', weight=700, stretch='normal', size='scalable')) = 10.335
2025-07-23 19:47:52,423 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\GILSANUB.TTF', name='Gill Sans Ultra Bold', style='normal', variant='normal', weight=400, stretch='normal', size='scalable')) = 10.05
2025-07-23 19:47:52,423 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\VerdanaPro-SemiBold.ttf', name='Verdana Pro', style='normal', variant='normal', weight=600, stretch='normal', size='scalable')) = 10.24
2025-07-23 19:47:52,423 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\GOTHICBI.TTF', name='Century Gothic', style='italic', variant='normal', weight=700, stretch='normal', size='scalable')) = 11.335
2025-07-23 19:47:52,423 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\simpo.ttf', name='Simplified Arabic', style='normal', variant='normal', weight=400, stretch='normal', size='scalable')) = 10.05
2025-07-23 19:47:52,424 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\RAVIE.TTF', name='Ravie', style='normal', variant='normal', weight=400, stretch='normal', size='scalable')) = 10.05
2025-07-23 19:47:52,424 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\majalla.ttf', name='Sakkal Majalla', style='normal', variant='normal', weight=400, stretch='normal', size='scalable')) = 10.05
2025-07-23 19:47:52,424 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\NIAGSOL.TTF', name='Niagara Solid', style='normal', variant='normal', weight=400, stretch='normal', size='scalable')) = 10.05
2025-07-23 19:47:52,424 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\CHILLER.TTF', name='Chiller', style='normal', variant='normal', weight=400, stretch='normal', size='scalable')) = 10.05
2025-07-23 19:47:52,424 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\consola.ttf', name='Consolas', style='normal', variant='normal', weight=400, stretch='normal', size='scalable')) = 10.05
2025-07-23 19:47:52,424 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\LBRITED.TTF', name='Lucida Bright', style='normal', variant='normal', weight=600, stretch='normal', size='scalable')) = 10.24
2025-07-23 19:47:52,425 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\BRLNSR.TTF', name='Berlin Sans FB', style='normal', variant='normal', weight=400, stretch='normal', size='scalable')) = 10.05
2025-07-23 19:47:52,425 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\calibrili.ttf', name='Calibri', style='italic', variant='normal', weight=300, stretch='normal', size='scalable')) = 11.145
2025-07-23 19:47:52,425 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\COPRGTB.TTF', name='Copperplate Gothic Bold', style='normal', variant='normal', weight=400, stretch='normal', size='scalable')) = 10.05
2025-07-23 19:47:52,425 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\simsun.ttc', name='SimSun', style='normal', variant='normal', weight=400, stretch='normal', size='scalable')) = 10.05
2025-07-23 19:47:52,425 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\MTCORSVA.TTF', name='Monotype Corsiva', style='normal', variant='normal', weight=400, stretch='normal', size='scalable')) = 10.05
2025-07-23 19:47:52,425 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\RockwellNovaCond-Light.ttf', name='Rockwell Nova', style='normal', variant='normal', weight=300, stretch='condensed', size='scalable')) = 10.344999999999999
2025-07-23 19:47:52,426 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\Candaral.ttf', name='Candara', style='normal', variant='normal', weight=400, stretch='normal', size='scalable')) = 10.05
2025-07-23 19:47:52,426 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\Candara.ttf', name='Candara', style='normal', variant='normal', weight=400, stretch='normal', size='scalable')) = 10.05
2025-07-23 19:47:52,426 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\seguibli.ttf', name='Segoe UI', style='italic', variant='normal', weight=900, stretch='normal', size='scalable')) = 11.525
2025-07-23 19:47:52,426 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\cambriaz.ttf', name='Cambria', style='italic', variant='normal', weight=700, stretch='normal', size='scalable')) = 11.335
2025-07-23 19:47:52,426 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\upcjl.ttf', name='JasmineUPC', style='normal', variant='normal', weight=400, stretch='normal', size='scalable')) = 10.05
2025-07-23 19:47:52,426 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\BOOKOSB.TTF', name='Bookman Old Style', style='normal', variant='normal', weight=600, stretch='normal', size='scalable')) = 10.24
2025-07-23 19:47:52,426 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\JUICE___.TTF', name='Juice ITC', style='normal', variant='normal', weight=400, stretch='normal', size='scalable')) = 10.05
2025-07-23 19:47:52,427 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\NotoSansHK-VF.ttf', name='Noto Sans HK', style='normal', variant='normal', weight=100, stretch='normal', size='scalable')) = 10.335
2025-07-23 19:47:52,427 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\Shonar.ttf', name='Shonar Bangla', style='normal', variant='normal', weight=400, stretch='normal', size='scalable')) = 10.05
2025-07-23 19:47:52,427 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\nrkis.ttf', name='Narkisim', style='normal', variant='normal', weight=400, stretch='normal', size='scalable')) = 10.05
2025-07-23 19:47:52,427 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\mingliub.ttc', name='MingLiU-ExtB', style='normal', variant='normal', weight=400, stretch='normal', size='scalable')) = 10.05
2025-07-23 19:47:52,427 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\GLSNECB.TTF', name='Gill Sans MT Ext Condensed Bold', style='normal', variant='normal', weight=400, stretch='condensed', size='scalable')) = 10.25
2025-07-23 19:47:52,427 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\BOOKOSI.TTF', name='Bookman Old Style', style='italic', variant='normal', weight=300, stretch='normal', size='scalable')) = 11.145
2025-07-23 19:47:52,428 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\HARLOWSI.TTF', name='Harlow Solid Italic', style='italic', variant='normal', weight=400, stretch='normal', size='scalable')) = 11.05
2025-07-23 19:47:52,428 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\VerdanaPro-CondBoldItalic.ttf', name='Verdana Pro', style='italic', variant='normal', weight=700, stretch='condensed', size='scalable')) = 11.535
2025-07-23 19:47:52,428 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\GillSansBoItNova.ttf', name='Gill Sans Nova', style='italic', variant='normal', weight=700, stretch='normal', size='scalable')) = 11.335
2025-07-23 19:47:52,429 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\l_10646.ttf', name='Lucida Sans Unicode', style='normal', variant='normal', weight=400, stretch='normal', size='scalable')) = 10.05
2025-07-23 19:47:52,429 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\gishabd.ttf', name='Gisha', style='normal', variant='normal', weight=700, stretch='normal', size='scalable')) = 10.335
2025-07-23 19:47:52,429 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\BOD_B.TTF', name='Bodoni MT', style='normal', variant='normal', weight=700, stretch='normal', size='scalable')) = 10.335
2025-07-23 19:47:52,429 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\utsaah.ttf', name='Utsaah', style='normal', variant='normal', weight=400, stretch='normal', size='scalable')) = 10.05
2025-07-23 19:47:52,429 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\utsaahb.ttf', name='Utsaah', style='normal', variant='normal', weight=700, stretch='normal', size='scalable')) = 10.335
2025-07-23 19:47:52,429 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\TCCEB.TTF', name='Tw Cen MT Condensed Extra Bold', style='normal', variant='normal', weight=400, stretch='condensed', size='scalable')) = 10.25
2025-07-23 19:47:52,430 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\GeorgiaPro-CondLightItalic.ttf', name='Georgia Pro', style='italic', variant='normal', weight=300, stretch='condensed', size='scalable')) = 11.344999999999999
2025-07-23 19:47:52,430 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\verdanai.ttf', name='Verdana', style='italic', variant='normal', weight=400, stretch='normal', size='scalable')) = 4.6863636363636365
2025-07-23 19:47:52,430 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\ntailu.ttf', name='Microsoft New Tai Lue', style='normal', variant='normal', weight=400, stretch='normal', size='scalable')) = 10.05
2025-07-23 19:47:52,430 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\SitkaZ.ttc', name='Sitka Small', style='italic', variant='normal', weight=700, stretch='normal', size='scalable')) = 11.335
2025-07-23 19:47:52,430 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\LATINWD.TTF', name='Wide Latin', style='normal', variant='normal', weight=400, stretch='expanded', size='scalable')) = 10.25
2025-07-23 19:47:52,430 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\segoeprb.ttf', name='Segoe Print', style='normal', variant='normal', weight=700, stretch='normal', size='scalable')) = 10.335
2025-07-23 19:47:52,431 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\segoeui.ttf', name='Segoe UI', style='normal', variant='normal', weight=400, stretch='normal', size='scalable')) = 10.05
2025-07-23 19:47:52,431 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\COLONNA.TTF', name='Colonna MT', style='normal', variant='normal', weight=400, stretch='normal', size='scalable')) = 10.05
2025-07-23 19:47:52,431 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\Gabriola.ttf', name='Gabriola', style='normal', variant='normal', weight=400, stretch='normal', size='scalable')) = 10.05
2025-07-23 19:47:52,431 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\UDDigiKyokashoN-B.ttc', name='UD Digi Kyokasho N-B', style='normal', variant='normal', weight=700, stretch='normal', size='scalable')) = 10.335
2025-07-23 19:47:52,431 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\phagspa.ttf', name='Microsoft PhagsPa', style='normal', variant='normal', weight=400, stretch='normal', size='scalable')) = 10.05
2025-07-23 19:47:52,431 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\FRSCRIPT.TTF', name='French Script MT', style='normal', variant='normal', weight=400, stretch='normal', size='scalable')) = 10.05
2025-07-23 19:47:52,432 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\NotoSansJP-VF.ttf', name='Noto Sans JP', style='normal', variant='normal', weight=100, stretch='normal', size='scalable')) = 10.335
2025-07-23 19:47:52,432 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\GeorgiaPro-CondSemiBoldItalic.ttf', name='Georgia Pro', style='italic', variant='normal', weight=600, stretch='condensed', size='scalable')) = 11.44
2025-07-23 19:47:52,432 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\ArialNova-Light.ttf', name='Arial Nova', style='normal', variant='normal', weight=300, stretch='normal', size='scalable')) = 10.145
2025-07-23 19:47:52,432 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\davidbd.ttf', name='David', style='normal', variant='normal', weight=700, stretch='normal', size='scalable')) = 10.335
2025-07-23 19:47:52,432 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\GeorgiaPro-CondLight.ttf', name='Georgia Pro', style='normal', variant='normal', weight=300, stretch='condensed', size='scalable')) = 10.344999999999999
2025-07-23 19:47:52,432 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\GOTHICI.TTF', name='Century Gothic', style='italic', variant='normal', weight=400, stretch='normal', size='scalable')) = 11.05
2025-07-23 19:47:52,433 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\VLADIMIR.TTF', name='Vladimir Script', style='normal', variant='normal', weight=400, stretch='normal', size='scalable')) = 10.05
2025-07-23 19:47:52,433 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\upcjbi.ttf', name='JasmineUPC', style='italic', variant='normal', weight=700, stretch='normal', size='scalable')) = 11.335
2025-07-23 19:47:52,433 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\HPSimplified_It.ttf', name='HP Simplified', style='italic', variant='normal', weight=400, stretch='normal', size='scalable')) = 11.05
2025-07-23 19:47:52,433 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\GeorgiaPro-Black.ttf', name='Georgia Pro', style='normal', variant='normal', weight=900, stretch='normal', size='scalable')) = 10.525
2025-07-23 19:47:52,433 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\ERASMD.TTF', name='Eras Medium ITC', style='normal', variant='normal', weight=400, stretch='normal', size='scalable')) = 10.05
2025-07-23 19:47:52,433 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\VerdanaPro-LightItalic.ttf', name='Verdana Pro', style='italic', variant='normal', weight=300, stretch='normal', size='scalable')) = 11.145
2025-07-23 19:47:52,434 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\yumindb.ttf', name='Yu Mincho', style='normal', variant='normal', weight=600, stretch='normal', size='scalable')) = 10.24
2025-07-23 19:47:52,434 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\BAUHS93.TTF', name='Bauhaus 93', style='normal', variant='normal', weight=400, stretch='normal', size='scalable')) = 10.05
2025-07-23 19:47:52,434 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\mmrtextb.ttf', name='Myanmar Text', style='normal', variant='normal', weight=700, stretch='normal', size='scalable')) = 10.335
2025-07-23 19:47:52,434 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\OCRAEXT.TTF', name='OCR A Extended', style='normal', variant='normal', weight=400, stretch='expanded', size='scalable')) = 10.25
2025-07-23 19:47:52,434 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\kalinga.ttf', name='Kalinga', style='normal', variant='normal', weight=400, stretch='normal', size='scalable')) = 10.05
2025-07-23 19:47:52,434 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\MISTRAL.TTF', name='Mistral', style='normal', variant='normal', weight=400, stretch='normal', size='scalable')) = 10.05
2025-07-23 19:47:52,434 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\simfang.ttf', name='FangSong', style='normal', variant='normal', weight=400, stretch='normal', size='scalable')) = 10.05
2025-07-23 19:47:52,436 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\VerdanaPro-Bold.ttf', name='Verdana Pro', style='normal', variant='normal', weight=700, stretch='normal', size='scalable')) = 10.335
2025-07-23 19:47:52,436 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\upcki.ttf', name='KodchiangUPC', style='italic', variant='normal', weight=400, stretch='normal', size='scalable')) = 11.05
2025-07-23 19:47:52,436 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\ntailub.ttf', name='Microsoft New Tai Lue', style='normal', variant='normal', weight=700, stretch='normal', size='scalable')) = 10.335
2025-07-23 19:47:52,436 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\ALGER.TTF', name='Algerian', style='normal', variant='normal', weight=400, stretch='normal', size='scalable')) = 10.05
2025-07-23 19:47:52,436 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\RockwellNova-LightItalic.ttf', name='Rockwell Nova', style='italic', variant='normal', weight=300, stretch='normal', size='scalable')) = 11.145
2025-07-23 19:47:52,437 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\upcdi.ttf', name='DilleniaUPC', style='italic', variant='normal', weight=400, stretch='normal', size='scalable')) = 11.05
2025-07-23 19:47:52,437 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\GillSansCondExtraNova.ttf', name='Gill Sans Nova', style='normal', variant='normal', weight=800, stretch='condensed', size='scalable')) = 10.629999999999999
2025-07-23 19:47:52,438 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\ARIALUNI.TTF', name='Arial Unicode MS', style='normal', variant='normal', weight=400, stretch='normal', size='scalable')) = 10.05
2025-07-23 19:47:52,438 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\palai.ttf', name='Palatino Linotype', style='italic', variant='normal', weight=400, stretch='normal', size='scalable')) = 11.05
2025-07-23 19:47:52,438 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\ROCC____.TTF', name='Rockwell Condensed', style='normal', variant='normal', weight=400, stretch='condensed', size='scalable')) = 10.25
2025-07-23 19:47:52,438 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\GeorgiaPro-CondRegular.ttf', name='Georgia Pro', style='normal', variant='normal', weight=400, stretch='condensed', size='scalable')) = 10.25
2025-07-23 19:47:52,439 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\LFAX.TTF', name='Lucida Fax', style='normal', variant='normal', weight=400, stretch='normal', size='scalable')) = 10.05
2025-07-23 19:47:52,439 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\TCCB____.TTF', name='Tw Cen MT Condensed', style='normal', variant='normal', weight=700, stretch='condensed', size='scalable')) = 10.535
2025-07-23 19:47:52,439 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\tungab.ttf', name='Tunga', style='normal', variant='normal', weight=700, stretch='normal', size='scalable')) = 10.335
2025-07-23 19:47:52,439 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\FRAMDCN.TTF', name='Franklin Gothic Medium Cond', style='normal', variant='normal', weight=400, stretch='condensed', size='scalable')) = 10.25
2025-07-23 19:47:52,439 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\kartika.ttf', name='Kartika', style='normal', variant='normal', weight=400, stretch='normal', size='scalable')) = 10.05
2025-07-23 19:47:52,440 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\PAPYRUS.TTF', name='Papyrus', style='normal', variant='normal', weight=400, stretch='normal', size='scalable')) = 10.05
2025-07-23 19:47:52,440 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\LFAXI.TTF', name='Lucida Fax', style='italic', variant='normal', weight=400, stretch='normal', size='scalable')) = 11.05
2025-07-23 19:47:52,440 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\BERNHC.TTF', name='Bernard MT Condensed', style='normal', variant='normal', weight=400, stretch='condensed', size='scalable')) = 10.25
2025-07-23 19:47:52,440 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\GeorgiaPro-SemiBold.ttf', name='Georgia Pro', style='normal', variant='normal', weight=600, stretch='normal', size='scalable')) = 10.24
2025-07-23 19:47:52,440 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\simsunb.ttf', name='SimSun-ExtB', style='normal', variant='normal', weight=400, stretch='normal', size='scalable')) = 10.05
2025-07-23 19:47:52,440 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\cambriab.ttf', name='Cambria', style='normal', variant='normal', weight=700, stretch='normal', size='scalable')) = 10.335
2025-07-23 19:47:52,441 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\ArialNovaCond-Light.ttf', name='Arial Nova', style='normal', variant='normal', weight=300, stretch='condensed', size='scalable')) = 10.344999999999999
2025-07-23 19:47:52,441 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\ArialNova-BoldItalic.ttf', name='Arial Nova', style='italic', variant='normal', weight=700, stretch='normal', size='scalable')) = 11.335
2025-07-23 19:47:52,441 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\yuminl.ttf', name='Yu Mincho', style='normal', variant='normal', weight=300, stretch='normal', size='scalable')) = 10.145
2025-07-23 19:47:52,441 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\BRLNSDB.TTF', name='Berlin Sans FB Demi', style='normal', variant='normal', weight=700, stretch='normal', size='scalable')) = 10.335
2025-07-23 19:47:52,441 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\taile.ttf', name='Microsoft Tai Le', style='normal', variant='normal', weight=400, stretch='normal', size='scalable')) = 10.05
2025-07-23 19:47:52,441 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\courbd.ttf', name='Courier New', style='normal', variant='normal', weight=700, stretch='normal', size='scalable')) = 10.335
2025-07-23 19:47:52,442 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\gadugib.ttf', name='Gadugi', style='normal', variant='normal', weight=700, stretch='normal', size='scalable')) = 10.335
2025-07-23 19:47:52,442 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\mvboli.ttf', name='MV Boli', style='normal', variant='normal', weight=400, stretch='normal', size='scalable')) = 10.05
2025-07-23 19:47:52,442 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\upcfi.ttf', name='FreesiaUPC', style='italic', variant='normal', weight=400, stretch='normal', size='scalable')) = 11.05
2025-07-23 19:47:52,442 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\VerdanaPro-CondBlackItalic.ttf', name='Verdana Pro', style='italic', variant='normal', weight=900, stretch='condensed', size='scalable')) = 11.725
2025-07-23 19:47:52,442 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\consolaz.ttf', name='Consolas', style='italic', variant='normal', weight=700, stretch='normal', size='scalable')) = 11.335
2025-07-23 19:47:52,442 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\GeorgiaPro-Italic.ttf', name='Georgia Pro', style='italic', variant='normal', weight=400, stretch='normal', size='scalable')) = 11.05
2025-07-23 19:47:52,442 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\PALSCRI.TTF', name='Palace Script MT', style='normal', variant='normal', weight=400, stretch='normal', size='scalable')) = 10.05
2025-07-23 19:47:52,443 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\PERTIBD.TTF', name='Perpetua Titling MT', style='normal', variant='normal', weight=700, stretch='normal', size='scalable')) = 10.335
2025-07-23 19:47:52,443 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\segoepr.ttf', name='Segoe Print', style='normal', variant='normal', weight=400, stretch='normal', size='scalable')) = 10.05
2025-07-23 19:47:52,443 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\ROCK.TTF', name='Rockwell', style='normal', variant='normal', weight=400, stretch='normal', size='scalable')) = 10.05
2025-07-23 19:47:52,443 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\GILI____.TTF', name='Gill Sans MT', style='italic', variant='normal', weight=400, stretch='normal', size='scalable')) = 11.05
2025-07-23 19:47:52,443 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\upcib.ttf', name='IrisUPC', style='normal', variant='normal', weight=700, stretch='normal', size='scalable')) = 10.335
2025-07-23 19:47:52,444 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\GillSansCondBoItNova.ttf', name='Gill Sans Nova', style='italic', variant='normal', weight=700, stretch='condensed', size='scalable')) = 11.535
2025-07-23 19:47:52,444 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\upcli.ttf', name='LilyUPC', style='italic', variant='normal', weight=400, stretch='normal', size='scalable')) = 11.05
2025-07-23 19:47:52,444 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\NirmalaB.ttf', name='Nirmala UI', style='normal', variant='normal', weight=700, stretch='normal', size='scalable')) = 10.335
2025-07-23 19:47:52,444 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\PARCHM.TTF', name='Parchment', style='normal', variant='normal', weight=400, stretch='normal', size='scalable')) = 10.05
2025-07-23 19:47:52,444 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\FRADMIT.TTF', name='Franklin Gothic Demi', style='italic', variant='normal', weight=400, stretch='normal', size='scalable')) = 11.05
2025-07-23 19:47:52,444 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\arabtype.ttf', name='Arabic Typesetting', style='normal', variant='normal', weight=400, stretch='normal', size='scalable')) = 10.05
2025-07-23 19:47:52,444 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\TEMPSITC.TTF', name='Tempus Sans ITC', style='normal', variant='normal', weight=400, stretch='normal', size='scalable')) = 10.05
2025-07-23 19:47:52,445 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\cordia.ttc', name='Cordia New', style='normal', variant='normal', weight=400, stretch='normal', size='scalable')) = 10.05
2025-07-23 19:47:52,445 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\ITCEDSCR.TTF', name='Edwardian Script ITC', style='normal', variant='normal', weight=400, stretch='normal', size='scalable')) = 10.05
2025-07-23 19:47:52,445 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\MAGNETOB.TTF', name='Magneto', style='normal', variant='normal', weight=700, stretch='normal', size='scalable')) = 10.335
2025-07-23 19:47:52,446 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\seguibl.ttf', name='Segoe UI', style='normal', variant='normal', weight=900, stretch='normal', size='scalable')) = 10.525
2025-07-23 19:47:52,446 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\YuGothB.ttc', name='Yu Gothic', style='normal', variant='normal', weight=700, stretch='normal', size='scalable')) = 10.335
2025-07-23 19:47:52,446 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\VerdanaPro-Light.ttf', name='Verdana Pro', style='normal', variant='normal', weight=300, stretch='normal', size='scalable')) = 10.145
2025-07-23 19:47:52,446 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\NotoSansSC-VF.ttf', name='Noto Sans SC', style='normal', variant='normal', weight=100, stretch='normal', size='scalable')) = 10.335
2025-07-23 19:47:52,446 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\micross.ttf', name='Microsoft Sans Serif', style='normal', variant='normal', weight=400, stretch='normal', size='scalable')) = 10.05
2025-07-23 19:47:52,446 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\FRADMCN.TTF', name='Franklin Gothic Demi Cond', style='normal', variant='normal', weight=400, stretch='condensed', size='scalable')) = 10.25
2025-07-23 19:47:52,447 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\upcel.ttf', name='EucrosiaUPC', style='normal', variant='normal', weight=400, stretch='normal', size='scalable')) = 10.05
2025-07-23 19:47:52,447 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\BOD_BLAI.TTF', name='Bodoni MT', style='italic', variant='normal', weight=900, stretch='normal', size='scalable')) = 11.525
2025-07-23 19:47:52,447 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\CALIFR.TTF', name='Californian FB', style='normal', variant='normal', weight=400, stretch='normal', size='scalable')) = 10.05
2025-07-23 19:47:52,447 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\VIVALDII.TTF', name='Vivaldi', style='italic', variant='normal', weight=400, stretch='normal', size='scalable')) = 11.05
2025-07-23 19:47:52,447 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\GARABD.TTF', name='Garamond', style='normal', variant='normal', weight=700, stretch='normal', size='scalable')) = 10.335
2025-07-23 19:47:52,447 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\upckb.ttf', name='KodchiangUPC', style='normal', variant='normal', weight=700, stretch='normal', size='scalable')) = 10.335
2025-07-23 19:47:52,448 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\FRABK.TTF', name='Franklin Gothic Book', style='normal', variant='normal', weight=400, stretch='normal', size='scalable')) = 10.05
2025-07-23 19:47:52,448 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\georgiai.ttf', name='Georgia', style='italic', variant='normal', weight=400, stretch='normal', size='scalable')) = 11.05
2025-07-23 19:47:52,448 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\COPRGTL.TTF', name='Copperplate Gothic Light', style='normal', variant='normal', weight=400, stretch='normal', size='scalable')) = 10.05
2025-07-23 19:47:52,448 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\VerdanaPro-BlackItalic.ttf', name='Verdana Pro', style='italic', variant='normal', weight=900, stretch='normal', size='scalable')) = 11.525
2025-07-23 19:47:52,448 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\upcji.ttf', name='JasmineUPC', style='italic', variant='normal', weight=400, stretch='normal', size='scalable')) = 11.05
2025-07-23 19:47:52,448 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\GillSansCondItNova.ttf', name='Gill Sans Nova', style='italic', variant='normal', weight=500, stretch='condensed', size='scalable')) = 11.344999999999999
2025-07-23 19:47:52,449 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\GeorgiaPro-Bold.ttf', name='Georgia Pro', style='normal', variant='normal', weight=700, stretch='normal', size='scalable')) = 10.335
2025-07-23 19:47:52,449 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\BASKVILL.TTF', name='Baskerville Old Face', style='normal', variant='normal', weight=400, stretch='normal', size='scalable')) = 10.05
2025-07-23 19:47:52,449 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\segoeuil.ttf', name='Segoe UI', style='normal', variant='normal', weight=300, stretch='normal', size='scalable')) = 10.145
2025-07-23 19:47:52,449 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\GIGI.TTF', name='Gigi', style='normal', variant='normal', weight=400, stretch='normal', size='scalable')) = 10.05
2025-07-23 19:47:52,449 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\IMPRISHA.TTF', name='Imprint MT Shadow', style='normal', variant='normal', weight=400, stretch='normal', size='scalable')) = 10.05
2025-07-23 19:47:52,450 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\GLECB.TTF', name='Gloucester MT Extra Condensed', style='normal', variant='normal', weight=400, stretch='condensed', size='scalable')) = 10.25
2025-07-23 19:47:52,450 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\KUNSTLER.TTF', name='Kunstler Script', style='normal', variant='normal', weight=400, stretch='normal', size='scalable')) = 10.05
2025-07-23 19:47:52,450 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\GillSansCondUltraBoNova.ttf', name='Gill Sans Nova', style='normal', variant='normal', weight=900, stretch='condensed', size='scalable')) = 10.725
2025-07-23 19:47:52,450 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\CENSCBK.TTF', name='Century Schoolbook', style='normal', variant='normal', weight=400, stretch='normal', size='scalable')) = 10.05
2025-07-23 19:47:52,450 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\MOD20.TTF', name='Modern No. 20', style='normal', variant='normal', weight=400, stretch='normal', size='scalable')) = 10.05
2025-07-23 19:47:52,450 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\VerdanaPro-BoldItalic.ttf', name='Verdana Pro', style='italic', variant='normal', weight=700, stretch='normal', size='scalable')) = 11.335
2025-07-23 19:47:52,451 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\LBRITEDI.TTF', name='Lucida Bright', style='italic', variant='normal', weight=600, stretch='normal', size='scalable')) = 11.24
2025-07-23 19:47:52,451 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\POORICH.TTF', name='Poor Richard', style='normal', variant='normal', weight=400, stretch='normal', size='scalable')) = 10.05
2025-07-23 19:47:52,451 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\UrdType.ttf', name='Urdu Typesetting', style='normal', variant='normal', weight=400, stretch='normal', size='scalable')) = 10.05
2025-07-23 19:47:52,451 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\aparajbi.ttf', name='Aparajita', style='italic', variant='normal', weight=700, stretch='normal', size='scalable')) = 11.335
2025-07-23 19:47:52,451 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\ROCKEB.TTF', name='Rockwell Extra Bold', style='normal', variant='normal', weight=800, stretch='normal', size='scalable')) = 10.43
2025-07-23 19:47:52,451 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\euphemia.ttf', name='Euphemia', style='normal', variant='normal', weight=400, stretch='normal', size='scalable')) = 10.05
2025-07-23 19:47:52,451 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\RockwellNovaCond-BoldItalic.ttf', name='Rockwell Nova', style='italic', variant='normal', weight=700, stretch='condensed', size='scalable')) = 11.535
2025-07-23 19:47:52,451 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\TCB_____.TTF', name='Tw Cen MT', style='normal', variant='normal', weight=700, stretch='normal', size='scalable')) = 10.335
2025-07-23 19:47:52,451 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\aparaji.ttf', name='Aparajita', style='italic', variant='normal', weight=400, stretch='normal', size='scalable')) = 11.05
2025-07-23 19:47:52,452 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\upcfl.ttf', name='FreesiaUPC', style='normal', variant='normal', weight=400, stretch='normal', size='scalable')) = 10.05
2025-07-23 19:47:52,452 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\upceb.ttf', name='EucrosiaUPC', style='normal', variant='normal', weight=700, stretch='normal', size='scalable')) = 10.335
2025-07-23 19:47:52,452 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\calibri.ttf', name='Calibri', style='normal', variant='normal', weight=400, stretch='normal', size='scalable')) = 10.05
2025-07-23 19:47:52,453 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\seguisli.ttf', name='Segoe UI', style='italic', variant='normal', weight=350, stretch='normal', size='scalable')) = 11.0975
2025-07-23 19:47:52,453 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\TCM_____.TTF', name='Tw Cen MT', style='normal', variant='normal', weight=400, stretch='normal', size='scalable')) = 10.05
2025-07-23 19:47:52,453 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\corbelz.ttf', name='Corbel', style='italic', variant='normal', weight=700, stretch='normal', size='scalable')) = 11.335
2025-07-23 19:47:52,453 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\ariblk.ttf', name='Arial', style='normal', variant='normal', weight=900, stretch='normal', size='scalable')) = 6.888636363636364
2025-07-23 19:47:52,453 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\mmrtext.ttf', name='Myanmar Text', style='normal', variant='normal', weight=400, stretch='normal', size='scalable')) = 10.05
2025-07-23 19:47:52,453 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\georgia.ttf', name='Georgia', style='normal', variant='normal', weight=400, stretch='normal', size='scalable')) = 10.05
2025-07-23 19:47:52,454 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\msjhl.ttc', name='Microsoft JhengHei', style='normal', variant='normal', weight=290, stretch='normal', size='scalable')) = 10.1545
2025-07-23 19:47:52,454 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\tradbdo.ttf', name='Traditional Arabic', style='normal', variant='normal', weight=700, stretch='normal', size='scalable')) = 10.335
2025-07-23 19:47:52,454 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\ArialNovaCond-Italic.ttf', name='Arial Nova', style='italic', variant='normal', weight=400, stretch='condensed', size='scalable')) = 11.25
2025-07-23 19:47:52,454 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\msjhbd.ttc', name='Microsoft JhengHei', style='normal', variant='normal', weight=700, stretch='normal', size='scalable')) = 10.335
2025-07-23 19:47:52,454 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\FRAHVIT.TTF', name='Franklin Gothic Heavy', style='italic', variant='normal', weight=400, stretch='normal', size='scalable')) = 11.05
2025-07-23 19:47:52,454 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\georgiab.ttf', name='Georgia', style='normal', variant='normal', weight=700, stretch='normal', size='scalable')) = 10.335
2025-07-23 19:47:52,455 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\daunpenh.ttf', name='DaunPenh', style='normal', variant='normal', weight=400, stretch='normal', size='scalable')) = 10.05
2025-07-23 19:47:52,455 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\HTOWERT.TTF', name='High Tower Text', style='normal', variant='normal', weight=400, stretch='normal', size='scalable')) = 10.05
2025-07-23 19:47:52,455 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\david.ttf', name='David', style='normal', variant='normal', weight=400, stretch='normal', size='scalable')) = 10.05
2025-07-23 19:47:52,455 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\NotoSansKR-VF.ttf', name='Noto Sans KR', style='normal', variant='normal', weight=100, stretch='normal', size='scalable')) = 10.335
2025-07-23 19:47:52,455 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\ANTQUAI.TTF', name='Book Antiqua', style='italic', variant='normal', weight=400, stretch='normal', size='scalable')) = 11.05
2025-07-23 19:47:52,455 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\holomdl2.ttf', name='HoloLens MDL2 Assets', style='normal', variant='normal', weight=400, stretch='normal', size='scalable')) = 10.05
2025-07-23 19:47:52,456 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\corbeli.ttf', name='Corbel', style='italic', variant='normal', weight=400, stretch='normal', size='scalable')) = 11.05
2025-07-23 19:47:52,456 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\aparajb.ttf', name='Aparajita', style='normal', variant='normal', weight=700, stretch='normal', size='scalable')) = 10.335
2025-07-23 19:47:52,456 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\constanb.ttf', name='Constantia', style='normal', variant='normal', weight=700, stretch='normal', size='scalable')) = 10.335
2025-07-23 19:47:52,456 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\GeorgiaPro-LightItalic.ttf', name='Georgia Pro', style='italic', variant='normal', weight=300, stretch='normal', size='scalable')) = 11.145
2025-07-23 19:47:52,456 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\plantc.ttf', name='Plantagenet Cherokee', style='normal', variant='normal', weight=400, stretch='normal', size='scalable')) = 10.05
2025-07-23 19:47:52,456 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\HARNGTON.TTF', name='Harrington', style='normal', variant='normal', weight=400, stretch='normal', size='scalable')) = 10.05
2025-07-23 19:47:52,457 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\himalaya.ttf', name='Microsoft Himalaya', style='normal', variant='normal', weight=400, stretch='normal', size='scalable')) = 10.05
2025-07-23 19:47:52,457 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\TCBI____.TTF', name='Tw Cen MT', style='italic', variant='normal', weight=700, stretch='normal', size='scalable')) = 11.335
2025-07-23 19:47:52,457 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\leelawdb.ttf', name='Leelawadee', style='normal', variant='normal', weight=700, stretch='normal', size='scalable')) = 10.335
2025-07-23 19:47:52,457 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\ariali.ttf', name='Arial', style='italic', variant='normal', weight=400, stretch='normal', size='scalable')) = 7.413636363636363
2025-07-23 19:47:52,457 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\LSANS.TTF', name='Lucida Sans', style='normal', variant='normal', weight=400, stretch='normal', size='scalable')) = 10.05
2025-07-23 19:47:52,457 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\iskpota.ttf', name='Iskoola Pota', style='normal', variant='normal', weight=400, stretch='normal', size='scalable')) = 10.05
2025-07-23 19:47:52,458 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\raavi.ttf', name='Raavi', style='normal', variant='normal', weight=400, stretch='normal', size='scalable')) = 10.05
2025-07-23 19:47:52,458 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\ArialNovaCond-LightItalic.ttf', name='Arial Nova', style='italic', variant='normal', weight=300, stretch='condensed', size='scalable')) = 11.344999999999999
2025-07-23 19:47:52,458 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\PLAYBILL.TTF', name='Playbill', style='normal', variant='normal', weight=400, stretch='normal', size='scalable')) = 10.05
2025-07-23 19:47:52,458 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\vrindab.ttf', name='Vrinda', style='normal', variant='normal', weight=700, stretch='normal', size='scalable')) = 10.335
2025-07-23 19:47:52,458 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\LSANSD.TTF', name='Lucida Sans', style='normal', variant='normal', weight=600, stretch='normal', size='scalable')) = 10.24
2025-07-23 19:47:52,458 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\seguili.ttf', name='Segoe UI', style='italic', variant='normal', weight=300, stretch='normal', size='scalable')) = 11.145
2025-07-23 19:47:52,458 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\estre.ttf', name='Estrangelo Edessa', style='normal', variant='normal', weight=400, stretch='normal', size='scalable')) = 10.05
2025-07-23 19:47:52,459 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\upckl.ttf', name='KodchiangUPC', style='normal', variant='normal', weight=400, stretch='normal', size='scalable')) = 10.05
2025-07-23 19:47:52,459 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\arialbd.ttf', name='Arial', style='normal', variant='normal', weight=700, stretch='normal', size='scalable')) = 6.698636363636363
2025-07-23 19:47:52,459 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\verdanab.ttf', name='Verdana', style='normal', variant='normal', weight=700, stretch='normal', size='scalable')) = 3.9713636363636367
2025-07-23 19:47:52,459 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\Dengl.ttf', name='DengXian', style='normal', variant='normal', weight=300, stretch='normal', size='scalable')) = 10.145
2025-07-23 19:47:52,459 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\KhmerUIb.ttf', name='Khmer UI', style='normal', variant='normal', weight=700, stretch='normal', size='scalable')) = 10.335
2025-07-23 19:47:52,459 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\ArialNovaCond-BoldItalic.ttf', name='Arial Nova', style='italic', variant='normal', weight=700, stretch='condensed', size='scalable')) = 11.535
2025-07-23 19:47:52,460 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\meiryob.ttc', name='Meiryo', style='normal', variant='normal', weight=700, stretch='normal', size='scalable')) = 10.335
2025-07-23 19:47:52,460 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\BRLNSB.TTF', name='Berlin Sans FB', style='normal', variant='normal', weight=700, stretch='normal', size='scalable')) = 10.335
2025-07-23 19:47:52,460 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\verdanaz.ttf', name='Verdana', style='italic', variant='normal', weight=700, stretch='normal', size='scalable')) = 4.971363636363637
2025-07-23 19:47:52,460 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\GOUDOS.TTF', name='Goudy Old Style', style='normal', variant='normal', weight=400, stretch='normal', size='scalable')) = 10.05
2025-07-23 19:47:52,461 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\upcdbi.ttf', name='DilleniaUPC', style='italic', variant='normal', weight=700, stretch='normal', size='scalable')) = 11.335
2025-07-23 19:47:52,461 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\SitkaB.ttc', name='Sitka Small', style='normal', variant='normal', weight=700, stretch='normal', size='scalable')) = 10.335
2025-07-23 19:47:52,461 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\ARLRDBD.TTF', name='Arial Rounded MT Bold', style='normal', variant='normal', weight=400, stretch='normal', size='scalable')) = 10.05
2025-07-23 19:47:52,461 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\kartikab.ttf', name='Kartika', style='normal', variant='normal', weight=700, stretch='normal', size='scalable')) = 10.335
2025-07-23 19:47:52,461 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\BELLB.TTF', name='Bell MT', style='normal', variant='normal', weight=700, stretch='normal', size='scalable')) = 10.335
2025-07-23 19:47:52,461 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\wingding.ttf', name='Wingdings', style='normal', variant='normal', weight=400, stretch='normal', size='scalable')) = 10.05
2025-07-23 19:47:52,462 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\mangal.ttf', name='Mangal', style='normal', variant='normal', weight=400, stretch='normal', size='scalable')) = 10.05
2025-07-23 19:47:52,463 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\WINGDNG3.TTF', name='Wingdings 3', style='normal', variant='normal', weight=400, stretch='normal', size='scalable')) = 10.05
2025-07-23 19:47:52,463 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\RockwellNovaCond-LightItalic.ttf', name='Rockwell Nova', style='italic', variant='normal', weight=300, stretch='condensed', size='scalable')) = 11.344999999999999
2025-07-23 19:47:52,463 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\HPSimplified_LtIt.ttf', name='HP Simplified', style='italic', variant='normal', weight=300, stretch='normal', size='scalable')) = 11.145
2025-07-23 19:47:52,463 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\VerdanaPro-CondLight.ttf', name='Verdana Pro', style='normal', variant='normal', weight=300, stretch='condensed', size='scalable')) = 10.344999999999999
2025-07-23 19:47:52,464 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\cambria.ttc', name='Cambria', style='normal', variant='normal', weight=400, stretch='normal', size='scalable')) = 10.05
2025-07-23 19:47:52,464 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\Candarai.ttf', name='Candara', style='italic', variant='normal', weight=400, stretch='normal', size='scalable')) = 11.05
2025-07-23 19:47:52,464 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\CALIST.TTF', name='Calisto MT', style='normal', variant='normal', weight=400, stretch='normal', size='scalable')) = 10.05
2025-07-23 19:47:52,464 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\ebrimabd.ttf', name='Ebrima', style='normal', variant='normal', weight=700, stretch='normal', size='scalable')) = 10.335
2025-07-23 19:47:52,464 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\CALIFB.TTF', name='Californian FB', style='normal', variant='normal', weight=700, stretch='normal', size='scalable')) = 10.335
2025-07-23 19:47:52,464 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\framd.ttf', name='Franklin Gothic Medium', style='normal', variant='normal', weight=400, stretch='normal', size='scalable')) = 10.05
2025-07-23 19:47:52,465 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\constani.ttf', name='Constantia', style='italic', variant='normal', weight=400, stretch='normal', size='scalable')) = 11.05
2025-07-23 19:47:52,465 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\LTYPE.TTF', name='Lucida Sans Typewriter', style='normal', variant='normal', weight=400, stretch='normal', size='scalable')) = 10.05
2025-07-23 19:47:52,465 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\LFAXD.TTF', name='Lucida Fax', style='normal', variant='normal', weight=600, stretch='normal', size='scalable')) = 10.24
2025-07-23 19:47:52,465 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\symbol.ttf', name='Symbol', style='normal', variant='normal', weight=400, stretch='normal', size='scalable')) = 10.05
2025-07-23 19:47:52,465 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\BIZ-UDGothicB.ttc', name='BIZ UDGothic', style='normal', variant='normal', weight=700, stretch='normal', size='scalable')) = 10.335
2025-07-23 19:47:52,465 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\VerdanaPro-Italic.ttf', name='Verdana Pro', style='italic', variant='normal', weight=400, stretch='normal', size='scalable')) = 11.05
2025-07-23 19:47:52,465 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\corbell.ttf', name='Corbel', style='normal', variant='normal', weight=300, stretch='normal', size='scalable')) = 10.145
2025-07-23 19:47:52,466 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\simpbdo.ttf', name='Simplified Arabic', style='normal', variant='normal', weight=700, stretch='normal', size='scalable')) = 10.335
2025-07-23 19:47:52,466 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\PER_____.TTF', name='Perpetua', style='normal', variant='normal', weight=400, stretch='normal', size='scalable')) = 10.05
2025-07-23 19:47:52,466 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\LaoUIb.ttf', name='Lao UI', style='normal', variant='normal', weight=700, stretch='normal', size='scalable')) = 10.335
2025-07-23 19:47:52,466 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\FELIXTI.TTF', name='Felix Titling', style='normal', variant='normal', weight=400, stretch='normal', size='scalable')) = 10.05
2025-07-23 19:47:52,466 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\trebuc.ttf', name='Trebuchet MS', style='normal', variant='normal', weight=400, stretch='normal', size='scalable')) = 10.05
2025-07-23 19:47:52,466 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\HTOWERTI.TTF', name='High Tower Text', style='italic', variant='normal', weight=400, stretch='normal', size='scalable')) = 11.05
2025-07-23 19:47:52,467 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\BIZ-UDMinchoM.ttc', name='BIZ UDMincho', style='normal', variant='normal', weight=500, stretch='normal', size='scalable')) = 10.145
2025-07-23 19:47:52,467 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\UrdTypeb.ttf', name='Urdu Typesetting', style='normal', variant='normal', weight=700, stretch='normal', size='scalable')) = 10.335
2025-07-23 19:47:52,467 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\VerdanaPro-CondBlack.ttf', name='Verdana Pro', style='normal', variant='normal', weight=900, stretch='condensed', size='scalable')) = 10.725
2025-07-23 19:47:52,467 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\Candarali.ttf', name='Candara', style='italic', variant='normal', weight=400, stretch='normal', size='scalable')) = 11.05
2025-07-23 19:47:52,467 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\ROCKB.TTF', name='Rockwell', style='normal', variant='normal', weight=700, stretch='normal', size='scalable')) = 10.335
2025-07-23 19:47:52,467 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\msyi.ttf', name='Microsoft Yi Baiti', style='normal', variant='normal', weight=400, stretch='normal', size='scalable')) = 10.05
2025-07-23 19:47:52,468 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\comici.ttf', name='Comic Sans MS', style='italic', variant='normal', weight=400, stretch='normal', size='scalable')) = 11.05
2025-07-23 19:47:52,468 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\cour.ttf', name='Courier New', style='normal', variant='normal', weight=400, stretch='normal', size='scalable')) = 10.05
2025-07-23 19:47:52,468 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\CENTAUR.TTF', name='Centaur', style='normal', variant='normal', weight=400, stretch='normal', size='scalable')) = 10.05
2025-07-23 19:47:52,468 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\VerdanaPro-CondBold.ttf', name='Verdana Pro', style='normal', variant='normal', weight=700, stretch='condensed', size='scalable')) = 10.535
2025-07-23 19:47:52,468 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\GOUDOSB.TTF', name='Goudy Old Style', style='normal', variant='normal', weight=700, stretch='normal', size='scalable')) = 10.335
2025-07-23 19:47:52,469 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\MATURASC.TTF', name='Matura MT Script Capitals', style='normal', variant='normal', weight=400, stretch='normal', size='scalable')) = 10.05
2025-07-23 19:47:52,469 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\FRAHV.TTF', name='Franklin Gothic Heavy', style='normal', variant='normal', weight=400, stretch='normal', size='scalable')) = 10.05
2025-07-23 19:47:52,469 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\corbelb.ttf', name='Corbel', style='normal', variant='normal', weight=700, stretch='normal', size='scalable')) = 10.335
2025-07-23 19:47:52,469 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\ArialNova-Bold.ttf', name='Arial Nova', style='normal', variant='normal', weight=700, stretch='normal', size='scalable')) = 10.335
2025-07-23 19:47:52,470 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\timesbd.ttf', name='Times New Roman', style='normal', variant='normal', weight=700, stretch='normal', size='scalable')) = 10.335
2025-07-23 19:47:52,470 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\LSANSI.TTF', name='Lucida Sans', style='italic', variant='normal', weight=400, stretch='normal', size='scalable')) = 11.05
2025-07-23 19:47:52,470 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\utsaahi.ttf', name='Utsaah', style='italic', variant='normal', weight=400, stretch='normal', size='scalable')) = 11.05
2025-07-23 19:47:52,470 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\georgiaz.ttf', name='Georgia', style='italic', variant='normal', weight=700, stretch='normal', size='scalable')) = 11.335
2025-07-23 19:47:52,470 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\GillSansCondBoNova.ttf', name='Gill Sans Nova', style='normal', variant='normal', weight=700, stretch='condensed', size='scalable')) = 10.535
2025-07-23 19:47:52,470 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\lvnm.ttf', name='Levenim MT', style='normal', variant='normal', weight=400, stretch='normal', size='scalable')) = 10.05
2025-07-23 19:47:52,471 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\FRABKIT.TTF', name='Franklin Gothic Book', style='italic', variant='normal', weight=400, stretch='normal', size='scalable')) = 11.05
2025-07-23 19:47:52,471 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\PERBI___.TTF', name='Perpetua', style='italic', variant='normal', weight=700, stretch='normal', size='scalable')) = 11.335
2025-07-23 19:47:52,471 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\Vanib.ttf', name='Vani', style='normal', variant='normal', weight=700, stretch='normal', size='scalable')) = 10.335
2025-07-23 19:47:52,471 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\segoesc.ttf', name='Segoe Script', style='normal', variant='normal', weight=400, stretch='normal', size='scalable')) = 10.05
2025-07-23 19:47:52,471 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\TCMI____.TTF', name='Tw Cen MT', style='italic', variant='normal', weight=400, stretch='normal', size='scalable')) = 11.05
2025-07-23 19:47:52,471 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\BELLI.TTF', name='Bell MT', style='italic', variant='normal', weight=400, stretch='normal', size='scalable')) = 11.05
2025-07-23 19:47:52,472 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\javatext.ttf', name='Javanese Text', style='normal', variant='normal', weight=400, stretch='normal', size='scalable')) = 10.05
2025-07-23 19:47:52,472 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\HPSimplified.ttf', name='HP Simplified', style='normal', variant='normal', weight=700, stretch='normal', size='scalable')) = 10.335
2025-07-23 19:47:52,472 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\HPSimplified_Lt.ttf', name='HP Simplified', style='normal', variant='normal', weight=300, stretch='normal', size='scalable')) = 10.145
2025-07-23 19:47:52,472 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\FRADM.TTF', name='Franklin Gothic Demi', style='normal', variant='normal', weight=400, stretch='normal', size='scalable')) = 10.05
2025-07-23 19:47:52,472 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\TCCM____.TTF', name='Tw Cen MT Condensed', style='normal', variant='normal', weight=400, stretch='condensed', size='scalable')) = 10.25
2025-07-23 19:47:52,472 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\constan.ttf', name='Constantia', style='normal', variant='normal', weight=400, stretch='normal', size='scalable')) = 10.05
2025-07-23 19:47:52,473 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\HPSimplified_BdIt.ttf', name='HP Simplified', style='italic', variant='normal', weight=700, stretch='normal', size='scalable')) = 11.335
2025-07-23 19:47:52,473 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\Candaraz.ttf', name='Candara', style='italic', variant='normal', weight=700, stretch='normal', size='scalable')) = 11.335
2025-07-23 19:47:52,473 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\ArialNovaCond.ttf', name='Arial Nova', style='normal', variant='normal', weight=400, stretch='condensed', size='scalable')) = 10.25
2025-07-23 19:47:52,473 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\Sitka.ttc', name='Sitka Small', style='normal', variant='normal', weight=400, stretch='normal', size='scalable')) = 10.05
2025-07-23 19:47:52,473 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\consolab.ttf', name='Consolas', style='normal', variant='normal', weight=700, stretch='normal', size='scalable')) = 10.335
2025-07-23 19:47:52,473 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\NHaasGroteskTXPro-66MdIt.ttf', name='Neue Haas Grotesk Text Pro', style='italic', variant='normal', weight=500, stretch='normal', size='scalable')) = 11.145
2025-07-23 19:47:52,473 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\PERTILI.TTF', name='Perpetua Titling MT', style='normal', variant='normal', weight=300, stretch='normal', size='scalable')) = 10.145
2025-07-23 19:47:52,474 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\leelawad.ttf', name='Leelawadee', style='normal', variant='normal', weight=400, stretch='normal', size='scalable')) = 10.05
2025-07-23 19:47:52,474 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\VerdanaPro-CondLightItalic.ttf', name='Verdana Pro', style='italic', variant='normal', weight=300, stretch='condensed', size='scalable')) = 11.344999999999999
2025-07-23 19:47:52,474 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\VerdanaPro-CondItalic.ttf', name='Verdana Pro', style='italic', variant='normal', weight=400, stretch='condensed', size='scalable')) = 11.25
2025-07-23 19:47:52,474 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\calibrib.ttf', name='Calibri', style='normal', variant='normal', weight=700, stretch='normal', size='scalable')) = 10.335
2025-07-23 19:47:52,474 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\vrinda.ttf', name='Vrinda', style='normal', variant='normal', weight=400, stretch='normal', size='scalable')) = 10.05
2025-07-23 19:47:52,474 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\BOD_PSTC.TTF', name='Bodoni MT', style='normal', variant='normal', weight=300, stretch='normal', size='scalable')) = 10.145
2025-07-23 19:47:52,475 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\segoescb.ttf', name='Segoe Script', style='normal', variant='normal', weight=700, stretch='normal', size='scalable')) = 10.335
2025-07-23 19:47:52,475 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\trebucbd.ttf', name='Trebuchet MS', style='normal', variant='normal', weight=700, stretch='normal', size='scalable')) = 10.335
2025-07-23 19:47:52,475 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\KhmerUI.ttf', name='Khmer UI', style='normal', variant='normal', weight=400, stretch='normal', size='scalable')) = 10.05
2025-07-23 19:47:52,475 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\AGENCYB.TTF', name='Agency FB', style='normal', variant='normal', weight=700, stretch='normal', size='scalable')) = 10.335
2025-07-23 19:47:52,475 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\BOD_BLAR.TTF', name='Bodoni MT', style='normal', variant='normal', weight=900, stretch='normal', size='scalable')) = 10.525
2025-07-23 19:47:52,476 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\upclb.ttf', name='LilyUPC', style='normal', variant='normal', weight=700, stretch='normal', size='scalable')) = 10.335
2025-07-23 19:47:52,476 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\seguisym.ttf', name='Segoe UI Symbol', style='normal', variant='normal', weight=400, stretch='normal', size='scalable')) = 10.05
2025-07-23 19:47:52,476 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\couri.ttf', name='Courier New', style='italic', variant='normal', weight=400, stretch='normal', size='scalable')) = 11.05
2025-07-23 19:47:52,476 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\angsana.ttc', name='Angsana New', style='normal', variant='normal', weight=400, stretch='normal', size='scalable')) = 10.05
2025-07-23 19:47:52,477 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\lathab.ttf', name='Latha', style='normal', variant='normal', weight=700, stretch='normal', size='scalable')) = 10.335
2025-07-23 19:47:52,477 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\GillSansBoNova.ttf', name='Gill Sans Nova', style='normal', variant='normal', weight=700, stretch='normal', size='scalable')) = 10.335
2025-07-23 19:47:52,477 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\GeorgiaPro-Light.ttf', name='Georgia Pro', style='normal', variant='normal', weight=300, stretch='normal', size='scalable')) = 10.145
2025-07-23 19:47:52,478 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\ELEPHNTI.TTF', name='Elephant', style='italic', variant='normal', weight=400, stretch='normal', size='scalable')) = 11.05
2025-07-23 19:47:52,478 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\simhei.ttf', name='SimHei', style='normal', variant='normal', weight=400, stretch='normal', size='scalable')) = 10.05
2025-07-23 19:47:52,478 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\LeelUIsl.ttf', name='Leelawadee UI', style='normal', variant='normal', weight=350, stretch='normal', size='scalable')) = 10.0975
2025-07-23 19:47:52,478 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\CURLZ___.TTF', name='Curlz MT', style='normal', variant='normal', weight=400, stretch='normal', size='scalable')) = 10.05
2025-07-23 19:47:52,479 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\upcil.ttf', name='IrisUPC', style='normal', variant='normal', weight=400, stretch='normal', size='scalable')) = 10.05
2025-07-23 19:47:52,479 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\BRITANIC.TTF', name='Britannic Bold', style='normal', variant='normal', weight=400, stretch='normal', size='scalable')) = 10.05
2025-07-23 19:47:52,479 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\aparaj.ttf', name='Aparajita', style='normal', variant='normal', weight=400, stretch='normal', size='scalable')) = 10.05
2025-07-23 19:47:52,479 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\LeelawUI.ttf', name='Leelawadee UI', style='normal', variant='normal', weight=400, stretch='normal', size='scalable')) = 10.05
2025-07-23 19:47:52,479 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\upcei.ttf', name='EucrosiaUPC', style='italic', variant='normal', weight=400, stretch='normal', size='scalable')) = 11.05
2025-07-23 19:47:52,479 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\seguiemj.ttf', name='Segoe UI Emoji', style='normal', variant='normal', weight=400, stretch='normal', size='scalable')) = 10.05
2025-07-23 19:47:52,479 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\GeorgiaPro-CondBoldItalic.ttf', name='Georgia Pro', style='italic', variant='normal', weight=700, stretch='condensed', size='scalable')) = 11.535
2025-07-23 19:47:52,480 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\iskpotab.ttf', name='Iskoola Pota', style='normal', variant='normal', weight=700, stretch='normal', size='scalable')) = 10.335
2025-07-23 19:47:52,480 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\LeelaUIb.ttf', name='Leelawadee UI', style='normal', variant='normal', weight=700, stretch='normal', size='scalable')) = 10.335
2025-07-23 19:47:52,480 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\GARAIT.TTF', name='Garamond', style='italic', variant='normal', weight=400, stretch='normal', size='scalable')) = 11.05
2025-07-23 19:47:52,480 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\mriam.ttf', name='Miriam', style='normal', variant='normal', weight=400, stretch='normal', size='scalable')) = 10.05
2025-07-23 19:47:52,481 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\LHANDW.TTF', name='Lucida Handwriting', style='italic', variant='normal', weight=400, stretch='normal', size='scalable')) = 11.05
2025-07-23 19:47:52,481 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\timesbi.ttf', name='Times New Roman', style='italic', variant='normal', weight=700, stretch='normal', size='scalable')) = 11.335
2025-07-23 19:47:52,481 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\sylfaen.ttf', name='Sylfaen', style='normal', variant='normal', weight=400, stretch='normal', size='scalable')) = 10.05
2025-07-23 19:47:52,481 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\ROCCB___.TTF', name='Rockwell Condensed', style='normal', variant='normal', weight=700, stretch='condensed', size='scalable')) = 10.535
2025-07-23 19:47:52,481 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\RockwellNovaCond-Italic.ttf', name='Rockwell Nova', style='italic', variant='normal', weight=400, stretch='condensed', size='scalable')) = 11.25
2025-07-23 19:47:52,481 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\upcjb.ttf', name='JasmineUPC', style='normal', variant='normal', weight=700, stretch='normal', size='scalable')) = 10.335
2025-07-23 19:47:52,482 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\HPSimplified_Rg.ttf', name='HP Simplified', style='normal', variant='normal', weight=400, stretch='normal', size='scalable')) = 10.05
2025-07-23 19:47:52,482 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\timesi.ttf', name='Times New Roman', style='italic', variant='normal', weight=400, stretch='normal', size='scalable')) = 11.05
2025-07-23 19:47:52,482 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\gautamib.ttf', name='Gautami', style='normal', variant='normal', weight=700, stretch='normal', size='scalable')) = 10.335
2025-07-23 19:47:52,482 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\comicbd.ttf', name='Comic Sans MS', style='normal', variant='normal', weight=700, stretch='normal', size='scalable')) = 10.335
2025-07-23 19:47:52,482 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\yumin.ttf', name='Yu Mincho', style='normal', variant='normal', weight=400, stretch='normal', size='scalable')) = 10.05
2025-07-23 19:47:52,482 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\taileb.ttf', name='Microsoft Tai Le', style='normal', variant='normal', weight=700, stretch='normal', size='scalable')) = 10.335
2025-07-23 19:47:52,482 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\ENGR.TTF', name='Engravers MT', style='normal', variant='normal', weight=500, stretch='normal', size='scalable')) = 10.145
2025-07-23 19:47:52,483 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\lucon.ttf', name='Lucida Console', style='normal', variant='normal', weight=400, stretch='normal', size='scalable')) = 10.05
2025-07-23 19:47:52,483 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\GILBI___.TTF', name='Gill Sans MT', style='italic', variant='normal', weight=700, stretch='normal', size='scalable')) = 11.335
2025-07-23 19:47:52,483 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\msgothic.ttc', name='MS Gothic', style='normal', variant='normal', weight=400, stretch='normal', size='scalable')) = 10.05
2025-07-23 19:47:52,483 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\ArialNova-Italic.ttf', name='Arial Nova', style='italic', variant='normal', weight=400, stretch='normal', size='scalable')) = 11.05
2025-07-23 19:47:52,483 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\trebucit.ttf', name='Trebuchet MS', style='italic', variant='normal', weight=400, stretch='normal', size='scalable')) = 11.05
2025-07-23 19:47:52,483 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\segmdl2.ttf', name='Segoe MDL2 Assets', style='normal', variant='normal', weight=400, stretch='normal', size='scalable')) = 10.05
2025-07-23 19:47:52,484 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\Candarab.ttf', name='Candara', style='normal', variant='normal', weight=700, stretch='normal', size='scalable')) = 10.335
2025-07-23 19:47:52,484 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\kaiu.ttf', name='DFKai-SB', style='normal', variant='normal', weight=400, stretch='normal', size='scalable')) = 10.05
2025-07-23 19:47:52,484 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\GeorgiaPro-CondBlackItalic.ttf', name='Georgia Pro', style='italic', variant='normal', weight=900, stretch='condensed', size='scalable')) = 11.725
2025-07-23 19:47:52,484 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\malgunsl.ttf', name='Malgun Gothic', style='normal', variant='normal', weight=300, stretch='normal', size='scalable')) = 10.145
2025-07-23 19:47:52,485 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\RockwellNova-Light.ttf', name='Rockwell Nova', style='normal', variant='normal', weight=300, stretch='normal', size='scalable')) = 10.145
2025-07-23 19:47:52,485 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\utsaahbi.ttf', name='Utsaah', style='italic', variant='normal', weight=700, stretch='normal', size='scalable')) = 11.335
2025-07-23 19:47:52,485 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\moolbor.ttf', name='MoolBoran', style='normal', variant='normal', weight=400, stretch='normal', size='scalable')) = 10.05
2025-07-23 19:47:52,485 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\GeorgiaPro-BlackItalic.ttf', name='Georgia Pro', style='italic', variant='normal', weight=900, stretch='normal', size='scalable')) = 11.525
2025-07-23 19:47:52,485 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\palabi.ttf', name='Palatino Linotype', style='italic', variant='normal', weight=700, stretch='normal', size='scalable')) = 11.335
2025-07-23 19:47:52,486 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\pala.ttf', name='Palatino Linotype', style='normal', variant='normal', weight=400, stretch='normal', size='scalable')) = 10.05
2025-07-23 19:47:52,486 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\NHaasGroteskTXPro-56It.ttf', name='Neue Haas Grotesk Text Pro', style='italic', variant='normal', weight=400, stretch='normal', size='scalable')) = 11.05
2025-07-23 19:47:52,486 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\HATTEN.TTF', name='Haettenschweiler', style='normal', variant='normal', weight=400, stretch='normal', size='scalable')) = 10.05
2025-07-23 19:47:52,486 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\BOD_CB.TTF', name='Bodoni MT', style='normal', variant='normal', weight=700, stretch='condensed', size='scalable')) = 10.535
2025-07-23 19:47:52,486 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\CALISTI.TTF', name='Calisto MT', style='italic', variant='normal', weight=400, stretch='normal', size='scalable')) = 11.05
2025-07-23 19:47:52,486 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\NHaasGroteskTXPro-55Rg.ttf', name='Neue Haas Grotesk Text Pro', style='normal', variant='normal', weight=400, stretch='normal', size='scalable')) = 10.05
2025-07-23 19:47:52,487 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\kokilab.ttf', name='Kokila', style='normal', variant='normal', weight=700, stretch='normal', size='scalable')) = 10.335
2025-07-23 19:47:52,487 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\BKANT.TTF', name='Book Antiqua', style='normal', variant='normal', weight=400, stretch='normal', size='scalable')) = 10.05
2025-07-23 19:47:52,487 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\ROCKBI.TTF', name='Rockwell', style='italic', variant='normal', weight=700, stretch='normal', size='scalable')) = 11.335
2025-07-23 19:47:52,487 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\BOD_CBI.TTF', name='Bodoni MT', style='italic', variant='normal', weight=700, stretch='condensed', size='scalable')) = 11.535
2025-07-23 19:47:52,487 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\Nirmala.ttf', name='Nirmala UI', style='normal', variant='normal', weight=400, stretch='normal', size='scalable')) = 10.05
2025-07-23 19:47:52,487 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\malgunbd.ttf', name='Malgun Gothic', style='normal', variant='normal', weight=700, stretch='normal', size='scalable')) = 10.335
2025-07-23 19:47:52,488 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\SitkaI.ttc', name='Sitka Small', style='italic', variant='normal', weight=400, stretch='normal', size='scalable')) = 11.05
2025-07-23 19:47:52,489 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\ERASBD.TTF', name='Eras Bold ITC', style='normal', variant='normal', weight=400, stretch='normal', size='scalable')) = 10.05
2025-07-23 19:47:52,489 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\seguisb.ttf', name='Segoe UI', style='normal', variant='normal', weight=600, stretch='normal', size='scalable')) = 10.24
2025-07-23 19:47:52,489 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\simpfxo.ttf', name='Simplified Arabic Fixed', style='normal', variant='normal', weight=400, stretch='normal', size='scalable')) = 10.05
2025-07-23 19:47:52,490 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\SCRIPTBL.TTF', name='Script MT Bold', style='normal', variant='normal', weight=700, stretch='normal', size='scalable')) = 10.335
2025-07-23 19:47:52,490 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\Deng.ttf', name='DengXian', style='normal', variant='normal', weight=400, stretch='normal', size='scalable')) = 10.05
2025-07-23 19:47:52,490 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\Vani.ttf', name='Vani', style='normal', variant='normal', weight=400, stretch='normal', size='scalable')) = 10.05
2025-07-23 19:47:52,490 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\comic.ttf', name='Comic Sans MS', style='normal', variant='normal', weight=400, stretch='normal', size='scalable')) = 10.05
2025-07-23 19:47:52,490 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\msyh.ttc', name='Microsoft YaHei', style='normal', variant='normal', weight=400, stretch='normal', size='scalable')) = 10.05
2025-07-23 19:47:52,491 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\BRUSHSCI.TTF', name='Brush Script MT', style='italic', variant='normal', weight=400, stretch='normal', size='scalable')) = 11.05
2025-07-23 19:47:52,491 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\GOTHIC.TTF', name='Century Gothic', style='normal', variant='normal', weight=400, stretch='normal', size='scalable')) = 10.05
2025-07-23 19:47:52,491 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\GOTHICB.TTF', name='Century Gothic', style='normal', variant='normal', weight=700, stretch='normal', size='scalable')) = 10.335
2025-07-23 19:47:52,491 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\msuighub.ttf', name='Microsoft Uighur', style='normal', variant='normal', weight=700, stretch='normal', size='scalable')) = 10.335
2025-07-23 19:47:52,491 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\kokila.ttf', name='Kokila', style='normal', variant='normal', weight=400, stretch='normal', size='scalable')) = 10.05
2025-07-23 19:47:52,491 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\VerdanaPro-CondSemiBoldItalic.ttf', name='Verdana Pro', style='italic', variant='normal', weight=600, stretch='condensed', size='scalable')) = 11.44
2025-07-23 19:47:52,492 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\browalia.ttc', name='Browallia New', style='normal', variant='normal', weight=400, stretch='normal', size='scalable')) = 10.05
2025-07-23 19:47:52,492 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\SCHLBKI.TTF', name='Century Schoolbook', style='italic', variant='normal', weight=400, stretch='normal', size='scalable')) = 11.05
2025-07-23 19:47:52,492 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\PERB____.TTF', name='Perpetua', style='normal', variant='normal', weight=700, stretch='normal', size='scalable')) = 10.335
2025-07-23 19:47:52,492 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\RockwellNovaCond-Bold.ttf', name='Rockwell Nova', style='normal', variant='normal', weight=700, stretch='condensed', size='scalable')) = 10.535
2025-07-23 19:47:52,492 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\ELEPHNT.TTF', name='Elephant', style='normal', variant='normal', weight=400, stretch='normal', size='scalable')) = 10.05
2025-07-23 19:47:52,492 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\NIAGENG.TTF', name='Niagara Engraved', style='normal', variant='normal', weight=400, stretch='normal', size='scalable')) = 10.05
2025-07-23 19:47:52,493 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\courbi.ttf', name='Courier New', style='italic', variant='normal', weight=700, stretch='normal', size='scalable')) = 11.335
2025-07-23 19:47:52,493 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\impact.ttf', name='Impact', style='normal', variant='normal', weight=400, stretch='normal', size='scalable')) = 10.05
2025-07-23 19:47:52,493 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\trebucbi.ttf', name='Trebuchet MS', style='italic', variant='normal', weight=700, stretch='normal', size='scalable')) = 11.335
2025-07-23 19:47:52,493 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\upcdb.ttf', name='DilleniaUPC', style='normal', variant='normal', weight=700, stretch='normal', size='scalable')) = 10.335
2025-07-23 19:47:52,494 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\LTYPEB.TTF', name='Lucida Sans Typewriter', style='normal', variant='normal', weight=600, stretch='normal', size='scalable')) = 10.24
2025-07-23 19:47:52,494 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\NHaasGroteskTXPro-75Bd.ttf', name='Neue Haas Grotesk Text Pro', style='normal', variant='normal', weight=700, stretch='normal', size='scalable')) = 10.335
2025-07-23 19:47:52,494 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\BRADHITC.TTF', name='Bradley Hand ITC', style='normal', variant='normal', weight=400, stretch='normal', size='scalable')) = 10.05
2025-07-23 19:47:52,494 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\GeorgiaPro-BoldItalic.ttf', name='Georgia Pro', style='italic', variant='normal', weight=700, stretch='normal', size='scalable')) = 11.335
2025-07-23 19:47:52,494 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\palab.ttf', name='Palatino Linotype', style='normal', variant='normal', weight=700, stretch='normal', size='scalable')) = 10.335
2025-07-23 19:47:52,494 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\malgun.ttf', name='Malgun Gothic', style='normal', variant='normal', weight=400, stretch='normal', size='scalable')) = 10.05
2025-07-23 19:47:52,495 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\NirmalaS.ttf', name='Nirmala UI', style='normal', variant='normal', weight=350, stretch='normal', size='scalable')) = 10.0975
2025-07-23 19:47:52,495 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\upcii.ttf', name='IrisUPC', style='italic', variant='normal', weight=400, stretch='normal', size='scalable')) = 11.05
2025-07-23 19:47:52,495 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\calibriz.ttf', name='Calibri', style='italic', variant='normal', weight=700, stretch='normal', size='scalable')) = 11.335
2025-07-23 19:47:52,495 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\tahomabd.ttf', name='Tahoma', style='normal', variant='normal', weight=700, stretch='normal', size='scalable')) = 10.335
2025-07-23 19:47:52,495 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\msmincho.ttc', name='MS Mincho', style='normal', variant='normal', weight=400, stretch='normal', size='scalable')) = 10.05
2025-07-23 19:47:52,495 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\segoeuii.ttf', name='Segoe UI', style='italic', variant='normal', weight=400, stretch='normal', size='scalable')) = 11.05
2025-07-23 19:47:52,496 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\GARA.TTF', name='Garamond', style='normal', variant='normal', weight=400, stretch='normal', size='scalable')) = 10.05
2025-07-23 19:47:52,496 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\STENCIL.TTF', name='Stencil', style='normal', variant='normal', weight=400, stretch='normal', size='scalable')) = 10.05
2025-07-23 19:47:52,496 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\ebrima.ttf', name='Ebrima', style='normal', variant='normal', weight=400, stretch='normal', size='scalable')) = 10.05
2025-07-23 19:47:52,496 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\tahoma.ttf', name='Tahoma', style='normal', variant='normal', weight=400, stretch='normal', size='scalable')) = 10.05
2025-07-23 19:47:52,496 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\CASTELAR.TTF', name='Castellar', style='normal', variant='normal', weight=400, stretch='normal', size='scalable')) = 10.05
2025-07-23 19:47:52,496 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\CENTURY.TTF', name='Century', style='normal', variant='normal', weight=400, stretch='normal', size='scalable')) = 10.05
2025-07-23 19:47:52,497 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\segoeuiz.ttf', name='Segoe UI', style='italic', variant='normal', weight=700, stretch='normal', size='scalable')) = 11.335
2025-07-23 19:47:52,497 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\GeorgiaPro-SemiBoldItalic.ttf', name='Georgia Pro', style='italic', variant='normal', weight=600, stretch='normal', size='scalable')) = 11.24
2025-07-23 19:47:52,497 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\GILLUBCD.TTF', name='Gill Sans Ultra Bold Condensed', style='normal', variant='normal', weight=400, stretch='condensed', size='scalable')) = 10.25
2025-07-23 19:47:52,497 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\upcfb.ttf', name='FreesiaUPC', style='normal', variant='normal', weight=700, stretch='normal', size='scalable')) = 10.335
2025-07-23 19:47:52,497 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\GillSansCondLightItNova.ttf', name='Gill Sans Nova', style='italic', variant='normal', weight=300, stretch='condensed', size='scalable')) = 11.344999999999999
2025-07-23 19:47:52,497 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\VerdanaPro-Black.ttf', name='Verdana Pro', style='normal', variant='normal', weight=900, stretch='normal', size='scalable')) = 10.525
2025-07-23 19:47:52,498 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\arialnbi.ttf', name='Arial', style='italic', variant='normal', weight=700, stretch='condensed', size='scalable')) = 7.8986363636363635
2025-07-23 19:47:52,498 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\upcibi.ttf', name='IrisUPC', style='italic', variant='normal', weight=700, stretch='normal', size='scalable')) = 11.335
2025-07-23 19:47:52,498 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\BOOKOS.TTF', name='Bookman Old Style', style='normal', variant='normal', weight=300, stretch='normal', size='scalable')) = 10.145
2025-07-23 19:47:52,498 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\GillSansCondLightNova.ttf', name='Gill Sans Nova', style='normal', variant='normal', weight=300, stretch='condensed', size='scalable')) = 10.344999999999999
2025-07-23 19:47:52,498 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\BELL.TTF', name='Bell MT', style='normal', variant='normal', weight=400, stretch='normal', size='scalable')) = 10.05
2025-07-23 19:47:52,498 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\OUTLOOK.TTF', name='MS Outlook', style='normal', variant='normal', weight=400, stretch='normal', size='scalable')) = 10.05
2025-07-23 19:47:52,499 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\ITCKRIST.TTF', name='Kristen ITC', style='normal', variant='normal', weight=400, stretch='normal', size='scalable')) = 10.05
2025-07-23 19:47:52,499 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\simkai.ttf', name='KaiTi', style='normal', variant='normal', weight=400, stretch='normal', size='scalable')) = 10.05
2025-07-23 19:47:52,499 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\aldhabi.ttf', name='Aldhabi', style='normal', variant='normal', weight=400, stretch='normal', size='scalable')) = 10.05
2025-07-23 19:47:52,499 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\FREESCPT.TTF', name='Freestyle Script', style='normal', variant='normal', weight=400, stretch='normal', size='scalable')) = 10.05
2025-07-23 19:47:52,499 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\PERI____.TTF', name='Perpetua', style='italic', variant='normal', weight=400, stretch='normal', size='scalable')) = 11.05
2025-07-23 19:47:52,499 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\arialn.ttf', name='Arial', style='normal', variant='normal', weight=400, stretch='condensed', size='scalable')) = 6.613636363636363
2025-07-23 19:47:52,499 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\YuGothM.ttc', name='Yu Gothic', style='normal', variant='normal', weight=500, stretch='normal', size='scalable')) = 10.145
2025-07-23 19:47:52,500 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\batang.ttc', name='Batang', style='normal', variant='normal', weight=400, stretch='normal', size='scalable')) = 10.05
2025-07-23 19:47:52,500 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\meiryo.ttc', name='Meiryo', style='normal', variant='normal', weight=400, stretch='normal', size='scalable')) = 10.05
2025-07-23 19:47:52,500 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\shrutib.ttf', name='Shruti', style='normal', variant='normal', weight=700, stretch='normal', size='scalable')) = 10.335
2025-07-23 19:47:52,500 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\kokilabi.ttf', name='Kokila', style='italic', variant='normal', weight=700, stretch='normal', size='scalable')) = 11.335
2025-07-23 19:47:52,500 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\GOUDYSTO.TTF', name='Goudy Stout', style='normal', variant='normal', weight=400, stretch='normal', size='scalable')) = 10.05
2025-07-23 19:47:52,500 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\seguihis.ttf', name='Segoe UI Historic', style='normal', variant='normal', weight=400, stretch='normal', size='scalable')) = 10.05
2025-07-23 19:47:52,501 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\majallab.ttf', name='Sakkal Majalla', style='normal', variant='normal', weight=700, stretch='normal', size='scalable')) = 10.335
2025-07-23 19:47:52,501 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\GILC____.TTF', name='Gill Sans MT Condensed', style='normal', variant='normal', weight=400, stretch='condensed', size='scalable')) = 10.25
2025-07-23 19:47:52,501 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\corbelli.ttf', name='Corbel', style='italic', variant='normal', weight=300, stretch='normal', size='scalable')) = 11.145
2025-07-23 19:47:52,501 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\seguisbi.ttf', name='Segoe UI', style='italic', variant='normal', weight=600, stretch='normal', size='scalable')) = 11.24
2025-07-23 19:47:52,502 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\BOD_CI.TTF', name='Bodoni MT', style='italic', variant='normal', weight=400, stretch='condensed', size='scalable')) = 11.25
2025-07-23 19:47:52,502 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\VerdanaPro-CondSemiBold.ttf', name='Verdana Pro', style='normal', variant='normal', weight=600, stretch='condensed', size='scalable')) = 10.44
2025-07-23 19:47:52,502 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\YuGothR.ttc', name='Yu Gothic', style='normal', variant='normal', weight=400, stretch='normal', size='scalable')) = 10.05
2025-07-23 19:47:52,502 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\ONYX.TTF', name='Onyx', style='normal', variant='normal', weight=400, stretch='normal', size='scalable')) = 10.05
2025-07-23 19:47:52,503 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\ArialNova-LightItalic.ttf', name='Arial Nova', style='italic', variant='normal', weight=300, stretch='normal', size='scalable')) = 11.145
2025-07-23 19:47:52,504 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\phagspab.ttf', name='Microsoft PhagsPa', style='normal', variant='normal', weight=700, stretch='normal', size='scalable')) = 10.335
2025-07-23 19:47:52,504 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\BOD_R.TTF', name='Bodoni MT', style='normal', variant='normal', weight=400, stretch='normal', size='scalable')) = 10.05
2025-07-23 19:47:52,504 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\REFSPCL.TTF', name='MS Reference Specialty', style='normal', variant='normal', weight=400, stretch='normal', size='scalable')) = 10.05
2025-07-23 19:47:52,504 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\gulim.ttc', name='Gulim', style='normal', variant='normal', weight=400, stretch='normal', size='scalable')) = 10.05
2025-07-23 19:47:52,504 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\ANTQUAB.TTF', name='Book Antiqua', style='normal', variant='normal', weight=700, stretch='normal', size='scalable')) = 10.335
2025-07-23 19:47:52,505 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\gisha.ttf', name='Gisha', style='normal', variant='normal', weight=400, stretch='normal', size='scalable')) = 10.05
2025-07-23 19:47:52,505 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\LBRITE.TTF', name='Lucida Bright', style='normal', variant='normal', weight=400, stretch='normal', size='scalable')) = 10.05
2025-07-23 19:47:52,505 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\calibrii.ttf', name='Calibri', style='italic', variant='normal', weight=400, stretch='normal', size='scalable')) = 11.05
2025-07-23 19:47:52,505 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\LTYPEO.TTF', name='Lucida Sans Typewriter', style='oblique', variant='normal', weight=400, stretch='normal', size='scalable')) = 11.05
2025-07-23 19:47:52,505 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\webdings.ttf', name='Webdings', style='normal', variant='normal', weight=400, stretch='normal', size='scalable')) = 10.05
2025-07-23 19:47:52,505 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\WINGDNG2.TTF', name='Wingdings 2', style='normal', variant='normal', weight=400, stretch='normal', size='scalable')) = 10.05
2025-07-23 19:47:52,505 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\corbel.ttf', name='Corbel', style='normal', variant='normal', weight=400, stretch='normal', size='scalable')) = 10.05
2025-07-23 19:47:52,506 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\MAIAN.TTF', name='Maiandra GD', style='normal', variant='normal', weight=400, stretch='normal', size='scalable')) = 10.05
2025-07-23 19:47:52,506 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\YuGothL.ttc', name='Yu Gothic', style='normal', variant='normal', weight=300, stretch='normal', size='scalable')) = 10.145
2025-07-23 19:47:52,506 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\ERASLGHT.TTF', name='Eras Light ITC', style='normal', variant='normal', weight=400, stretch='normal', size='scalable')) = 10.05
2025-07-23 19:47:52,506 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\segoeuisl.ttf', name='Segoe UI', style='normal', variant='normal', weight=350, stretch='normal', size='scalable')) = 10.0975
2025-07-23 19:47:52,506 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\RockwellNova-ExtraBold.ttf', name='Rockwell Nova', style='normal', variant='normal', weight=800, stretch='normal', size='scalable')) = 10.43
2025-07-23 19:47:52,506 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\RAGE.TTF', name='Rage Italic', style='italic', variant='normal', weight=400, stretch='normal', size='scalable')) = 11.05
2025-07-23 19:47:52,507 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\GillSansLightNova.ttf', name='Gill Sans Nova', style='normal', variant='normal', weight=300, stretch='normal', size='scalable')) = 10.145
2025-07-23 19:47:52,507 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\RockwellNova-BoldItalic.ttf', name='Rockwell Nova', style='italic', variant='normal', weight=700, stretch='normal', size='scalable')) = 11.335
2025-07-23 19:47:52,507 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\calibril.ttf', name='Calibri', style='normal', variant='normal', weight=300, stretch='normal', size='scalable')) = 10.145
2025-07-23 19:47:52,507 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\NHaasGroteskTXPro-65Md.ttf', name='Neue Haas Grotesk Text Pro', style='normal', variant='normal', weight=500, stretch='normal', size='scalable')) = 10.145
2025-07-23 19:47:52,507 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\OLDENGL.TTF', name='Old English Text MT', style='normal', variant='normal', weight=400, stretch='normal', size='scalable')) = 10.05
2025-07-23 19:47:52,507 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\BIZ-UDGothicR.ttc', name='BIZ UDGothic', style='normal', variant='normal', weight=400, stretch='normal', size='scalable')) = 10.05
2025-07-23 19:47:52,508 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\trado.ttf', name='Traditional Arabic', style='normal', variant='normal', weight=400, stretch='normal', size='scalable')) = 10.05
2025-07-23 19:47:52,508 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\upcdl.ttf', name='DilleniaUPC', style='normal', variant='normal', weight=400, stretch='normal', size='scalable')) = 10.05
2025-07-23 19:47:52,508 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\GeorgiaPro-CondBold.ttf', name='Georgia Pro', style='normal', variant='normal', weight=700, stretch='condensed', size='scalable')) = 10.535
2025-07-23 19:47:52,508 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\RockwellNova-ExtraBoldItalic.ttf', name='Rockwell Nova', style='italic', variant='normal', weight=800, stretch='normal', size='scalable')) = 11.43
2025-07-23 19:47:52,508 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\SCHLBKBI.TTF', name='Century Schoolbook', style='italic', variant='normal', weight=700, stretch='normal', size='scalable')) = 11.335
2025-07-23 19:47:52,508 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\shruti.ttf', name='Shruti', style='normal', variant='normal', weight=400, stretch='normal', size='scalable')) = 10.05
2025-07-23 19:47:52,508 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\RockwellNova.ttf', name='Rockwell Nova', style='normal', variant='normal', weight=400, stretch='normal', size='scalable')) = 10.05
2025-07-23 19:47:52,509 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\ArialNovaCond-Bold.ttf', name='Arial Nova', style='normal', variant='normal', weight=700, stretch='condensed', size='scalable')) = 10.535
2025-07-23 19:47:52,509 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\Sanskr.ttf', name='Sanskrit Text', style='normal', variant='normal', weight=400, stretch='normal', size='scalable')) = 10.05
2025-07-23 19:47:52,509 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\upcfbi.ttf', name='FreesiaUPC', style='italic', variant='normal', weight=700, stretch='normal', size='scalable')) = 11.335
2025-07-23 19:47:52,510 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\gadugi.ttf', name='Gadugi', style='normal', variant='normal', weight=400, stretch='normal', size='scalable')) = 10.05
2025-07-23 19:47:52,510 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\times.ttf', name='Times New Roman', style='normal', variant='normal', weight=400, stretch='normal', size='scalable')) = 10.05
2025-07-23 19:47:52,510 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\RockwellNova-Bold.ttf', name='Rockwell Nova', style='normal', variant='normal', weight=700, stretch='normal', size='scalable')) = 10.335
2025-07-23 19:47:52,510 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\VINERITC.TTF', name='Viner Hand ITC', style='normal', variant='normal', weight=400, stretch='normal', size='scalable')) = 10.05
2025-07-23 19:47:52,510 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\gautami.ttf', name='Gautami', style='normal', variant='normal', weight=400, stretch='normal', size='scalable')) = 10.05
2025-07-23 19:47:52,510 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\raavib.ttf', name='Raavi', style='normal', variant='normal', weight=700, stretch='normal', size='scalable')) = 10.335
2025-07-23 19:47:52,511 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\arialbi.ttf', name='Arial', style='italic', variant='normal', weight=700, stretch='normal', size='scalable')) = 7.698636363636363
2025-07-23 19:47:52,511 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\SHOWG.TTF', name='Showcard Gothic', style='normal', variant='normal', weight=400, stretch='normal', size='scalable')) = 10.05
2025-07-23 19:47:52,511 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\GeorgiaPro-CondSemiBold.ttf', name='Georgia Pro', style='normal', variant='normal', weight=600, stretch='condensed', size='scalable')) = 10.44
2025-07-23 19:47:52,511 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\GeorgiaPro-Regular.ttf', name='Georgia Pro', style='normal', variant='normal', weight=400, stretch='normal', size='scalable')) = 10.05
2025-07-23 19:47:52,512 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\NHaasGroteskTXPro-76BdIt.ttf', name='Neue Haas Grotesk Text Pro', style='italic', variant='normal', weight=700, stretch='normal', size='scalable')) = 11.335
2025-07-23 19:47:52,512 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\msuighur.ttf', name='Microsoft Uighur', style='normal', variant='normal', weight=400, stretch='normal', size='scalable')) = 10.05
2025-07-23 19:47:52,512 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\LTYPEBO.TTF', name='Lucida Sans Typewriter', style='oblique', variant='normal', weight=600, stretch='normal', size='scalable')) = 11.24
2025-07-23 19:47:52,512 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\LCALLIG.TTF', name='Lucida Calligraphy', style='italic', variant='normal', weight=400, stretch='normal', size='scalable')) = 11.05
2025-07-23 19:47:52,512 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\LBRITEI.TTF', name='Lucida Bright', style='italic', variant='normal', weight=400, stretch='normal', size='scalable')) = 11.05
2025-07-23 19:47:52,512 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\mingliu.ttc', name='MingLiU', style='normal', variant='normal', weight=400, stretch='normal', size='scalable')) = 10.05
2025-07-23 19:47:52,513 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\BOOKOSBI.TTF', name='Bookman Old Style', style='italic', variant='normal', weight=600, stretch='normal', size='scalable')) = 11.24
2025-07-23 19:47:52,513 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\ERASDEMI.TTF', name='Eras Demi ITC', style='normal', variant='normal', weight=400, stretch='normal', size='scalable')) = 10.05
2025-07-23 19:47:52,513 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\constanz.ttf', name='Constantia', style='italic', variant='normal', weight=700, stretch='normal', size='scalable')) = 11.335
2025-07-23 19:47:52,513 - DEBUG - findfont: Matching sans\-serif:style=normal:variant=normal:weight=normal:stretch=normal:size=10.0 to DejaVu Sans ('C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\site-packages\\matplotlib\\mpl-data\\fonts\\ttf\\DejaVuSans.ttf') with score of 0.050000.
2025-07-23 19:47:52,535 - INFO - Using categorical units to plot a list of strings that are all parsable as floats or dates. If these strings should be plotted as numbers, cast to the appropriate data type before plotting.
2025-07-23 19:47:52,539 - INFO - Using categorical units to plot a list of strings that are all parsable as floats or dates. If these strings should be plotted as numbers, cast to the appropriate data type before plotting.
2025-07-23 19:47:52,649 - DEBUG - findfont: Matching Arial:style=normal:variant=normal:weight=normal:stretch=normal:size=10.0.
2025-07-23 19:47:52,650 - DEBUG - findfont: score(FontEntry(fname='C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\site-packages\\matplotlib\\mpl-data\\fonts\\ttf\\cmss10.ttf', name='cmss10', style='normal', variant='normal', weight=400, stretch='normal', size='scalable')) = 10.05
2025-07-23 19:47:52,650 - DEBUG - findfont: score(FontEntry(fname='C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\site-packages\\matplotlib\\mpl-data\\fonts\\ttf\\DejaVuSansMono.ttf', name='DejaVu Sans Mono', style='normal', variant='normal', weight=400, stretch='normal', size='scalable')) = 10.05
2025-07-23 19:47:52,650 - DEBUG - findfont: score(FontEntry(fname='C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\site-packages\\matplotlib\\mpl-data\\fonts\\ttf\\STIXGeneral.ttf', name='STIXGeneral', style='normal', variant='normal', weight=400, stretch='normal', size='scalable')) = 10.05
2025-07-23 19:47:52,651 - DEBUG - findfont: score(FontEntry(fname='C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\site-packages\\matplotlib\\mpl-data\\fonts\\ttf\\STIXGeneralItalic.ttf', name='STIXGeneral', style='italic', variant='normal', weight=400, stretch='normal', size='scalable')) = 11.05
2025-07-23 19:47:52,651 - DEBUG - findfont: score(FontEntry(fname='C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\site-packages\\matplotlib\\mpl-data\\fonts\\ttf\\STIXNonUniBol.ttf', name='STIXNonUnicode', style='normal', variant='normal', weight=700, stretch='normal', size='scalable')) = 10.335
2025-07-23 19:47:52,651 - DEBUG - findfont: score(FontEntry(fname='C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\site-packages\\matplotlib\\mpl-data\\fonts\\ttf\\cmsy10.ttf', name='cmsy10', style='normal', variant='normal', weight=400, stretch='normal', size='scalable')) = 10.05
2025-07-23 19:47:52,651 - DEBUG - findfont: score(FontEntry(fname='C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\site-packages\\matplotlib\\mpl-data\\fonts\\ttf\\DejaVuSansMono-Oblique.ttf', name='DejaVu Sans Mono', style='oblique', variant='normal', weight=400, stretch='normal', size='scalable')) = 11.05
2025-07-23 19:47:52,651 - DEBUG - findfont: score(FontEntry(fname='C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\site-packages\\matplotlib\\mpl-data\\fonts\\ttf\\DejaVuSansMono-Bold.ttf', name='DejaVu Sans Mono', style='normal', variant='normal', weight=700, stretch='normal', size='scalable')) = 10.335
2025-07-23 19:47:52,651 - DEBUG - findfont: score(FontEntry(fname='C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\site-packages\\matplotlib\\mpl-data\\fonts\\ttf\\DejaVuSerif-Italic.ttf', name='DejaVu Serif', style='italic', variant='normal', weight=400, stretch='normal', size='scalable')) = 11.05
2025-07-23 19:47:52,652 - DEBUG - findfont: score(FontEntry(fname='C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\site-packages\\matplotlib\\mpl-data\\fonts\\ttf\\cmtt10.ttf', name='cmtt10', style='normal', variant='normal', weight=400, stretch='normal', size='scalable')) = 10.05
2025-07-23 19:47:52,652 - DEBUG - findfont: score(FontEntry(fname='C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\site-packages\\matplotlib\\mpl-data\\fonts\\ttf\\STIXSizThreeSymReg.ttf', name='STIXSizeThreeSym', style='normal', variant='normal', weight=400, stretch='normal', size='scalable')) = 10.05
2025-07-23 19:47:52,652 - DEBUG - findfont: score(FontEntry(fname='C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\site-packages\\matplotlib\\mpl-data\\fonts\\ttf\\cmex10.ttf', name='cmex10', style='normal', variant='normal', weight=400, stretch='normal', size='scalable')) = 10.05
2025-07-23 19:47:52,652 - DEBUG - findfont: score(FontEntry(fname='C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\site-packages\\matplotlib\\mpl-data\\fonts\\ttf\\STIXSizFourSymBol.ttf', name='STIXSizeFourSym', style='normal', variant='normal', weight=700, stretch='normal', size='scalable')) = 10.335
2025-07-23 19:47:52,652 - DEBUG - findfont: score(FontEntry(fname='C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\site-packages\\matplotlib\\mpl-data\\fonts\\ttf\\STIXSizFourSymReg.ttf', name='STIXSizeFourSym', style='normal', variant='normal', weight=400, stretch='normal', size='scalable')) = 10.05
2025-07-23 19:47:52,652 - DEBUG - findfont: score(FontEntry(fname='C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\site-packages\\matplotlib\\mpl-data\\fonts\\ttf\\STIXSizTwoSymBol.ttf', name='STIXSizeTwoSym', style='normal', variant='normal', weight=700, stretch='normal', size='scalable')) = 10.335
2025-07-23 19:47:52,653 - DEBUG - findfont: score(FontEntry(fname='C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\site-packages\\matplotlib\\mpl-data\\fonts\\ttf\\STIXSizTwoSymReg.ttf', name='STIXSizeTwoSym', style='normal', variant='normal', weight=400, stretch='normal', size='scalable')) = 10.05
2025-07-23 19:47:52,653 - DEBUG - findfont: score(FontEntry(fname='C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\site-packages\\matplotlib\\mpl-data\\fonts\\ttf\\STIXSizFiveSymReg.ttf', name='STIXSizeFiveSym', style='normal', variant='normal', weight=400, stretch='normal', size='scalable')) = 10.05
2025-07-23 19:47:52,654 - DEBUG - findfont: score(FontEntry(fname='C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\site-packages\\matplotlib\\mpl-data\\fonts\\ttf\\cmmi10.ttf', name='cmmi10', style='normal', variant='normal', weight=400, stretch='normal', size='scalable')) = 10.05
2025-07-23 19:47:52,654 - DEBUG - findfont: score(FontEntry(fname='C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\site-packages\\matplotlib\\mpl-data\\fonts\\ttf\\STIXSizOneSymReg.ttf', name='STIXSizeOneSym', style='normal', variant='normal', weight=400, stretch='normal', size='scalable')) = 10.05
2025-07-23 19:47:52,654 - DEBUG - findfont: score(FontEntry(fname='C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\site-packages\\matplotlib\\mpl-data\\fonts\\ttf\\DejaVuSansMono-BoldOblique.ttf', name='DejaVu Sans Mono', style='oblique', variant='normal', weight=700, stretch='normal', size='scalable')) = 11.335
2025-07-23 19:47:52,654 - DEBUG - findfont: score(FontEntry(fname='C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\site-packages\\matplotlib\\mpl-data\\fonts\\ttf\\STIXNonUni.ttf', name='STIXNonUnicode', style='normal', variant='normal', weight=400, stretch='normal', size='scalable')) = 10.05
2025-07-23 19:47:52,654 - DEBUG - findfont: score(FontEntry(fname='C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\site-packages\\matplotlib\\mpl-data\\fonts\\ttf\\DejaVuSerif-BoldItalic.ttf', name='DejaVu Serif', style='italic', variant='normal', weight=700, stretch='normal', size='scalable')) = 11.335
2025-07-23 19:47:52,654 - DEBUG - findfont: score(FontEntry(fname='C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\site-packages\\matplotlib\\mpl-data\\fonts\\ttf\\STIXGeneralBol.ttf', name='STIXGeneral', style='normal', variant='normal', weight=700, stretch='normal', size='scalable')) = 10.335
2025-07-23 19:47:52,655 - DEBUG - findfont: score(FontEntry(fname='C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\site-packages\\matplotlib\\mpl-data\\fonts\\ttf\\DejaVuSerif.ttf', name='DejaVu Serif', style='normal', variant='normal', weight=400, stretch='normal', size='scalable')) = 10.05
2025-07-23 19:47:52,655 - DEBUG - findfont: score(FontEntry(fname='C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\site-packages\\matplotlib\\mpl-data\\fonts\\ttf\\STIXNonUniBolIta.ttf', name='STIXNonUnicode', style='italic', variant='normal', weight=700, stretch='normal', size='scalable')) = 11.335
2025-07-23 19:47:52,655 - DEBUG - findfont: score(FontEntry(fname='C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\site-packages\\matplotlib\\mpl-data\\fonts\\ttf\\STIXSizOneSymBol.ttf', name='STIXSizeOneSym', style='normal', variant='normal', weight=700, stretch='normal', size='scalable')) = 10.335
2025-07-23 19:47:52,655 - DEBUG - findfont: score(FontEntry(fname='C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\site-packages\\matplotlib\\mpl-data\\fonts\\ttf\\DejaVuSerif-Bold.ttf', name='DejaVu Serif', style='normal', variant='normal', weight=700, stretch='normal', size='scalable')) = 10.335
2025-07-23 19:47:52,655 - DEBUG - findfont: score(FontEntry(fname='C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\site-packages\\matplotlib\\mpl-data\\fonts\\ttf\\DejaVuSans-Oblique.ttf', name='DejaVu Sans', style='oblique', variant='normal', weight=400, stretch='normal', size='scalable')) = 11.05
2025-07-23 19:47:52,656 - DEBUG - findfont: score(FontEntry(fname='C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\site-packages\\matplotlib\\mpl-data\\fonts\\ttf\\DejaVuSans-Bold.ttf', name='DejaVu Sans', style='normal', variant='normal', weight=700, stretch='normal', size='scalable')) = 10.335
2025-07-23 19:47:52,656 - DEBUG - findfont: score(FontEntry(fname='C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\site-packages\\matplotlib\\mpl-data\\fonts\\ttf\\cmr10.ttf', name='cmr10', style='normal', variant='normal', weight=400, stretch='normal', size='scalable')) = 10.05
2025-07-23 19:47:52,656 - DEBUG - findfont: score(FontEntry(fname='C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\site-packages\\matplotlib\\mpl-data\\fonts\\ttf\\cmb10.ttf', name='cmb10', style='normal', variant='normal', weight=400, stretch='normal', size='scalable')) = 10.05
2025-07-23 19:47:52,656 - DEBUG - findfont: score(FontEntry(fname='C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\site-packages\\matplotlib\\mpl-data\\fonts\\ttf\\DejaVuSansDisplay.ttf', name='DejaVu Sans Display', style='normal', variant='normal', weight=400, stretch='normal', size='scalable')) = 10.05
2025-07-23 19:47:52,656 - DEBUG - findfont: score(FontEntry(fname='C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\site-packages\\matplotlib\\mpl-data\\fonts\\ttf\\DejaVuSans.ttf', name='DejaVu Sans', style='normal', variant='normal', weight=400, stretch='normal', size='scalable')) = 10.05
2025-07-23 19:47:52,656 - DEBUG - findfont: score(FontEntry(fname='C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\site-packages\\matplotlib\\mpl-data\\fonts\\ttf\\STIXSizThreeSymBol.ttf', name='STIXSizeThreeSym', style='normal', variant='normal', weight=700, stretch='normal', size='scalable')) = 10.335
2025-07-23 19:47:52,657 - DEBUG - findfont: score(FontEntry(fname='C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\site-packages\\matplotlib\\mpl-data\\fonts\\ttf\\STIXGeneralBolIta.ttf', name='STIXGeneral', style='italic', variant='normal', weight=700, stretch='normal', size='scalable')) = 11.335
2025-07-23 19:47:52,657 - DEBUG - findfont: score(FontEntry(fname='C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\site-packages\\matplotlib\\mpl-data\\fonts\\ttf\\DejaVuSerifDisplay.ttf', name='DejaVu Serif Display', style='normal', variant='normal', weight=400, stretch='normal', size='scalable')) = 10.05
2025-07-23 19:47:52,657 - DEBUG - findfont: score(FontEntry(fname='C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\site-packages\\matplotlib\\mpl-data\\fonts\\ttf\\DejaVuSans-BoldOblique.ttf', name='DejaVu Sans', style='oblique', variant='normal', weight=700, stretch='normal', size='scalable')) = 11.335
2025-07-23 19:47:52,657 - DEBUG - findfont: score(FontEntry(fname='C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\site-packages\\matplotlib\\mpl-data\\fonts\\ttf\\STIXNonUniIta.ttf', name='STIXNonUnicode', style='italic', variant='normal', weight=400, stretch='normal', size='scalable')) = 11.05
2025-07-23 19:47:52,657 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\VerdanaPro-Regular.ttf', name='Verdana Pro', style='normal', variant='normal', weight=400, stretch='normal', size='scalable')) = 10.05
2025-07-23 19:47:52,657 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\ArialNova.ttf', name='Arial Nova', style='normal', variant='normal', weight=400, stretch='normal', size='scalable')) = 10.05
2025-07-23 19:47:52,658 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\dokchamp.ttf', name='DokChampa', style='normal', variant='normal', weight=400, stretch='normal', size='scalable')) = 10.05
2025-07-23 19:47:52,658 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\ROCKI.TTF', name='Rockwell', style='italic', variant='normal', weight=400, stretch='normal', size='scalable')) = 11.05
2025-07-23 19:47:52,658 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\upcebi.ttf', name='EucrosiaUPC', style='italic', variant='normal', weight=700, stretch='normal', size='scalable')) = 11.335
2025-07-23 19:47:52,658 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\LFAXDI.TTF', name='Lucida Fax', style='italic', variant='normal', weight=600, stretch='normal', size='scalable')) = 11.24
2025-07-23 19:47:52,658 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\BOD_I.TTF', name='Bodoni MT', style='italic', variant='normal', weight=400, stretch='normal', size='scalable')) = 11.05
2025-07-23 19:47:52,659 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\comicz.ttf', name='Comic Sans MS', style='italic', variant='normal', weight=700, stretch='normal', size='scalable')) = 11.335
2025-07-23 19:47:52,659 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\VerdanaPro-CondRegular.ttf', name='Verdana Pro', style='normal', variant='normal', weight=400, stretch='condensed', size='scalable')) = 10.25
2025-07-23 19:47:52,659 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\GillSansLightItNova.ttf', name='Gill Sans Nova', style='italic', variant='normal', weight=300, stretch='normal', size='scalable')) = 11.145
2025-07-23 19:47:52,659 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\NotoSansTC-VF.ttf', name='Noto Sans TC', style='normal', variant='normal', weight=100, stretch='normal', size='scalable')) = 10.335
2025-07-23 19:47:52,659 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\upcll.ttf', name='LilyUPC', style='normal', variant='normal', weight=400, stretch='normal', size='scalable')) = 10.05
2025-07-23 19:47:52,659 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\REFSAN.TTF', name='MS Reference Sans Serif', style='normal', variant='normal', weight=400, stretch='normal', size='scalable')) = 10.05
2025-07-23 19:47:52,659 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\consolai.ttf', name='Consolas', style='italic', variant='normal', weight=400, stretch='normal', size='scalable')) = 11.05
2025-07-23 19:47:52,660 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\verdana.ttf', name='Verdana', style='normal', variant='normal', weight=400, stretch='normal', size='scalable')) = 10.05
2025-07-23 19:47:52,660 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\SCHLBKB.TTF', name='Century Schoolbook', style='normal', variant='normal', weight=700, stretch='normal', size='scalable')) = 10.335
2025-07-23 19:47:52,660 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\SNAP____.TTF', name='Snap ITC', style='normal', variant='normal', weight=400, stretch='normal', size='scalable')) = 10.05
2025-07-23 19:47:52,660 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\upckbi.ttf', name='KodchiangUPC', style='italic', variant='normal', weight=700, stretch='normal', size='scalable')) = 11.335
2025-07-23 19:47:52,660 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\latha.ttf', name='Latha', style='normal', variant='normal', weight=400, stretch='normal', size='scalable')) = 10.05
2025-07-23 19:47:52,660 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\monbaiti.ttf', name='Mongolian Baiti', style='normal', variant='normal', weight=400, stretch='normal', size='scalable')) = 10.05
2025-07-23 19:47:52,661 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\JOKERMAN.TTF', name='Jokerman', style='normal', variant='normal', weight=400, stretch='normal', size='scalable')) = 10.05
2025-07-23 19:47:52,661 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\mangalb.ttf', name='Mangal', style='normal', variant='normal', weight=700, stretch='normal', size='scalable')) = 10.335
2025-07-23 19:47:52,661 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\GeorgiaPro-CondItalic.ttf', name='Georgia Pro', style='italic', variant='normal', weight=400, stretch='condensed', size='scalable')) = 11.25
2025-07-23 19:47:52,662 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\arialni.ttf', name='Arial', style='italic', variant='normal', weight=400, stretch='condensed', size='scalable')) = 1.25
2025-07-23 19:47:52,662 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\arialnb.ttf', name='Arial', style='normal', variant='normal', weight=700, stretch='condensed', size='scalable')) = 0.5349999999999999
2025-07-23 19:47:52,662 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\ANTQUABI.TTF', name='Book Antiqua', style='italic', variant='normal', weight=700, stretch='normal', size='scalable')) = 11.335
2025-07-23 19:47:52,662 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\FTLTLT.TTF', name='Footlight MT Light', style='normal', variant='normal', weight=300, stretch='normal', size='scalable')) = 10.145
2025-07-23 19:47:52,662 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\Shonarb.ttf', name='Shonar Bangla', style='normal', variant='normal', weight=700, stretch='normal', size='scalable')) = 10.335
2025-07-23 19:47:52,662 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\msyhbd.ttc', name='Microsoft YaHei', style='normal', variant='normal', weight=700, stretch='normal', size='scalable')) = 10.335
2025-07-23 19:47:52,663 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\GIL_____.TTF', name='Gill Sans MT', style='normal', variant='normal', weight=400, stretch='normal', size='scalable')) = 10.05
2025-07-23 19:47:52,663 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\FORTE.TTF', name='Forte', style='normal', variant='normal', weight=400, stretch='normal', size='scalable')) = 10.05
2025-07-23 19:47:52,663 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\cambriai.ttf', name='Cambria', style='italic', variant='normal', weight=400, stretch='normal', size='scalable')) = 11.05
2025-07-23 19:47:52,663 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\arial.ttf', name='Arial', style='normal', variant='normal', weight=400, stretch='normal', size='scalable')) = 0.05
2025-07-23 19:47:52,663 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\GillSansItNova.ttf', name='Gill Sans Nova', style='italic', variant='normal', weight=500, stretch='normal', size='scalable')) = 11.145
2025-07-23 19:47:52,664 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\kokilai.ttf', name='Kokila', style='italic', variant='normal', weight=400, stretch='normal', size='scalable')) = 11.05
2025-07-23 19:47:52,664 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\tunga.ttf', name='Tunga', style='normal', variant='normal', weight=400, stretch='normal', size='scalable')) = 10.05
2025-07-23 19:47:52,664 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\nyala.ttf', name='Nyala', style='normal', variant='normal', weight=400, stretch='normal', size='scalable')) = 10.05
2025-07-23 19:47:52,664 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\AGENCYR.TTF', name='Agency FB', style='normal', variant='normal', weight=400, stretch='normal', size='scalable')) = 10.05
2025-07-23 19:47:52,664 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\framdit.ttf', name='Franklin Gothic Medium', style='italic', variant='normal', weight=400, stretch='normal', size='scalable')) = 11.05
2025-07-23 19:47:52,664 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\bahnschrift.ttf', name='Bahnschrift', style='normal', variant='normal', weight=400, stretch='normal', size='scalable')) = 10.05
2025-07-23 19:47:52,664 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\GeorgiaPro-CondBlack.ttf', name='Georgia Pro', style='normal', variant='normal', weight=900, stretch='condensed', size='scalable')) = 10.725
2025-07-23 19:47:52,665 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\GillSansCondNova.ttf', name='Gill Sans Nova', style='normal', variant='normal', weight=500, stretch='condensed', size='scalable')) = 10.344999999999999
2025-07-23 19:47:52,665 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\GillSansNova.ttf', name='Gill Sans Nova', style='normal', variant='normal', weight=500, stretch='normal', size='scalable')) = 10.145
2025-07-23 19:47:52,665 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\msyhl.ttc', name='Microsoft YaHei', style='normal', variant='normal', weight=290, stretch='normal', size='scalable')) = 10.1545
2025-07-23 19:47:52,665 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\RockwellNova-Italic.ttf', name='Rockwell Nova', style='italic', variant='normal', weight=400, stretch='normal', size='scalable')) = 11.05
2025-07-23 19:47:52,665 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\BOD_BI.TTF', name='Bodoni MT', style='italic', variant='normal', weight=700, stretch='normal', size='scalable')) = 11.335
2025-07-23 19:47:52,665 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\COOPBL.TTF', name='Cooper Black', style='normal', variant='normal', weight=400, stretch='normal', size='scalable')) = 10.05
2025-07-23 19:47:52,665 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\CALIFI.TTF', name='Californian FB', style='italic', variant='normal', weight=400, stretch='normal', size='scalable')) = 11.05
2025-07-23 19:47:52,666 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\upclbi.ttf', name='LilyUPC', style='italic', variant='normal', weight=700, stretch='normal', size='scalable')) = 11.335
2025-07-23 19:47:52,666 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\RockwellNovaCond.ttf', name='Rockwell Nova', style='normal', variant='normal', weight=400, stretch='condensed', size='scalable')) = 10.25
2025-07-23 19:47:52,666 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\VerdanaPro-SemiBoldItalic.ttf', name='Verdana Pro', style='italic', variant='normal', weight=600, stretch='normal', size='scalable')) = 11.24
2025-07-23 19:47:52,666 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\GOUDOSI.TTF', name='Goudy Old Style', style='italic', variant='normal', weight=400, stretch='normal', size='scalable')) = 11.05
2025-07-23 19:47:52,666 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\andlso.ttf', name='Andalus', style='normal', variant='normal', weight=400, stretch='normal', size='scalable')) = 10.05
2025-07-23 19:47:52,666 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\SimsunExtG.ttf', name='SimSun-ExtG', style='normal', variant='normal', weight=400, stretch='normal', size='scalable')) = 10.05
2025-07-23 19:47:52,666 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\GILB____.TTF', name='Gill Sans MT', style='normal', variant='normal', weight=700, stretch='normal', size='scalable')) = 10.335
2025-07-23 19:47:52,667 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\kalingab.ttf', name='Kalinga', style='normal', variant='normal', weight=700, stretch='normal', size='scalable')) = 10.335
2025-07-23 19:47:52,667 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\Inkfree.ttf', name='Ink Free', style='normal', variant='normal', weight=400, stretch='normal', size='scalable')) = 10.05
2025-07-23 19:47:52,667 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\BSSYM7.TTF', name='Bookshelf Symbol 7', style='normal', variant='normal', weight=400, stretch='normal', size='scalable')) = 10.05
2025-07-23 19:47:52,667 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\INFROMAN.TTF', name='Informal Roman', style='normal', variant='normal', weight=400, stretch='normal', size='scalable')) = 10.05
2025-07-23 19:47:52,667 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\GillSansCondExtraItNova.ttf', name='Gill Sans Nova', style='italic', variant='normal', weight=800, stretch='condensed', size='scalable')) = 11.629999999999999
2025-07-23 19:47:52,667 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\BOD_CR.TTF', name='Bodoni MT', style='normal', variant='normal', weight=400, stretch='condensed', size='scalable')) = 10.25
2025-07-23 19:47:52,668 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\ITCBLKAD.TTF', name='Blackadder ITC', style='normal', variant='normal', weight=400, stretch='normal', size='scalable')) = 10.05
2025-07-23 19:47:52,668 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\ahronbd.ttf', name='Aharoni', style='normal', variant='normal', weight=700, stretch='normal', size='scalable')) = 10.335
2025-07-23 19:47:52,668 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\UDDigiKyokashoN-R.ttc', name='UD Digi Kyokasho N-R', style='normal', variant='normal', weight=400, stretch='normal', size='scalable')) = 10.05
2025-07-23 19:47:52,668 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\vijaya.ttf', name='Vijaya', style='normal', variant='normal', weight=400, stretch='normal', size='scalable')) = 10.05
2025-07-23 19:47:52,668 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\LaoUI.ttf', name='Lao UI', style='normal', variant='normal', weight=400, stretch='normal', size='scalable')) = 10.05
2025-07-23 19:47:52,668 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\GillSansUltraBoNova.ttf', name='Gill Sans Nova', style='normal', variant='normal', weight=900, stretch='normal', size='scalable')) = 10.525
2025-07-23 19:47:52,669 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\vijayab.ttf', name='Vijaya', style='normal', variant='normal', weight=700, stretch='normal', size='scalable')) = 10.335
2025-07-23 19:47:52,669 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\PRISTINA.TTF', name='Pristina', style='normal', variant='normal', weight=400, stretch='normal', size='scalable')) = 10.05
2025-07-23 19:47:52,669 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\msjh.ttc', name='Microsoft JhengHei', style='normal', variant='normal', weight=400, stretch='normal', size='scalable')) = 10.05
2025-07-23 19:47:52,669 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\mriamc.ttf', name='Miriam Fixed', style='normal', variant='normal', weight=400, stretch='normal', size='scalable')) = 10.05
2025-07-23 19:47:52,669 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\segoeuib.ttf', name='Segoe UI', style='normal', variant='normal', weight=700, stretch='normal', size='scalable')) = 10.335
2025-07-23 19:47:52,670 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\frank.ttf', name='FrankRuehl', style='normal', variant='normal', weight=400, stretch='normal', size='scalable')) = 10.05
2025-07-23 19:47:52,670 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\Dengb.ttf', name='DengXian', style='normal', variant='normal', weight=700, stretch='normal', size='scalable')) = 10.335
2025-07-23 19:47:52,670 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\LSANSDI.TTF', name='Lucida Sans', style='italic', variant='normal', weight=600, stretch='normal', size='scalable')) = 11.24
2025-07-23 19:47:52,670 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\CALISTBI.TTF', name='Calisto MT', style='italic', variant='normal', weight=700, stretch='normal', size='scalable')) = 11.335
2025-07-23 19:47:52,670 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\CALISTB.TTF', name='Calisto MT', style='normal', variant='normal', weight=700, stretch='normal', size='scalable')) = 10.335
2025-07-23 19:47:52,670 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\rod.ttf', name='Rod', style='normal', variant='normal', weight=400, stretch='normal', size='scalable')) = 10.05
2025-07-23 19:47:52,670 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\BROADW.TTF', name='Broadway', style='normal', variant='normal', weight=400, stretch='normal', size='scalable')) = 10.05
2025-07-23 19:47:52,671 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\lvnmbd.ttf', name='Levenim MT', style='normal', variant='normal', weight=700, stretch='normal', size='scalable')) = 10.335
2025-07-23 19:47:52,671 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\GILSANUB.TTF', name='Gill Sans Ultra Bold', style='normal', variant='normal', weight=400, stretch='normal', size='scalable')) = 10.05
2025-07-23 19:47:52,671 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\VerdanaPro-SemiBold.ttf', name='Verdana Pro', style='normal', variant='normal', weight=600, stretch='normal', size='scalable')) = 10.24
2025-07-23 19:47:52,671 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\GOTHICBI.TTF', name='Century Gothic', style='italic', variant='normal', weight=700, stretch='normal', size='scalable')) = 11.335
2025-07-23 19:47:52,671 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\simpo.ttf', name='Simplified Arabic', style='normal', variant='normal', weight=400, stretch='normal', size='scalable')) = 10.05
2025-07-23 19:47:52,671 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\RAVIE.TTF', name='Ravie', style='normal', variant='normal', weight=400, stretch='normal', size='scalable')) = 10.05
2025-07-23 19:47:52,672 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\majalla.ttf', name='Sakkal Majalla', style='normal', variant='normal', weight=400, stretch='normal', size='scalable')) = 10.05
2025-07-23 19:47:52,672 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\NIAGSOL.TTF', name='Niagara Solid', style='normal', variant='normal', weight=400, stretch='normal', size='scalable')) = 10.05
2025-07-23 19:47:52,672 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\CHILLER.TTF', name='Chiller', style='normal', variant='normal', weight=400, stretch='normal', size='scalable')) = 10.05
2025-07-23 19:47:52,672 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\consola.ttf', name='Consolas', style='normal', variant='normal', weight=400, stretch='normal', size='scalable')) = 10.05
2025-07-23 19:47:52,672 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\LBRITED.TTF', name='Lucida Bright', style='normal', variant='normal', weight=600, stretch='normal', size='scalable')) = 10.24
2025-07-23 19:47:52,672 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\BRLNSR.TTF', name='Berlin Sans FB', style='normal', variant='normal', weight=400, stretch='normal', size='scalable')) = 10.05
2025-07-23 19:47:52,672 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\calibrili.ttf', name='Calibri', style='italic', variant='normal', weight=300, stretch='normal', size='scalable')) = 11.145
2025-07-23 19:47:52,673 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\COPRGTB.TTF', name='Copperplate Gothic Bold', style='normal', variant='normal', weight=400, stretch='normal', size='scalable')) = 10.05
2025-07-23 19:47:52,673 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\simsun.ttc', name='SimSun', style='normal', variant='normal', weight=400, stretch='normal', size='scalable')) = 10.05
2025-07-23 19:47:52,673 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\MTCORSVA.TTF', name='Monotype Corsiva', style='normal', variant='normal', weight=400, stretch='normal', size='scalable')) = 10.05
2025-07-23 19:47:52,673 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\RockwellNovaCond-Light.ttf', name='Rockwell Nova', style='normal', variant='normal', weight=300, stretch='condensed', size='scalable')) = 10.344999999999999
2025-07-23 19:47:52,673 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\Candaral.ttf', name='Candara', style='normal', variant='normal', weight=400, stretch='normal', size='scalable')) = 10.05
2025-07-23 19:47:52,673 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\Candara.ttf', name='Candara', style='normal', variant='normal', weight=400, stretch='normal', size='scalable')) = 10.05
2025-07-23 19:47:52,674 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\seguibli.ttf', name='Segoe UI', style='italic', variant='normal', weight=900, stretch='normal', size='scalable')) = 11.525
2025-07-23 19:47:52,674 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\cambriaz.ttf', name='Cambria', style='italic', variant='normal', weight=700, stretch='normal', size='scalable')) = 11.335
2025-07-23 19:47:52,674 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\upcjl.ttf', name='JasmineUPC', style='normal', variant='normal', weight=400, stretch='normal', size='scalable')) = 10.05
2025-07-23 19:47:52,674 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\BOOKOSB.TTF', name='Bookman Old Style', style='normal', variant='normal', weight=600, stretch='normal', size='scalable')) = 10.24
2025-07-23 19:47:52,674 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\JUICE___.TTF', name='Juice ITC', style='normal', variant='normal', weight=400, stretch='normal', size='scalable')) = 10.05
2025-07-23 19:47:52,674 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\NotoSansHK-VF.ttf', name='Noto Sans HK', style='normal', variant='normal', weight=100, stretch='normal', size='scalable')) = 10.335
2025-07-23 19:47:52,674 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\Shonar.ttf', name='Shonar Bangla', style='normal', variant='normal', weight=400, stretch='normal', size='scalable')) = 10.05
2025-07-23 19:47:52,676 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\nrkis.ttf', name='Narkisim', style='normal', variant='normal', weight=400, stretch='normal', size='scalable')) = 10.05
2025-07-23 19:47:52,676 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\mingliub.ttc', name='MingLiU-ExtB', style='normal', variant='normal', weight=400, stretch='normal', size='scalable')) = 10.05
2025-07-23 19:47:52,676 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\GLSNECB.TTF', name='Gill Sans MT Ext Condensed Bold', style='normal', variant='normal', weight=400, stretch='condensed', size='scalable')) = 10.25
2025-07-23 19:47:52,676 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\BOOKOSI.TTF', name='Bookman Old Style', style='italic', variant='normal', weight=300, stretch='normal', size='scalable')) = 11.145
2025-07-23 19:47:52,676 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\HARLOWSI.TTF', name='Harlow Solid Italic', style='italic', variant='normal', weight=400, stretch='normal', size='scalable')) = 11.05
2025-07-23 19:47:52,676 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\VerdanaPro-CondBoldItalic.ttf', name='Verdana Pro', style='italic', variant='normal', weight=700, stretch='condensed', size='scalable')) = 11.535
2025-07-23 19:47:52,677 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\GillSansBoItNova.ttf', name='Gill Sans Nova', style='italic', variant='normal', weight=700, stretch='normal', size='scalable')) = 11.335
2025-07-23 19:47:52,677 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\l_10646.ttf', name='Lucida Sans Unicode', style='normal', variant='normal', weight=400, stretch='normal', size='scalable')) = 10.05
2025-07-23 19:47:52,677 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\gishabd.ttf', name='Gisha', style='normal', variant='normal', weight=700, stretch='normal', size='scalable')) = 10.335
2025-07-23 19:47:52,677 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\BOD_B.TTF', name='Bodoni MT', style='normal', variant='normal', weight=700, stretch='normal', size='scalable')) = 10.335
2025-07-23 19:47:52,677 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\utsaah.ttf', name='Utsaah', style='normal', variant='normal', weight=400, stretch='normal', size='scalable')) = 10.05
2025-07-23 19:47:52,677 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\utsaahb.ttf', name='Utsaah', style='normal', variant='normal', weight=700, stretch='normal', size='scalable')) = 10.335
2025-07-23 19:47:52,677 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\TCCEB.TTF', name='Tw Cen MT Condensed Extra Bold', style='normal', variant='normal', weight=400, stretch='condensed', size='scalable')) = 10.25
2025-07-23 19:47:52,678 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\GeorgiaPro-CondLightItalic.ttf', name='Georgia Pro', style='italic', variant='normal', weight=300, stretch='condensed', size='scalable')) = 11.344999999999999
2025-07-23 19:47:52,678 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\verdanai.ttf', name='Verdana', style='italic', variant='normal', weight=400, stretch='normal', size='scalable')) = 11.05
2025-07-23 19:47:52,678 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\ntailu.ttf', name='Microsoft New Tai Lue', style='normal', variant='normal', weight=400, stretch='normal', size='scalable')) = 10.05
2025-07-23 19:47:52,678 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\SitkaZ.ttc', name='Sitka Small', style='italic', variant='normal', weight=700, stretch='normal', size='scalable')) = 11.335
2025-07-23 19:47:52,678 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\LATINWD.TTF', name='Wide Latin', style='normal', variant='normal', weight=400, stretch='expanded', size='scalable')) = 10.25
2025-07-23 19:47:52,678 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\segoeprb.ttf', name='Segoe Print', style='normal', variant='normal', weight=700, stretch='normal', size='scalable')) = 10.335
2025-07-23 19:47:52,678 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\segoeui.ttf', name='Segoe UI', style='normal', variant='normal', weight=400, stretch='normal', size='scalable')) = 10.05
2025-07-23 19:47:52,679 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\COLONNA.TTF', name='Colonna MT', style='normal', variant='normal', weight=400, stretch='normal', size='scalable')) = 10.05
2025-07-23 19:47:52,679 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\Gabriola.ttf', name='Gabriola', style='normal', variant='normal', weight=400, stretch='normal', size='scalable')) = 10.05
2025-07-23 19:47:52,679 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\UDDigiKyokashoN-B.ttc', name='UD Digi Kyokasho N-B', style='normal', variant='normal', weight=700, stretch='normal', size='scalable')) = 10.335
2025-07-23 19:47:52,679 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\phagspa.ttf', name='Microsoft PhagsPa', style='normal', variant='normal', weight=400, stretch='normal', size='scalable')) = 10.05
2025-07-23 19:47:52,679 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\FRSCRIPT.TTF', name='French Script MT', style='normal', variant='normal', weight=400, stretch='normal', size='scalable')) = 10.05
2025-07-23 19:47:52,679 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\NotoSansJP-VF.ttf', name='Noto Sans JP', style='normal', variant='normal', weight=100, stretch='normal', size='scalable')) = 10.335
2025-07-23 19:47:52,680 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\GeorgiaPro-CondSemiBoldItalic.ttf', name='Georgia Pro', style='italic', variant='normal', weight=600, stretch='condensed', size='scalable')) = 11.44
2025-07-23 19:47:52,680 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\ArialNova-Light.ttf', name='Arial Nova', style='normal', variant='normal', weight=300, stretch='normal', size='scalable')) = 10.145
2025-07-23 19:47:52,680 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\davidbd.ttf', name='David', style='normal', variant='normal', weight=700, stretch='normal', size='scalable')) = 10.335
2025-07-23 19:47:52,680 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\GeorgiaPro-CondLight.ttf', name='Georgia Pro', style='normal', variant='normal', weight=300, stretch='condensed', size='scalable')) = 10.344999999999999
2025-07-23 19:47:52,680 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\GOTHICI.TTF', name='Century Gothic', style='italic', variant='normal', weight=400, stretch='normal', size='scalable')) = 11.05
2025-07-23 19:47:52,680 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\VLADIMIR.TTF', name='Vladimir Script', style='normal', variant='normal', weight=400, stretch='normal', size='scalable')) = 10.05
2025-07-23 19:47:52,680 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\upcjbi.ttf', name='JasmineUPC', style='italic', variant='normal', weight=700, stretch='normal', size='scalable')) = 11.335
2025-07-23 19:47:52,681 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\HPSimplified_It.ttf', name='HP Simplified', style='italic', variant='normal', weight=400, stretch='normal', size='scalable')) = 11.05
2025-07-23 19:47:52,681 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\GeorgiaPro-Black.ttf', name='Georgia Pro', style='normal', variant='normal', weight=900, stretch='normal', size='scalable')) = 10.525
2025-07-23 19:47:52,681 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\ERASMD.TTF', name='Eras Medium ITC', style='normal', variant='normal', weight=400, stretch='normal', size='scalable')) = 10.05
2025-07-23 19:47:52,681 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\VerdanaPro-LightItalic.ttf', name='Verdana Pro', style='italic', variant='normal', weight=300, stretch='normal', size='scalable')) = 11.145
2025-07-23 19:47:52,681 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\yumindb.ttf', name='Yu Mincho', style='normal', variant='normal', weight=600, stretch='normal', size='scalable')) = 10.24
2025-07-23 19:47:52,681 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\BAUHS93.TTF', name='Bauhaus 93', style='normal', variant='normal', weight=400, stretch='normal', size='scalable')) = 10.05
2025-07-23 19:47:52,681 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\mmrtextb.ttf', name='Myanmar Text', style='normal', variant='normal', weight=700, stretch='normal', size='scalable')) = 10.335
2025-07-23 19:47:52,682 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\OCRAEXT.TTF', name='OCR A Extended', style='normal', variant='normal', weight=400, stretch='expanded', size='scalable')) = 10.25
2025-07-23 19:47:52,682 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\kalinga.ttf', name='Kalinga', style='normal', variant='normal', weight=400, stretch='normal', size='scalable')) = 10.05
2025-07-23 19:47:52,682 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\MISTRAL.TTF', name='Mistral', style='normal', variant='normal', weight=400, stretch='normal', size='scalable')) = 10.05
2025-07-23 19:47:52,682 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\simfang.ttf', name='FangSong', style='normal', variant='normal', weight=400, stretch='normal', size='scalable')) = 10.05
2025-07-23 19:47:52,682 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\VerdanaPro-Bold.ttf', name='Verdana Pro', style='normal', variant='normal', weight=700, stretch='normal', size='scalable')) = 10.335
2025-07-23 19:47:52,682 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\upcki.ttf', name='KodchiangUPC', style='italic', variant='normal', weight=400, stretch='normal', size='scalable')) = 11.05
2025-07-23 19:47:52,683 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\ntailub.ttf', name='Microsoft New Tai Lue', style='normal', variant='normal', weight=700, stretch='normal', size='scalable')) = 10.335
2025-07-23 19:47:52,683 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\ALGER.TTF', name='Algerian', style='normal', variant='normal', weight=400, stretch='normal', size='scalable')) = 10.05
2025-07-23 19:47:52,683 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\RockwellNova-LightItalic.ttf', name='Rockwell Nova', style='italic', variant='normal', weight=300, stretch='normal', size='scalable')) = 11.145
2025-07-23 19:47:52,683 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\upcdi.ttf', name='DilleniaUPC', style='italic', variant='normal', weight=400, stretch='normal', size='scalable')) = 11.05
2025-07-23 19:47:52,683 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\GillSansCondExtraNova.ttf', name='Gill Sans Nova', style='normal', variant='normal', weight=800, stretch='condensed', size='scalable')) = 10.629999999999999
2025-07-23 19:47:52,683 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\ARIALUNI.TTF', name='Arial Unicode MS', style='normal', variant='normal', weight=400, stretch='normal', size='scalable')) = 10.05
2025-07-23 19:47:52,683 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\palai.ttf', name='Palatino Linotype', style='italic', variant='normal', weight=400, stretch='normal', size='scalable')) = 11.05
2025-07-23 19:47:52,684 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\ROCC____.TTF', name='Rockwell Condensed', style='normal', variant='normal', weight=400, stretch='condensed', size='scalable')) = 10.25
2025-07-23 19:47:52,684 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\GeorgiaPro-CondRegular.ttf', name='Georgia Pro', style='normal', variant='normal', weight=400, stretch='condensed', size='scalable')) = 10.25
2025-07-23 19:47:52,684 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\LFAX.TTF', name='Lucida Fax', style='normal', variant='normal', weight=400, stretch='normal', size='scalable')) = 10.05
2025-07-23 19:47:52,684 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\TCCB____.TTF', name='Tw Cen MT Condensed', style='normal', variant='normal', weight=700, stretch='condensed', size='scalable')) = 10.535
2025-07-23 19:47:52,684 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\tungab.ttf', name='Tunga', style='normal', variant='normal', weight=700, stretch='normal', size='scalable')) = 10.335
2025-07-23 19:47:52,685 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\FRAMDCN.TTF', name='Franklin Gothic Medium Cond', style='normal', variant='normal', weight=400, stretch='condensed', size='scalable')) = 10.25
2025-07-23 19:47:52,685 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\kartika.ttf', name='Kartika', style='normal', variant='normal', weight=400, stretch='normal', size='scalable')) = 10.05
2025-07-23 19:47:52,685 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\PAPYRUS.TTF', name='Papyrus', style='normal', variant='normal', weight=400, stretch='normal', size='scalable')) = 10.05
2025-07-23 19:47:52,685 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\LFAXI.TTF', name='Lucida Fax', style='italic', variant='normal', weight=400, stretch='normal', size='scalable')) = 11.05
2025-07-23 19:47:52,685 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\BERNHC.TTF', name='Bernard MT Condensed', style='normal', variant='normal', weight=400, stretch='condensed', size='scalable')) = 10.25
2025-07-23 19:47:52,686 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\GeorgiaPro-SemiBold.ttf', name='Georgia Pro', style='normal', variant='normal', weight=600, stretch='normal', size='scalable')) = 10.24
2025-07-23 19:47:52,686 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\simsunb.ttf', name='SimSun-ExtB', style='normal', variant='normal', weight=400, stretch='normal', size='scalable')) = 10.05
2025-07-23 19:47:52,686 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\cambriab.ttf', name='Cambria', style='normal', variant='normal', weight=700, stretch='normal', size='scalable')) = 10.335
2025-07-23 19:47:52,686 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\ArialNovaCond-Light.ttf', name='Arial Nova', style='normal', variant='normal', weight=300, stretch='condensed', size='scalable')) = 10.344999999999999
2025-07-23 19:47:52,686 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\ArialNova-BoldItalic.ttf', name='Arial Nova', style='italic', variant='normal', weight=700, stretch='normal', size='scalable')) = 11.335
2025-07-23 19:47:52,686 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\yuminl.ttf', name='Yu Mincho', style='normal', variant='normal', weight=300, stretch='normal', size='scalable')) = 10.145
2025-07-23 19:47:52,686 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\BRLNSDB.TTF', name='Berlin Sans FB Demi', style='normal', variant='normal', weight=700, stretch='normal', size='scalable')) = 10.335
2025-07-23 19:47:52,687 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\taile.ttf', name='Microsoft Tai Le', style='normal', variant='normal', weight=400, stretch='normal', size='scalable')) = 10.05
2025-07-23 19:47:52,687 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\courbd.ttf', name='Courier New', style='normal', variant='normal', weight=700, stretch='normal', size='scalable')) = 10.335
2025-07-23 19:47:52,687 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\gadugib.ttf', name='Gadugi', style='normal', variant='normal', weight=700, stretch='normal', size='scalable')) = 10.335
2025-07-23 19:47:52,687 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\mvboli.ttf', name='MV Boli', style='normal', variant='normal', weight=400, stretch='normal', size='scalable')) = 10.05
2025-07-23 19:47:52,687 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\upcfi.ttf', name='FreesiaUPC', style='italic', variant='normal', weight=400, stretch='normal', size='scalable')) = 11.05
2025-07-23 19:47:52,687 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\VerdanaPro-CondBlackItalic.ttf', name='Verdana Pro', style='italic', variant='normal', weight=900, stretch='condensed', size='scalable')) = 11.725
2025-07-23 19:47:52,688 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\consolaz.ttf', name='Consolas', style='italic', variant='normal', weight=700, stretch='normal', size='scalable')) = 11.335
2025-07-23 19:47:52,689 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\GeorgiaPro-Italic.ttf', name='Georgia Pro', style='italic', variant='normal', weight=400, stretch='normal', size='scalable')) = 11.05
2025-07-23 19:47:52,689 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\PALSCRI.TTF', name='Palace Script MT', style='normal', variant='normal', weight=400, stretch='normal', size='scalable')) = 10.05
2025-07-23 19:47:52,689 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\PERTIBD.TTF', name='Perpetua Titling MT', style='normal', variant='normal', weight=700, stretch='normal', size='scalable')) = 10.335
2025-07-23 19:47:52,689 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\segoepr.ttf', name='Segoe Print', style='normal', variant='normal', weight=400, stretch='normal', size='scalable')) = 10.05
2025-07-23 19:47:52,689 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\ROCK.TTF', name='Rockwell', style='normal', variant='normal', weight=400, stretch='normal', size='scalable')) = 10.05
2025-07-23 19:47:52,689 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\GILI____.TTF', name='Gill Sans MT', style='italic', variant='normal', weight=400, stretch='normal', size='scalable')) = 11.05
2025-07-23 19:47:52,690 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\upcib.ttf', name='IrisUPC', style='normal', variant='normal', weight=700, stretch='normal', size='scalable')) = 10.335
2025-07-23 19:47:52,690 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\GillSansCondBoItNova.ttf', name='Gill Sans Nova', style='italic', variant='normal', weight=700, stretch='condensed', size='scalable')) = 11.535
2025-07-23 19:47:52,690 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\upcli.ttf', name='LilyUPC', style='italic', variant='normal', weight=400, stretch='normal', size='scalable')) = 11.05
2025-07-23 19:47:52,690 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\NirmalaB.ttf', name='Nirmala UI', style='normal', variant='normal', weight=700, stretch='normal', size='scalable')) = 10.335
2025-07-23 19:47:52,690 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\PARCHM.TTF', name='Parchment', style='normal', variant='normal', weight=400, stretch='normal', size='scalable')) = 10.05
2025-07-23 19:47:52,690 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\FRADMIT.TTF', name='Franklin Gothic Demi', style='italic', variant='normal', weight=400, stretch='normal', size='scalable')) = 11.05
2025-07-23 19:47:52,690 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\arabtype.ttf', name='Arabic Typesetting', style='normal', variant='normal', weight=400, stretch='normal', size='scalable')) = 10.05
2025-07-23 19:47:52,690 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\TEMPSITC.TTF', name='Tempus Sans ITC', style='normal', variant='normal', weight=400, stretch='normal', size='scalable')) = 10.05
2025-07-23 19:47:52,690 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\cordia.ttc', name='Cordia New', style='normal', variant='normal', weight=400, stretch='normal', size='scalable')) = 10.05
2025-07-23 19:47:52,691 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\ITCEDSCR.TTF', name='Edwardian Script ITC', style='normal', variant='normal', weight=400, stretch='normal', size='scalable')) = 10.05
2025-07-23 19:47:52,691 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\MAGNETOB.TTF', name='Magneto', style='normal', variant='normal', weight=700, stretch='normal', size='scalable')) = 10.335
2025-07-23 19:47:52,691 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\seguibl.ttf', name='Segoe UI', style='normal', variant='normal', weight=900, stretch='normal', size='scalable')) = 10.525
2025-07-23 19:47:52,691 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\YuGothB.ttc', name='Yu Gothic', style='normal', variant='normal', weight=700, stretch='normal', size='scalable')) = 10.335
2025-07-23 19:47:52,691 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\VerdanaPro-Light.ttf', name='Verdana Pro', style='normal', variant='normal', weight=300, stretch='normal', size='scalable')) = 10.145
2025-07-23 19:47:52,691 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\NotoSansSC-VF.ttf', name='Noto Sans SC', style='normal', variant='normal', weight=100, stretch='normal', size='scalable')) = 10.335
2025-07-23 19:47:52,691 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\micross.ttf', name='Microsoft Sans Serif', style='normal', variant='normal', weight=400, stretch='normal', size='scalable')) = 10.05
2025-07-23 19:47:52,692 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\FRADMCN.TTF', name='Franklin Gothic Demi Cond', style='normal', variant='normal', weight=400, stretch='condensed', size='scalable')) = 10.25
2025-07-23 19:47:52,692 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\upcel.ttf', name='EucrosiaUPC', style='normal', variant='normal', weight=400, stretch='normal', size='scalable')) = 10.05
2025-07-23 19:47:52,692 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\BOD_BLAI.TTF', name='Bodoni MT', style='italic', variant='normal', weight=900, stretch='normal', size='scalable')) = 11.525
2025-07-23 19:47:52,692 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\CALIFR.TTF', name='Californian FB', style='normal', variant='normal', weight=400, stretch='normal', size='scalable')) = 10.05
2025-07-23 19:47:52,692 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\VIVALDII.TTF', name='Vivaldi', style='italic', variant='normal', weight=400, stretch='normal', size='scalable')) = 11.05
2025-07-23 19:47:52,692 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\GARABD.TTF', name='Garamond', style='normal', variant='normal', weight=700, stretch='normal', size='scalable')) = 10.335
2025-07-23 19:47:52,693 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\upckb.ttf', name='KodchiangUPC', style='normal', variant='normal', weight=700, stretch='normal', size='scalable')) = 10.335
2025-07-23 19:47:52,693 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\FRABK.TTF', name='Franklin Gothic Book', style='normal', variant='normal', weight=400, stretch='normal', size='scalable')) = 10.05
2025-07-23 19:47:52,693 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\georgiai.ttf', name='Georgia', style='italic', variant='normal', weight=400, stretch='normal', size='scalable')) = 11.05
2025-07-23 19:47:52,693 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\COPRGTL.TTF', name='Copperplate Gothic Light', style='normal', variant='normal', weight=400, stretch='normal', size='scalable')) = 10.05
2025-07-23 19:47:52,694 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\VerdanaPro-BlackItalic.ttf', name='Verdana Pro', style='italic', variant='normal', weight=900, stretch='normal', size='scalable')) = 11.525
2025-07-23 19:47:52,694 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\upcji.ttf', name='JasmineUPC', style='italic', variant='normal', weight=400, stretch='normal', size='scalable')) = 11.05
2025-07-23 19:47:52,694 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\GillSansCondItNova.ttf', name='Gill Sans Nova', style='italic', variant='normal', weight=500, stretch='condensed', size='scalable')) = 11.344999999999999
2025-07-23 19:47:52,694 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\GeorgiaPro-Bold.ttf', name='Georgia Pro', style='normal', variant='normal', weight=700, stretch='normal', size='scalable')) = 10.335
2025-07-23 19:47:52,694 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\BASKVILL.TTF', name='Baskerville Old Face', style='normal', variant='normal', weight=400, stretch='normal', size='scalable')) = 10.05
2025-07-23 19:47:52,694 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\segoeuil.ttf', name='Segoe UI', style='normal', variant='normal', weight=300, stretch='normal', size='scalable')) = 10.145
2025-07-23 19:47:52,695 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\GIGI.TTF', name='Gigi', style='normal', variant='normal', weight=400, stretch='normal', size='scalable')) = 10.05
2025-07-23 19:47:52,695 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\IMPRISHA.TTF', name='Imprint MT Shadow', style='normal', variant='normal', weight=400, stretch='normal', size='scalable')) = 10.05
2025-07-23 19:47:52,695 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\GLECB.TTF', name='Gloucester MT Extra Condensed', style='normal', variant='normal', weight=400, stretch='condensed', size='scalable')) = 10.25
2025-07-23 19:47:52,695 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\KUNSTLER.TTF', name='Kunstler Script', style='normal', variant='normal', weight=400, stretch='normal', size='scalable')) = 10.05
2025-07-23 19:47:52,695 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\GillSansCondUltraBoNova.ttf', name='Gill Sans Nova', style='normal', variant='normal', weight=900, stretch='condensed', size='scalable')) = 10.725
2025-07-23 19:47:52,695 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\CENSCBK.TTF', name='Century Schoolbook', style='normal', variant='normal', weight=400, stretch='normal', size='scalable')) = 10.05
2025-07-23 19:47:52,695 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\MOD20.TTF', name='Modern No. 20', style='normal', variant='normal', weight=400, stretch='normal', size='scalable')) = 10.05
2025-07-23 19:47:52,696 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\VerdanaPro-BoldItalic.ttf', name='Verdana Pro', style='italic', variant='normal', weight=700, stretch='normal', size='scalable')) = 11.335
2025-07-23 19:47:52,696 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\LBRITEDI.TTF', name='Lucida Bright', style='italic', variant='normal', weight=600, stretch='normal', size='scalable')) = 11.24
2025-07-23 19:47:52,696 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\POORICH.TTF', name='Poor Richard', style='normal', variant='normal', weight=400, stretch='normal', size='scalable')) = 10.05
2025-07-23 19:47:52,696 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\UrdType.ttf', name='Urdu Typesetting', style='normal', variant='normal', weight=400, stretch='normal', size='scalable')) = 10.05
2025-07-23 19:47:52,696 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\aparajbi.ttf', name='Aparajita', style='italic', variant='normal', weight=700, stretch='normal', size='scalable')) = 11.335
2025-07-23 19:47:52,696 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\ROCKEB.TTF', name='Rockwell Extra Bold', style='normal', variant='normal', weight=800, stretch='normal', size='scalable')) = 10.43
2025-07-23 19:47:52,696 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\euphemia.ttf', name='Euphemia', style='normal', variant='normal', weight=400, stretch='normal', size='scalable')) = 10.05
2025-07-23 19:47:52,697 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\RockwellNovaCond-BoldItalic.ttf', name='Rockwell Nova', style='italic', variant='normal', weight=700, stretch='condensed', size='scalable')) = 11.535
2025-07-23 19:47:52,697 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\TCB_____.TTF', name='Tw Cen MT', style='normal', variant='normal', weight=700, stretch='normal', size='scalable')) = 10.335
2025-07-23 19:47:52,697 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\aparaji.ttf', name='Aparajita', style='italic', variant='normal', weight=400, stretch='normal', size='scalable')) = 11.05
2025-07-23 19:47:52,697 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\upcfl.ttf', name='FreesiaUPC', style='normal', variant='normal', weight=400, stretch='normal', size='scalable')) = 10.05
2025-07-23 19:47:52,698 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\upceb.ttf', name='EucrosiaUPC', style='normal', variant='normal', weight=700, stretch='normal', size='scalable')) = 10.335
2025-07-23 19:47:52,698 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\calibri.ttf', name='Calibri', style='normal', variant='normal', weight=400, stretch='normal', size='scalable')) = 10.05
2025-07-23 19:47:52,698 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\seguisli.ttf', name='Segoe UI', style='italic', variant='normal', weight=350, stretch='normal', size='scalable')) = 11.0975
2025-07-23 19:47:52,698 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\TCM_____.TTF', name='Tw Cen MT', style='normal', variant='normal', weight=400, stretch='normal', size='scalable')) = 10.05
2025-07-23 19:47:52,698 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\corbelz.ttf', name='Corbel', style='italic', variant='normal', weight=700, stretch='normal', size='scalable')) = 11.335
2025-07-23 19:47:52,698 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\ariblk.ttf', name='Arial', style='normal', variant='normal', weight=900, stretch='normal', size='scalable')) = 0.525
2025-07-23 19:47:52,698 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\mmrtext.ttf', name='Myanmar Text', style='normal', variant='normal', weight=400, stretch='normal', size='scalable')) = 10.05
2025-07-23 19:47:52,699 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\georgia.ttf', name='Georgia', style='normal', variant='normal', weight=400, stretch='normal', size='scalable')) = 10.05
2025-07-23 19:47:52,699 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\msjhl.ttc', name='Microsoft JhengHei', style='normal', variant='normal', weight=290, stretch='normal', size='scalable')) = 10.1545
2025-07-23 19:47:52,699 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\tradbdo.ttf', name='Traditional Arabic', style='normal', variant='normal', weight=700, stretch='normal', size='scalable')) = 10.335
2025-07-23 19:47:52,699 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\ArialNovaCond-Italic.ttf', name='Arial Nova', style='italic', variant='normal', weight=400, stretch='condensed', size='scalable')) = 11.25
2025-07-23 19:47:52,699 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\msjhbd.ttc', name='Microsoft JhengHei', style='normal', variant='normal', weight=700, stretch='normal', size='scalable')) = 10.335
2025-07-23 19:47:52,699 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\FRAHVIT.TTF', name='Franklin Gothic Heavy', style='italic', variant='normal', weight=400, stretch='normal', size='scalable')) = 11.05
2025-07-23 19:47:52,699 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\georgiab.ttf', name='Georgia', style='normal', variant='normal', weight=700, stretch='normal', size='scalable')) = 10.335
2025-07-23 19:47:52,700 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\daunpenh.ttf', name='DaunPenh', style='normal', variant='normal', weight=400, stretch='normal', size='scalable')) = 10.05
2025-07-23 19:47:52,700 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\HTOWERT.TTF', name='High Tower Text', style='normal', variant='normal', weight=400, stretch='normal', size='scalable')) = 10.05
2025-07-23 19:47:52,700 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\david.ttf', name='David', style='normal', variant='normal', weight=400, stretch='normal', size='scalable')) = 10.05
2025-07-23 19:47:52,700 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\NotoSansKR-VF.ttf', name='Noto Sans KR', style='normal', variant='normal', weight=100, stretch='normal', size='scalable')) = 10.335
2025-07-23 19:47:52,700 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\ANTQUAI.TTF', name='Book Antiqua', style='italic', variant='normal', weight=400, stretch='normal', size='scalable')) = 11.05
2025-07-23 19:47:52,701 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\holomdl2.ttf', name='HoloLens MDL2 Assets', style='normal', variant='normal', weight=400, stretch='normal', size='scalable')) = 10.05
2025-07-23 19:47:52,701 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\corbeli.ttf', name='Corbel', style='italic', variant='normal', weight=400, stretch='normal', size='scalable')) = 11.05
2025-07-23 19:47:52,705 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\aparajb.ttf', name='Aparajita', style='normal', variant='normal', weight=700, stretch='normal', size='scalable')) = 10.335
2025-07-23 19:47:52,705 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\constanb.ttf', name='Constantia', style='normal', variant='normal', weight=700, stretch='normal', size='scalable')) = 10.335
2025-07-23 19:47:52,705 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\GeorgiaPro-LightItalic.ttf', name='Georgia Pro', style='italic', variant='normal', weight=300, stretch='normal', size='scalable')) = 11.145
2025-07-23 19:47:52,705 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\plantc.ttf', name='Plantagenet Cherokee', style='normal', variant='normal', weight=400, stretch='normal', size='scalable')) = 10.05
2025-07-23 19:47:52,707 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\HARNGTON.TTF', name='Harrington', style='normal', variant='normal', weight=400, stretch='normal', size='scalable')) = 10.05
2025-07-23 19:47:52,707 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\himalaya.ttf', name='Microsoft Himalaya', style='normal', variant='normal', weight=400, stretch='normal', size='scalable')) = 10.05
2025-07-23 19:47:52,707 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\TCBI____.TTF', name='Tw Cen MT', style='italic', variant='normal', weight=700, stretch='normal', size='scalable')) = 11.335
2025-07-23 19:47:52,707 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\leelawdb.ttf', name='Leelawadee', style='normal', variant='normal', weight=700, stretch='normal', size='scalable')) = 10.335
2025-07-23 19:47:52,708 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\ariali.ttf', name='Arial', style='italic', variant='normal', weight=400, stretch='normal', size='scalable')) = 1.05
2025-07-23 19:47:52,721 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\LSANS.TTF', name='Lucida Sans', style='normal', variant='normal', weight=400, stretch='normal', size='scalable')) = 10.05
2025-07-23 19:47:52,722 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\iskpota.ttf', name='Iskoola Pota', style='normal', variant='normal', weight=400, stretch='normal', size='scalable')) = 10.05
2025-07-23 19:47:52,722 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\raavi.ttf', name='Raavi', style='normal', variant='normal', weight=400, stretch='normal', size='scalable')) = 10.05
2025-07-23 19:47:52,722 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\ArialNovaCond-LightItalic.ttf', name='Arial Nova', style='italic', variant='normal', weight=300, stretch='condensed', size='scalable')) = 11.344999999999999
2025-07-23 19:47:52,722 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\PLAYBILL.TTF', name='Playbill', style='normal', variant='normal', weight=400, stretch='normal', size='scalable')) = 10.05
2025-07-23 19:47:52,722 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\vrindab.ttf', name='Vrinda', style='normal', variant='normal', weight=700, stretch='normal', size='scalable')) = 10.335
2025-07-23 19:47:52,722 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\LSANSD.TTF', name='Lucida Sans', style='normal', variant='normal', weight=600, stretch='normal', size='scalable')) = 10.24
2025-07-23 19:47:52,723 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\seguili.ttf', name='Segoe UI', style='italic', variant='normal', weight=300, stretch='normal', size='scalable')) = 11.145
2025-07-23 19:47:52,723 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\estre.ttf', name='Estrangelo Edessa', style='normal', variant='normal', weight=400, stretch='normal', size='scalable')) = 10.05
2025-07-23 19:47:52,723 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\upckl.ttf', name='KodchiangUPC', style='normal', variant='normal', weight=400, stretch='normal', size='scalable')) = 10.05
2025-07-23 19:47:52,723 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\arialbd.ttf', name='Arial', style='normal', variant='normal', weight=700, stretch='normal', size='scalable')) = 0.33499999999999996
2025-07-23 19:47:52,723 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\verdanab.ttf', name='Verdana', style='normal', variant='normal', weight=700, stretch='normal', size='scalable')) = 10.335
2025-07-23 19:47:52,723 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\Dengl.ttf', name='DengXian', style='normal', variant='normal', weight=300, stretch='normal', size='scalable')) = 10.145
2025-07-23 19:47:52,723 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\KhmerUIb.ttf', name='Khmer UI', style='normal', variant='normal', weight=700, stretch='normal', size='scalable')) = 10.335
2025-07-23 19:47:52,724 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\ArialNovaCond-BoldItalic.ttf', name='Arial Nova', style='italic', variant='normal', weight=700, stretch='condensed', size='scalable')) = 11.535
2025-07-23 19:47:52,724 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\meiryob.ttc', name='Meiryo', style='normal', variant='normal', weight=700, stretch='normal', size='scalable')) = 10.335
2025-07-23 19:47:52,724 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\BRLNSB.TTF', name='Berlin Sans FB', style='normal', variant='normal', weight=700, stretch='normal', size='scalable')) = 10.335
2025-07-23 19:47:52,725 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\verdanaz.ttf', name='Verdana', style='italic', variant='normal', weight=700, stretch='normal', size='scalable')) = 11.335
2025-07-23 19:47:52,726 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\GOUDOS.TTF', name='Goudy Old Style', style='normal', variant='normal', weight=400, stretch='normal', size='scalable')) = 10.05
2025-07-23 19:47:52,726 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\upcdbi.ttf', name='DilleniaUPC', style='italic', variant='normal', weight=700, stretch='normal', size='scalable')) = 11.335
2025-07-23 19:47:52,726 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\SitkaB.ttc', name='Sitka Small', style='normal', variant='normal', weight=700, stretch='normal', size='scalable')) = 10.335
2025-07-23 19:47:52,727 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\ARLRDBD.TTF', name='Arial Rounded MT Bold', style='normal', variant='normal', weight=400, stretch='normal', size='scalable')) = 10.05
2025-07-23 19:47:52,727 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\kartikab.ttf', name='Kartika', style='normal', variant='normal', weight=700, stretch='normal', size='scalable')) = 10.335
2025-07-23 19:47:52,727 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\BELLB.TTF', name='Bell MT', style='normal', variant='normal', weight=700, stretch='normal', size='scalable')) = 10.335
2025-07-23 19:47:52,727 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\wingding.ttf', name='Wingdings', style='normal', variant='normal', weight=400, stretch='normal', size='scalable')) = 10.05
2025-07-23 19:47:52,728 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\mangal.ttf', name='Mangal', style='normal', variant='normal', weight=400, stretch='normal', size='scalable')) = 10.05
2025-07-23 19:47:52,728 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\WINGDNG3.TTF', name='Wingdings 3', style='normal', variant='normal', weight=400, stretch='normal', size='scalable')) = 10.05
2025-07-23 19:47:52,729 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\RockwellNovaCond-LightItalic.ttf', name='Rockwell Nova', style='italic', variant='normal', weight=300, stretch='condensed', size='scalable')) = 11.344999999999999
2025-07-23 19:47:52,729 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\HPSimplified_LtIt.ttf', name='HP Simplified', style='italic', variant='normal', weight=300, stretch='normal', size='scalable')) = 11.145
2025-07-23 19:47:52,729 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\VerdanaPro-CondLight.ttf', name='Verdana Pro', style='normal', variant='normal', weight=300, stretch='condensed', size='scalable')) = 10.344999999999999
2025-07-23 19:47:52,729 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\cambria.ttc', name='Cambria', style='normal', variant='normal', weight=400, stretch='normal', size='scalable')) = 10.05
2025-07-23 19:47:52,729 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\Candarai.ttf', name='Candara', style='italic', variant='normal', weight=400, stretch='normal', size='scalable')) = 11.05
2025-07-23 19:47:52,730 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\CALIST.TTF', name='Calisto MT', style='normal', variant='normal', weight=400, stretch='normal', size='scalable')) = 10.05
2025-07-23 19:47:52,730 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\ebrimabd.ttf', name='Ebrima', style='normal', variant='normal', weight=700, stretch='normal', size='scalable')) = 10.335
2025-07-23 19:47:52,730 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\CALIFB.TTF', name='Californian FB', style='normal', variant='normal', weight=700, stretch='normal', size='scalable')) = 10.335
2025-07-23 19:47:52,730 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\framd.ttf', name='Franklin Gothic Medium', style='normal', variant='normal', weight=400, stretch='normal', size='scalable')) = 10.05
2025-07-23 19:47:52,730 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\constani.ttf', name='Constantia', style='italic', variant='normal', weight=400, stretch='normal', size='scalable')) = 11.05
2025-07-23 19:47:52,730 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\LTYPE.TTF', name='Lucida Sans Typewriter', style='normal', variant='normal', weight=400, stretch='normal', size='scalable')) = 10.05
2025-07-23 19:47:52,731 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\LFAXD.TTF', name='Lucida Fax', style='normal', variant='normal', weight=600, stretch='normal', size='scalable')) = 10.24
2025-07-23 19:47:52,731 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\symbol.ttf', name='Symbol', style='normal', variant='normal', weight=400, stretch='normal', size='scalable')) = 10.05
2025-07-23 19:47:52,731 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\BIZ-UDGothicB.ttc', name='BIZ UDGothic', style='normal', variant='normal', weight=700, stretch='normal', size='scalable')) = 10.335
2025-07-23 19:47:52,731 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\VerdanaPro-Italic.ttf', name='Verdana Pro', style='italic', variant='normal', weight=400, stretch='normal', size='scalable')) = 11.05
2025-07-23 19:47:52,731 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\corbell.ttf', name='Corbel', style='normal', variant='normal', weight=300, stretch='normal', size='scalable')) = 10.145
2025-07-23 19:47:52,731 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\simpbdo.ttf', name='Simplified Arabic', style='normal', variant='normal', weight=700, stretch='normal', size='scalable')) = 10.335
2025-07-23 19:47:52,732 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\PER_____.TTF', name='Perpetua', style='normal', variant='normal', weight=400, stretch='normal', size='scalable')) = 10.05
2025-07-23 19:47:52,732 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\LaoUIb.ttf', name='Lao UI', style='normal', variant='normal', weight=700, stretch='normal', size='scalable')) = 10.335
2025-07-23 19:47:52,732 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\FELIXTI.TTF', name='Felix Titling', style='normal', variant='normal', weight=400, stretch='normal', size='scalable')) = 10.05
2025-07-23 19:47:52,732 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\trebuc.ttf', name='Trebuchet MS', style='normal', variant='normal', weight=400, stretch='normal', size='scalable')) = 10.05
2025-07-23 19:47:52,732 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\HTOWERTI.TTF', name='High Tower Text', style='italic', variant='normal', weight=400, stretch='normal', size='scalable')) = 11.05
2025-07-23 19:47:52,733 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\BIZ-UDMinchoM.ttc', name='BIZ UDMincho', style='normal', variant='normal', weight=500, stretch='normal', size='scalable')) = 10.145
2025-07-23 19:47:52,734 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\UrdTypeb.ttf', name='Urdu Typesetting', style='normal', variant='normal', weight=700, stretch='normal', size='scalable')) = 10.335
2025-07-23 19:47:52,734 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\VerdanaPro-CondBlack.ttf', name='Verdana Pro', style='normal', variant='normal', weight=900, stretch='condensed', size='scalable')) = 10.725
2025-07-23 19:47:52,734 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\Candarali.ttf', name='Candara', style='italic', variant='normal', weight=400, stretch='normal', size='scalable')) = 11.05
2025-07-23 19:47:52,735 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\ROCKB.TTF', name='Rockwell', style='normal', variant='normal', weight=700, stretch='normal', size='scalable')) = 10.335
2025-07-23 19:47:52,735 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\msyi.ttf', name='Microsoft Yi Baiti', style='normal', variant='normal', weight=400, stretch='normal', size='scalable')) = 10.05
2025-07-23 19:47:52,736 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\comici.ttf', name='Comic Sans MS', style='italic', variant='normal', weight=400, stretch='normal', size='scalable')) = 11.05
2025-07-23 19:47:52,736 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\cour.ttf', name='Courier New', style='normal', variant='normal', weight=400, stretch='normal', size='scalable')) = 10.05
2025-07-23 19:47:52,737 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\CENTAUR.TTF', name='Centaur', style='normal', variant='normal', weight=400, stretch='normal', size='scalable')) = 10.05
2025-07-23 19:47:52,737 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\VerdanaPro-CondBold.ttf', name='Verdana Pro', style='normal', variant='normal', weight=700, stretch='condensed', size='scalable')) = 10.535
2025-07-23 19:47:52,738 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\GOUDOSB.TTF', name='Goudy Old Style', style='normal', variant='normal', weight=700, stretch='normal', size='scalable')) = 10.335
2025-07-23 19:47:52,738 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\MATURASC.TTF', name='Matura MT Script Capitals', style='normal', variant='normal', weight=400, stretch='normal', size='scalable')) = 10.05
2025-07-23 19:47:52,738 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\FRAHV.TTF', name='Franklin Gothic Heavy', style='normal', variant='normal', weight=400, stretch='normal', size='scalable')) = 10.05
2025-07-23 19:47:52,739 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\corbelb.ttf', name='Corbel', style='normal', variant='normal', weight=700, stretch='normal', size='scalable')) = 10.335
2025-07-23 19:47:52,739 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\ArialNova-Bold.ttf', name='Arial Nova', style='normal', variant='normal', weight=700, stretch='normal', size='scalable')) = 10.335
2025-07-23 19:47:52,739 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\timesbd.ttf', name='Times New Roman', style='normal', variant='normal', weight=700, stretch='normal', size='scalable')) = 10.335
2025-07-23 19:47:52,739 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\LSANSI.TTF', name='Lucida Sans', style='italic', variant='normal', weight=400, stretch='normal', size='scalable')) = 11.05
2025-07-23 19:47:52,740 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\utsaahi.ttf', name='Utsaah', style='italic', variant='normal', weight=400, stretch='normal', size='scalable')) = 11.05
2025-07-23 19:47:52,740 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\georgiaz.ttf', name='Georgia', style='italic', variant='normal', weight=700, stretch='normal', size='scalable')) = 11.335
2025-07-23 19:47:52,740 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\GillSansCondBoNova.ttf', name='Gill Sans Nova', style='normal', variant='normal', weight=700, stretch='condensed', size='scalable')) = 10.535
2025-07-23 19:47:52,740 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\lvnm.ttf', name='Levenim MT', style='normal', variant='normal', weight=400, stretch='normal', size='scalable')) = 10.05
2025-07-23 19:47:52,740 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\FRABKIT.TTF', name='Franklin Gothic Book', style='italic', variant='normal', weight=400, stretch='normal', size='scalable')) = 11.05
2025-07-23 19:47:52,741 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\PERBI___.TTF', name='Perpetua', style='italic', variant='normal', weight=700, stretch='normal', size='scalable')) = 11.335
2025-07-23 19:47:52,741 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\Vanib.ttf', name='Vani', style='normal', variant='normal', weight=700, stretch='normal', size='scalable')) = 10.335
2025-07-23 19:47:52,742 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\segoesc.ttf', name='Segoe Script', style='normal', variant='normal', weight=400, stretch='normal', size='scalable')) = 10.05
2025-07-23 19:47:52,742 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\TCMI____.TTF', name='Tw Cen MT', style='italic', variant='normal', weight=400, stretch='normal', size='scalable')) = 11.05
2025-07-23 19:47:52,744 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\BELLI.TTF', name='Bell MT', style='italic', variant='normal', weight=400, stretch='normal', size='scalable')) = 11.05
2025-07-23 19:47:52,744 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\javatext.ttf', name='Javanese Text', style='normal', variant='normal', weight=400, stretch='normal', size='scalable')) = 10.05
2025-07-23 19:47:52,745 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\HPSimplified.ttf', name='HP Simplified', style='normal', variant='normal', weight=700, stretch='normal', size='scalable')) = 10.335
2025-07-23 19:47:52,745 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\HPSimplified_Lt.ttf', name='HP Simplified', style='normal', variant='normal', weight=300, stretch='normal', size='scalable')) = 10.145
2025-07-23 19:47:52,745 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\FRADM.TTF', name='Franklin Gothic Demi', style='normal', variant='normal', weight=400, stretch='normal', size='scalable')) = 10.05
2025-07-23 19:47:52,746 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\TCCM____.TTF', name='Tw Cen MT Condensed', style='normal', variant='normal', weight=400, stretch='condensed', size='scalable')) = 10.25
2025-07-23 19:47:52,746 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\constan.ttf', name='Constantia', style='normal', variant='normal', weight=400, stretch='normal', size='scalable')) = 10.05
2025-07-23 19:47:52,746 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\HPSimplified_BdIt.ttf', name='HP Simplified', style='italic', variant='normal', weight=700, stretch='normal', size='scalable')) = 11.335
2025-07-23 19:47:52,746 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\Candaraz.ttf', name='Candara', style='italic', variant='normal', weight=700, stretch='normal', size='scalable')) = 11.335
2025-07-23 19:47:52,746 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\ArialNovaCond.ttf', name='Arial Nova', style='normal', variant='normal', weight=400, stretch='condensed', size='scalable')) = 10.25
2025-07-23 19:47:52,746 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\Sitka.ttc', name='Sitka Small', style='normal', variant='normal', weight=400, stretch='normal', size='scalable')) = 10.05
2025-07-23 19:47:52,746 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\consolab.ttf', name='Consolas', style='normal', variant='normal', weight=700, stretch='normal', size='scalable')) = 10.335
2025-07-23 19:47:52,747 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\NHaasGroteskTXPro-66MdIt.ttf', name='Neue Haas Grotesk Text Pro', style='italic', variant='normal', weight=500, stretch='normal', size='scalable')) = 11.145
2025-07-23 19:47:52,747 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\PERTILI.TTF', name='Perpetua Titling MT', style='normal', variant='normal', weight=300, stretch='normal', size='scalable')) = 10.145
2025-07-23 19:47:52,747 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\leelawad.ttf', name='Leelawadee', style='normal', variant='normal', weight=400, stretch='normal', size='scalable')) = 10.05
2025-07-23 19:47:52,747 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\VerdanaPro-CondLightItalic.ttf', name='Verdana Pro', style='italic', variant='normal', weight=300, stretch='condensed', size='scalable')) = 11.344999999999999
2025-07-23 19:47:52,747 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\VerdanaPro-CondItalic.ttf', name='Verdana Pro', style='italic', variant='normal', weight=400, stretch='condensed', size='scalable')) = 11.25
2025-07-23 19:47:52,747 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\calibrib.ttf', name='Calibri', style='normal', variant='normal', weight=700, stretch='normal', size='scalable')) = 10.335
2025-07-23 19:47:52,748 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\vrinda.ttf', name='Vrinda', style='normal', variant='normal', weight=400, stretch='normal', size='scalable')) = 10.05
2025-07-23 19:47:52,748 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\BOD_PSTC.TTF', name='Bodoni MT', style='normal', variant='normal', weight=300, stretch='normal', size='scalable')) = 10.145
2025-07-23 19:47:52,748 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\segoescb.ttf', name='Segoe Script', style='normal', variant='normal', weight=700, stretch='normal', size='scalable')) = 10.335
2025-07-23 19:47:52,748 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\trebucbd.ttf', name='Trebuchet MS', style='normal', variant='normal', weight=700, stretch='normal', size='scalable')) = 10.335
2025-07-23 19:47:52,749 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\KhmerUI.ttf', name='Khmer UI', style='normal', variant='normal', weight=400, stretch='normal', size='scalable')) = 10.05
2025-07-23 19:47:52,750 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\AGENCYB.TTF', name='Agency FB', style='normal', variant='normal', weight=700, stretch='normal', size='scalable')) = 10.335
2025-07-23 19:47:52,751 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\BOD_BLAR.TTF', name='Bodoni MT', style='normal', variant='normal', weight=900, stretch='normal', size='scalable')) = 10.525
2025-07-23 19:47:52,751 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\upclb.ttf', name='LilyUPC', style='normal', variant='normal', weight=700, stretch='normal', size='scalable')) = 10.335
2025-07-23 19:47:52,751 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\seguisym.ttf', name='Segoe UI Symbol', style='normal', variant='normal', weight=400, stretch='normal', size='scalable')) = 10.05
2025-07-23 19:47:52,752 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\couri.ttf', name='Courier New', style='italic', variant='normal', weight=400, stretch='normal', size='scalable')) = 11.05
2025-07-23 19:47:52,752 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\angsana.ttc', name='Angsana New', style='normal', variant='normal', weight=400, stretch='normal', size='scalable')) = 10.05
2025-07-23 19:47:52,752 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\lathab.ttf', name='Latha', style='normal', variant='normal', weight=700, stretch='normal', size='scalable')) = 10.335
2025-07-23 19:47:52,753 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\GillSansBoNova.ttf', name='Gill Sans Nova', style='normal', variant='normal', weight=700, stretch='normal', size='scalable')) = 10.335
2025-07-23 19:47:52,753 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\GeorgiaPro-Light.ttf', name='Georgia Pro', style='normal', variant='normal', weight=300, stretch='normal', size='scalable')) = 10.145
2025-07-23 19:47:52,753 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\ELEPHNTI.TTF', name='Elephant', style='italic', variant='normal', weight=400, stretch='normal', size='scalable')) = 11.05
2025-07-23 19:47:52,753 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\simhei.ttf', name='SimHei', style='normal', variant='normal', weight=400, stretch='normal', size='scalable')) = 10.05
2025-07-23 19:47:52,753 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\LeelUIsl.ttf', name='Leelawadee UI', style='normal', variant='normal', weight=350, stretch='normal', size='scalable')) = 10.0975
2025-07-23 19:47:52,753 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\CURLZ___.TTF', name='Curlz MT', style='normal', variant='normal', weight=400, stretch='normal', size='scalable')) = 10.05
2025-07-23 19:47:52,753 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\upcil.ttf', name='IrisUPC', style='normal', variant='normal', weight=400, stretch='normal', size='scalable')) = 10.05
2025-07-23 19:47:52,754 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\BRITANIC.TTF', name='Britannic Bold', style='normal', variant='normal', weight=400, stretch='normal', size='scalable')) = 10.05
2025-07-23 19:47:52,754 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\aparaj.ttf', name='Aparajita', style='normal', variant='normal', weight=400, stretch='normal', size='scalable')) = 10.05
2025-07-23 19:47:52,754 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\LeelawUI.ttf', name='Leelawadee UI', style='normal', variant='normal', weight=400, stretch='normal', size='scalable')) = 10.05
2025-07-23 19:47:52,754 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\upcei.ttf', name='EucrosiaUPC', style='italic', variant='normal', weight=400, stretch='normal', size='scalable')) = 11.05
2025-07-23 19:47:52,754 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\seguiemj.ttf', name='Segoe UI Emoji', style='normal', variant='normal', weight=400, stretch='normal', size='scalable')) = 10.05
2025-07-23 19:47:52,754 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\GeorgiaPro-CondBoldItalic.ttf', name='Georgia Pro', style='italic', variant='normal', weight=700, stretch='condensed', size='scalable')) = 11.535
2025-07-23 19:47:52,755 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\iskpotab.ttf', name='Iskoola Pota', style='normal', variant='normal', weight=700, stretch='normal', size='scalable')) = 10.335
2025-07-23 19:47:52,755 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\LeelaUIb.ttf', name='Leelawadee UI', style='normal', variant='normal', weight=700, stretch='normal', size='scalable')) = 10.335
2025-07-23 19:47:52,755 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\GARAIT.TTF', name='Garamond', style='italic', variant='normal', weight=400, stretch='normal', size='scalable')) = 11.05
2025-07-23 19:47:52,755 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\mriam.ttf', name='Miriam', style='normal', variant='normal', weight=400, stretch='normal', size='scalable')) = 10.05
2025-07-23 19:47:52,755 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\LHANDW.TTF', name='Lucida Handwriting', style='italic', variant='normal', weight=400, stretch='normal', size='scalable')) = 11.05
2025-07-23 19:47:52,755 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\timesbi.ttf', name='Times New Roman', style='italic', variant='normal', weight=700, stretch='normal', size='scalable')) = 11.335
2025-07-23 19:47:52,755 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\sylfaen.ttf', name='Sylfaen', style='normal', variant='normal', weight=400, stretch='normal', size='scalable')) = 10.05
2025-07-23 19:47:52,756 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\ROCCB___.TTF', name='Rockwell Condensed', style='normal', variant='normal', weight=700, stretch='condensed', size='scalable')) = 10.535
2025-07-23 19:47:52,757 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\RockwellNovaCond-Italic.ttf', name='Rockwell Nova', style='italic', variant='normal', weight=400, stretch='condensed', size='scalable')) = 11.25
2025-07-23 19:47:52,758 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\upcjb.ttf', name='JasmineUPC', style='normal', variant='normal', weight=700, stretch='normal', size='scalable')) = 10.335
2025-07-23 19:47:52,758 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\HPSimplified_Rg.ttf', name='HP Simplified', style='normal', variant='normal', weight=400, stretch='normal', size='scalable')) = 10.05
2025-07-23 19:47:52,759 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\timesi.ttf', name='Times New Roman', style='italic', variant='normal', weight=400, stretch='normal', size='scalable')) = 11.05
2025-07-23 19:47:52,759 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\gautamib.ttf', name='Gautami', style='normal', variant='normal', weight=700, stretch='normal', size='scalable')) = 10.335
2025-07-23 19:47:52,759 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\comicbd.ttf', name='Comic Sans MS', style='normal', variant='normal', weight=700, stretch='normal', size='scalable')) = 10.335
2025-07-23 19:47:52,759 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\yumin.ttf', name='Yu Mincho', style='normal', variant='normal', weight=400, stretch='normal', size='scalable')) = 10.05
2025-07-23 19:47:52,760 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\taileb.ttf', name='Microsoft Tai Le', style='normal', variant='normal', weight=700, stretch='normal', size='scalable')) = 10.335
2025-07-23 19:47:52,760 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\ENGR.TTF', name='Engravers MT', style='normal', variant='normal', weight=500, stretch='normal', size='scalable')) = 10.145
2025-07-23 19:47:52,760 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\lucon.ttf', name='Lucida Console', style='normal', variant='normal', weight=400, stretch='normal', size='scalable')) = 10.05
2025-07-23 19:47:52,761 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\GILBI___.TTF', name='Gill Sans MT', style='italic', variant='normal', weight=700, stretch='normal', size='scalable')) = 11.335
2025-07-23 19:47:52,761 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\msgothic.ttc', name='MS Gothic', style='normal', variant='normal', weight=400, stretch='normal', size='scalable')) = 10.05
2025-07-23 19:47:52,761 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\ArialNova-Italic.ttf', name='Arial Nova', style='italic', variant='normal', weight=400, stretch='normal', size='scalable')) = 11.05
2025-07-23 19:47:52,761 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\trebucit.ttf', name='Trebuchet MS', style='italic', variant='normal', weight=400, stretch='normal', size='scalable')) = 11.05
2025-07-23 19:47:52,761 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\segmdl2.ttf', name='Segoe MDL2 Assets', style='normal', variant='normal', weight=400, stretch='normal', size='scalable')) = 10.05
2025-07-23 19:47:52,761 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\Candarab.ttf', name='Candara', style='normal', variant='normal', weight=700, stretch='normal', size='scalable')) = 10.335
2025-07-23 19:47:52,762 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\kaiu.ttf', name='DFKai-SB', style='normal', variant='normal', weight=400, stretch='normal', size='scalable')) = 10.05
2025-07-23 19:47:52,762 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\GeorgiaPro-CondBlackItalic.ttf', name='Georgia Pro', style='italic', variant='normal', weight=900, stretch='condensed', size='scalable')) = 11.725
2025-07-23 19:47:52,763 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\malgunsl.ttf', name='Malgun Gothic', style='normal', variant='normal', weight=300, stretch='normal', size='scalable')) = 10.145
2025-07-23 19:47:52,763 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\RockwellNova-Light.ttf', name='Rockwell Nova', style='normal', variant='normal', weight=300, stretch='normal', size='scalable')) = 10.145
2025-07-23 19:47:52,763 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\utsaahbi.ttf', name='Utsaah', style='italic', variant='normal', weight=700, stretch='normal', size='scalable')) = 11.335
2025-07-23 19:47:52,763 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\moolbor.ttf', name='MoolBoran', style='normal', variant='normal', weight=400, stretch='normal', size='scalable')) = 10.05
2025-07-23 19:47:52,763 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\GeorgiaPro-BlackItalic.ttf', name='Georgia Pro', style='italic', variant='normal', weight=900, stretch='normal', size='scalable')) = 11.525
2025-07-23 19:47:52,764 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\palabi.ttf', name='Palatino Linotype', style='italic', variant='normal', weight=700, stretch='normal', size='scalable')) = 11.335
2025-07-23 19:47:52,764 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\pala.ttf', name='Palatino Linotype', style='normal', variant='normal', weight=400, stretch='normal', size='scalable')) = 10.05
2025-07-23 19:47:52,764 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\NHaasGroteskTXPro-56It.ttf', name='Neue Haas Grotesk Text Pro', style='italic', variant='normal', weight=400, stretch='normal', size='scalable')) = 11.05
2025-07-23 19:47:52,764 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\HATTEN.TTF', name='Haettenschweiler', style='normal', variant='normal', weight=400, stretch='normal', size='scalable')) = 10.05
2025-07-23 19:47:52,764 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\BOD_CB.TTF', name='Bodoni MT', style='normal', variant='normal', weight=700, stretch='condensed', size='scalable')) = 10.535
2025-07-23 19:47:52,764 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\CALISTI.TTF', name='Calisto MT', style='italic', variant='normal', weight=400, stretch='normal', size='scalable')) = 11.05
2025-07-23 19:47:52,765 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\NHaasGroteskTXPro-55Rg.ttf', name='Neue Haas Grotesk Text Pro', style='normal', variant='normal', weight=400, stretch='normal', size='scalable')) = 10.05
2025-07-23 19:47:52,765 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\kokilab.ttf', name='Kokila', style='normal', variant='normal', weight=700, stretch='normal', size='scalable')) = 10.335
2025-07-23 19:47:52,765 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\BKANT.TTF', name='Book Antiqua', style='normal', variant='normal', weight=400, stretch='normal', size='scalable')) = 10.05
2025-07-23 19:47:52,765 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\ROCKBI.TTF', name='Rockwell', style='italic', variant='normal', weight=700, stretch='normal', size='scalable')) = 11.335
2025-07-23 19:47:52,766 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\BOD_CBI.TTF', name='Bodoni MT', style='italic', variant='normal', weight=700, stretch='condensed', size='scalable')) = 11.535
2025-07-23 19:47:52,767 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\Nirmala.ttf', name='Nirmala UI', style='normal', variant='normal', weight=400, stretch='normal', size='scalable')) = 10.05
2025-07-23 19:47:52,767 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\malgunbd.ttf', name='Malgun Gothic', style='normal', variant='normal', weight=700, stretch='normal', size='scalable')) = 10.335
2025-07-23 19:47:52,768 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\SitkaI.ttc', name='Sitka Small', style='italic', variant='normal', weight=400, stretch='normal', size='scalable')) = 11.05
2025-07-23 19:47:52,768 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\ERASBD.TTF', name='Eras Bold ITC', style='normal', variant='normal', weight=400, stretch='normal', size='scalable')) = 10.05
2025-07-23 19:47:52,769 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\seguisb.ttf', name='Segoe UI', style='normal', variant='normal', weight=600, stretch='normal', size='scalable')) = 10.24
2025-07-23 19:47:52,769 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\simpfxo.ttf', name='Simplified Arabic Fixed', style='normal', variant='normal', weight=400, stretch='normal', size='scalable')) = 10.05
2025-07-23 19:47:52,769 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\SCRIPTBL.TTF', name='Script MT Bold', style='normal', variant='normal', weight=700, stretch='normal', size='scalable')) = 10.335
2025-07-23 19:47:52,770 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\Deng.ttf', name='DengXian', style='normal', variant='normal', weight=400, stretch='normal', size='scalable')) = 10.05
2025-07-23 19:47:52,770 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\Vani.ttf', name='Vani', style='normal', variant='normal', weight=400, stretch='normal', size='scalable')) = 10.05
2025-07-23 19:47:52,770 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\comic.ttf', name='Comic Sans MS', style='normal', variant='normal', weight=400, stretch='normal', size='scalable')) = 10.05
2025-07-23 19:47:52,771 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\msyh.ttc', name='Microsoft YaHei', style='normal', variant='normal', weight=400, stretch='normal', size='scalable')) = 10.05
2025-07-23 19:47:52,771 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\BRUSHSCI.TTF', name='Brush Script MT', style='italic', variant='normal', weight=400, stretch='normal', size='scalable')) = 11.05
2025-07-23 19:47:52,772 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\GOTHIC.TTF', name='Century Gothic', style='normal', variant='normal', weight=400, stretch='normal', size='scalable')) = 10.05
2025-07-23 19:47:52,772 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\GOTHICB.TTF', name='Century Gothic', style='normal', variant='normal', weight=700, stretch='normal', size='scalable')) = 10.335
2025-07-23 19:47:52,773 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\msuighub.ttf', name='Microsoft Uighur', style='normal', variant='normal', weight=700, stretch='normal', size='scalable')) = 10.335
2025-07-23 19:47:52,774 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\kokila.ttf', name='Kokila', style='normal', variant='normal', weight=400, stretch='normal', size='scalable')) = 10.05
2025-07-23 19:47:52,774 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\VerdanaPro-CondSemiBoldItalic.ttf', name='Verdana Pro', style='italic', variant='normal', weight=600, stretch='condensed', size='scalable')) = 11.44
2025-07-23 19:47:52,775 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\browalia.ttc', name='Browallia New', style='normal', variant='normal', weight=400, stretch='normal', size='scalable')) = 10.05
2025-07-23 19:47:52,775 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\SCHLBKI.TTF', name='Century Schoolbook', style='italic', variant='normal', weight=400, stretch='normal', size='scalable')) = 11.05
2025-07-23 19:47:52,776 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\PERB____.TTF', name='Perpetua', style='normal', variant='normal', weight=700, stretch='normal', size='scalable')) = 10.335
2025-07-23 19:47:52,776 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\RockwellNovaCond-Bold.ttf', name='Rockwell Nova', style='normal', variant='normal', weight=700, stretch='condensed', size='scalable')) = 10.535
2025-07-23 19:47:52,776 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\ELEPHNT.TTF', name='Elephant', style='normal', variant='normal', weight=400, stretch='normal', size='scalable')) = 10.05
2025-07-23 19:47:52,777 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\NIAGENG.TTF', name='Niagara Engraved', style='normal', variant='normal', weight=400, stretch='normal', size='scalable')) = 10.05
2025-07-23 19:47:52,777 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\courbi.ttf', name='Courier New', style='italic', variant='normal', weight=700, stretch='normal', size='scalable')) = 11.335
2025-07-23 19:47:52,777 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\impact.ttf', name='Impact', style='normal', variant='normal', weight=400, stretch='normal', size='scalable')) = 10.05
2025-07-23 19:47:52,777 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\trebucbi.ttf', name='Trebuchet MS', style='italic', variant='normal', weight=700, stretch='normal', size='scalable')) = 11.335
2025-07-23 19:47:52,777 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\upcdb.ttf', name='DilleniaUPC', style='normal', variant='normal', weight=700, stretch='normal', size='scalable')) = 10.335
2025-07-23 19:47:52,777 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\LTYPEB.TTF', name='Lucida Sans Typewriter', style='normal', variant='normal', weight=600, stretch='normal', size='scalable')) = 10.24
2025-07-23 19:47:52,778 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\NHaasGroteskTXPro-75Bd.ttf', name='Neue Haas Grotesk Text Pro', style='normal', variant='normal', weight=700, stretch='normal', size='scalable')) = 10.335
2025-07-23 19:47:52,778 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\BRADHITC.TTF', name='Bradley Hand ITC', style='normal', variant='normal', weight=400, stretch='normal', size='scalable')) = 10.05
2025-07-23 19:47:52,778 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\GeorgiaPro-BoldItalic.ttf', name='Georgia Pro', style='italic', variant='normal', weight=700, stretch='normal', size='scalable')) = 11.335
2025-07-23 19:47:52,778 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\palab.ttf', name='Palatino Linotype', style='normal', variant='normal', weight=700, stretch='normal', size='scalable')) = 10.335
2025-07-23 19:47:52,778 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\malgun.ttf', name='Malgun Gothic', style='normal', variant='normal', weight=400, stretch='normal', size='scalable')) = 10.05
2025-07-23 19:47:52,778 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\NirmalaS.ttf', name='Nirmala UI', style='normal', variant='normal', weight=350, stretch='normal', size='scalable')) = 10.0975
2025-07-23 19:47:52,779 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\upcii.ttf', name='IrisUPC', style='italic', variant='normal', weight=400, stretch='normal', size='scalable')) = 11.05
2025-07-23 19:47:52,779 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\calibriz.ttf', name='Calibri', style='italic', variant='normal', weight=700, stretch='normal', size='scalable')) = 11.335
2025-07-23 19:47:52,779 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\tahomabd.ttf', name='Tahoma', style='normal', variant='normal', weight=700, stretch='normal', size='scalable')) = 10.335
2025-07-23 19:47:52,779 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\msmincho.ttc', name='MS Mincho', style='normal', variant='normal', weight=400, stretch='normal', size='scalable')) = 10.05
2025-07-23 19:47:52,779 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\segoeuii.ttf', name='Segoe UI', style='italic', variant='normal', weight=400, stretch='normal', size='scalable')) = 11.05
2025-07-23 19:47:52,780 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\GARA.TTF', name='Garamond', style='normal', variant='normal', weight=400, stretch='normal', size='scalable')) = 10.05
2025-07-23 19:47:52,780 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\STENCIL.TTF', name='Stencil', style='normal', variant='normal', weight=400, stretch='normal', size='scalable')) = 10.05
2025-07-23 19:47:52,780 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\ebrima.ttf', name='Ebrima', style='normal', variant='normal', weight=400, stretch='normal', size='scalable')) = 10.05
2025-07-23 19:47:52,780 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\tahoma.ttf', name='Tahoma', style='normal', variant='normal', weight=400, stretch='normal', size='scalable')) = 10.05
2025-07-23 19:47:52,780 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\CASTELAR.TTF', name='Castellar', style='normal', variant='normal', weight=400, stretch='normal', size='scalable')) = 10.05
2025-07-23 19:47:52,780 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\CENTURY.TTF', name='Century', style='normal', variant='normal', weight=400, stretch='normal', size='scalable')) = 10.05
2025-07-23 19:47:52,780 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\segoeuiz.ttf', name='Segoe UI', style='italic', variant='normal', weight=700, stretch='normal', size='scalable')) = 11.335
2025-07-23 19:47:52,781 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\GeorgiaPro-SemiBoldItalic.ttf', name='Georgia Pro', style='italic', variant='normal', weight=600, stretch='normal', size='scalable')) = 11.24
2025-07-23 19:47:52,781 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\GILLUBCD.TTF', name='Gill Sans Ultra Bold Condensed', style='normal', variant='normal', weight=400, stretch='condensed', size='scalable')) = 10.25
2025-07-23 19:47:52,781 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\upcfb.ttf', name='FreesiaUPC', style='normal', variant='normal', weight=700, stretch='normal', size='scalable')) = 10.335
2025-07-23 19:47:52,781 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\GillSansCondLightItNova.ttf', name='Gill Sans Nova', style='italic', variant='normal', weight=300, stretch='condensed', size='scalable')) = 11.344999999999999
2025-07-23 19:47:52,782 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\VerdanaPro-Black.ttf', name='Verdana Pro', style='normal', variant='normal', weight=900, stretch='normal', size='scalable')) = 10.525
2025-07-23 19:47:52,782 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\arialnbi.ttf', name='Arial', style='italic', variant='normal', weight=700, stretch='condensed', size='scalable')) = 1.535
2025-07-23 19:47:52,783 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\upcibi.ttf', name='IrisUPC', style='italic', variant='normal', weight=700, stretch='normal', size='scalable')) = 11.335
2025-07-23 19:47:52,784 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\BOOKOS.TTF', name='Bookman Old Style', style='normal', variant='normal', weight=300, stretch='normal', size='scalable')) = 10.145
2025-07-23 19:47:52,784 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\GillSansCondLightNova.ttf', name='Gill Sans Nova', style='normal', variant='normal', weight=300, stretch='condensed', size='scalable')) = 10.344999999999999
2025-07-23 19:47:52,785 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\BELL.TTF', name='Bell MT', style='normal', variant='normal', weight=400, stretch='normal', size='scalable')) = 10.05
2025-07-23 19:47:52,785 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\OUTLOOK.TTF', name='MS Outlook', style='normal', variant='normal', weight=400, stretch='normal', size='scalable')) = 10.05
2025-07-23 19:47:52,785 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\ITCKRIST.TTF', name='Kristen ITC', style='normal', variant='normal', weight=400, stretch='normal', size='scalable')) = 10.05
2025-07-23 19:47:52,785 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\simkai.ttf', name='KaiTi', style='normal', variant='normal', weight=400, stretch='normal', size='scalable')) = 10.05
2025-07-23 19:47:52,785 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\aldhabi.ttf', name='Aldhabi', style='normal', variant='normal', weight=400, stretch='normal', size='scalable')) = 10.05
2025-07-23 19:47:52,785 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\FREESCPT.TTF', name='Freestyle Script', style='normal', variant='normal', weight=400, stretch='normal', size='scalable')) = 10.05
2025-07-23 19:47:52,786 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\PERI____.TTF', name='Perpetua', style='italic', variant='normal', weight=400, stretch='normal', size='scalable')) = 11.05
2025-07-23 19:47:52,786 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\arialn.ttf', name='Arial', style='normal', variant='normal', weight=400, stretch='condensed', size='scalable')) = 0.25
2025-07-23 19:47:52,786 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\YuGothM.ttc', name='Yu Gothic', style='normal', variant='normal', weight=500, stretch='normal', size='scalable')) = 10.145
2025-07-23 19:47:52,786 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\batang.ttc', name='Batang', style='normal', variant='normal', weight=400, stretch='normal', size='scalable')) = 10.05
2025-07-23 19:47:52,786 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\meiryo.ttc', name='Meiryo', style='normal', variant='normal', weight=400, stretch='normal', size='scalable')) = 10.05
2025-07-23 19:47:52,786 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\shrutib.ttf', name='Shruti', style='normal', variant='normal', weight=700, stretch='normal', size='scalable')) = 10.335
2025-07-23 19:47:52,787 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\kokilabi.ttf', name='Kokila', style='italic', variant='normal', weight=700, stretch='normal', size='scalable')) = 11.335
2025-07-23 19:47:52,788 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\GOUDYSTO.TTF', name='Goudy Stout', style='normal', variant='normal', weight=400, stretch='normal', size='scalable')) = 10.05
2025-07-23 19:47:52,788 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\seguihis.ttf', name='Segoe UI Historic', style='normal', variant='normal', weight=400, stretch='normal', size='scalable')) = 10.05
2025-07-23 19:47:52,788 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\majallab.ttf', name='Sakkal Majalla', style='normal', variant='normal', weight=700, stretch='normal', size='scalable')) = 10.335
2025-07-23 19:47:52,788 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\GILC____.TTF', name='Gill Sans MT Condensed', style='normal', variant='normal', weight=400, stretch='condensed', size='scalable')) = 10.25
2025-07-23 19:47:52,789 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\corbelli.ttf', name='Corbel', style='italic', variant='normal', weight=300, stretch='normal', size='scalable')) = 11.145
2025-07-23 19:47:52,789 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\seguisbi.ttf', name='Segoe UI', style='italic', variant='normal', weight=600, stretch='normal', size='scalable')) = 11.24
2025-07-23 19:47:52,790 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\BOD_CI.TTF', name='Bodoni MT', style='italic', variant='normal', weight=400, stretch='condensed', size='scalable')) = 11.25
2025-07-23 19:47:52,791 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\VerdanaPro-CondSemiBold.ttf', name='Verdana Pro', style='normal', variant='normal', weight=600, stretch='condensed', size='scalable')) = 10.44
2025-07-23 19:47:52,791 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\YuGothR.ttc', name='Yu Gothic', style='normal', variant='normal', weight=400, stretch='normal', size='scalable')) = 10.05
2025-07-23 19:47:52,791 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\ONYX.TTF', name='Onyx', style='normal', variant='normal', weight=400, stretch='normal', size='scalable')) = 10.05
2025-07-23 19:47:52,792 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\ArialNova-LightItalic.ttf', name='Arial Nova', style='italic', variant='normal', weight=300, stretch='normal', size='scalable')) = 11.145
2025-07-23 19:47:52,792 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\phagspab.ttf', name='Microsoft PhagsPa', style='normal', variant='normal', weight=700, stretch='normal', size='scalable')) = 10.335
2025-07-23 19:47:52,792 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\BOD_R.TTF', name='Bodoni MT', style='normal', variant='normal', weight=400, stretch='normal', size='scalable')) = 10.05
2025-07-23 19:47:52,792 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\REFSPCL.TTF', name='MS Reference Specialty', style='normal', variant='normal', weight=400, stretch='normal', size='scalable')) = 10.05
2025-07-23 19:47:52,792 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\gulim.ttc', name='Gulim', style='normal', variant='normal', weight=400, stretch='normal', size='scalable')) = 10.05
2025-07-23 19:47:52,793 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\ANTQUAB.TTF', name='Book Antiqua', style='normal', variant='normal', weight=700, stretch='normal', size='scalable')) = 10.335
2025-07-23 19:47:52,793 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\gisha.ttf', name='Gisha', style='normal', variant='normal', weight=400, stretch='normal', size='scalable')) = 10.05
2025-07-23 19:47:52,793 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\LBRITE.TTF', name='Lucida Bright', style='normal', variant='normal', weight=400, stretch='normal', size='scalable')) = 10.05
2025-07-23 19:47:52,793 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\calibrii.ttf', name='Calibri', style='italic', variant='normal', weight=400, stretch='normal', size='scalable')) = 11.05
2025-07-23 19:47:52,793 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\LTYPEO.TTF', name='Lucida Sans Typewriter', style='oblique', variant='normal', weight=400, stretch='normal', size='scalable')) = 11.05
2025-07-23 19:47:52,793 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\webdings.ttf', name='Webdings', style='normal', variant='normal', weight=400, stretch='normal', size='scalable')) = 10.05
2025-07-23 19:47:52,794 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\WINGDNG2.TTF', name='Wingdings 2', style='normal', variant='normal', weight=400, stretch='normal', size='scalable')) = 10.05
2025-07-23 19:47:52,794 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\corbel.ttf', name='Corbel', style='normal', variant='normal', weight=400, stretch='normal', size='scalable')) = 10.05
2025-07-23 19:47:52,794 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\MAIAN.TTF', name='Maiandra GD', style='normal', variant='normal', weight=400, stretch='normal', size='scalable')) = 10.05
2025-07-23 19:47:52,794 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\YuGothL.ttc', name='Yu Gothic', style='normal', variant='normal', weight=300, stretch='normal', size='scalable')) = 10.145
2025-07-23 19:47:52,794 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\ERASLGHT.TTF', name='Eras Light ITC', style='normal', variant='normal', weight=400, stretch='normal', size='scalable')) = 10.05
2025-07-23 19:47:52,794 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\segoeuisl.ttf', name='Segoe UI', style='normal', variant='normal', weight=350, stretch='normal', size='scalable')) = 10.0975
2025-07-23 19:47:52,794 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\RockwellNova-ExtraBold.ttf', name='Rockwell Nova', style='normal', variant='normal', weight=800, stretch='normal', size='scalable')) = 10.43
2025-07-23 19:47:52,795 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\RAGE.TTF', name='Rage Italic', style='italic', variant='normal', weight=400, stretch='normal', size='scalable')) = 11.05
2025-07-23 19:47:52,795 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\GillSansLightNova.ttf', name='Gill Sans Nova', style='normal', variant='normal', weight=300, stretch='normal', size='scalable')) = 10.145
2025-07-23 19:47:52,796 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\RockwellNova-BoldItalic.ttf', name='Rockwell Nova', style='italic', variant='normal', weight=700, stretch='normal', size='scalable')) = 11.335
2025-07-23 19:47:52,796 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\calibril.ttf', name='Calibri', style='normal', variant='normal', weight=300, stretch='normal', size='scalable')) = 10.145
2025-07-23 19:47:52,796 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\NHaasGroteskTXPro-65Md.ttf', name='Neue Haas Grotesk Text Pro', style='normal', variant='normal', weight=500, stretch='normal', size='scalable')) = 10.145
2025-07-23 19:47:52,796 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\OLDENGL.TTF', name='Old English Text MT', style='normal', variant='normal', weight=400, stretch='normal', size='scalable')) = 10.05
2025-07-23 19:47:52,796 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\BIZ-UDGothicR.ttc', name='BIZ UDGothic', style='normal', variant='normal', weight=400, stretch='normal', size='scalable')) = 10.05
2025-07-23 19:47:52,796 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\trado.ttf', name='Traditional Arabic', style='normal', variant='normal', weight=400, stretch='normal', size='scalable')) = 10.05
2025-07-23 19:47:52,796 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\upcdl.ttf', name='DilleniaUPC', style='normal', variant='normal', weight=400, stretch='normal', size='scalable')) = 10.05
2025-07-23 19:47:52,797 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\GeorgiaPro-CondBold.ttf', name='Georgia Pro', style='normal', variant='normal', weight=700, stretch='condensed', size='scalable')) = 10.535
2025-07-23 19:47:52,797 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\RockwellNova-ExtraBoldItalic.ttf', name='Rockwell Nova', style='italic', variant='normal', weight=800, stretch='normal', size='scalable')) = 11.43
2025-07-23 19:47:52,798 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\SCHLBKBI.TTF', name='Century Schoolbook', style='italic', variant='normal', weight=700, stretch='normal', size='scalable')) = 11.335
2025-07-23 19:47:52,799 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\shruti.ttf', name='Shruti', style='normal', variant='normal', weight=400, stretch='normal', size='scalable')) = 10.05
2025-07-23 19:47:52,799 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\RockwellNova.ttf', name='Rockwell Nova', style='normal', variant='normal', weight=400, stretch='normal', size='scalable')) = 10.05
2025-07-23 19:47:52,799 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\ArialNovaCond-Bold.ttf', name='Arial Nova', style='normal', variant='normal', weight=700, stretch='condensed', size='scalable')) = 10.535
2025-07-23 19:47:52,799 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\Sanskr.ttf', name='Sanskrit Text', style='normal', variant='normal', weight=400, stretch='normal', size='scalable')) = 10.05
2025-07-23 19:47:52,799 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\upcfbi.ttf', name='FreesiaUPC', style='italic', variant='normal', weight=700, stretch='normal', size='scalable')) = 11.335
2025-07-23 19:47:52,800 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\gadugi.ttf', name='Gadugi', style='normal', variant='normal', weight=400, stretch='normal', size='scalable')) = 10.05
2025-07-23 19:47:52,800 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\times.ttf', name='Times New Roman', style='normal', variant='normal', weight=400, stretch='normal', size='scalable')) = 10.05
2025-07-23 19:47:52,800 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\RockwellNova-Bold.ttf', name='Rockwell Nova', style='normal', variant='normal', weight=700, stretch='normal', size='scalable')) = 10.335
2025-07-23 19:47:52,800 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\VINERITC.TTF', name='Viner Hand ITC', style='normal', variant='normal', weight=400, stretch='normal', size='scalable')) = 10.05
2025-07-23 19:47:52,801 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\gautami.ttf', name='Gautami', style='normal', variant='normal', weight=400, stretch='normal', size='scalable')) = 10.05
2025-07-23 19:47:52,801 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\raavib.ttf', name='Raavi', style='normal', variant='normal', weight=700, stretch='normal', size='scalable')) = 10.335
2025-07-23 19:47:52,802 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\arialbi.ttf', name='Arial', style='italic', variant='normal', weight=700, stretch='normal', size='scalable')) = 1.335
2025-07-23 19:47:52,802 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\SHOWG.TTF', name='Showcard Gothic', style='normal', variant='normal', weight=400, stretch='normal', size='scalable')) = 10.05
2025-07-23 19:47:52,802 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\GeorgiaPro-CondSemiBold.ttf', name='Georgia Pro', style='normal', variant='normal', weight=600, stretch='condensed', size='scalable')) = 10.44
2025-07-23 19:47:52,802 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\GeorgiaPro-Regular.ttf', name='Georgia Pro', style='normal', variant='normal', weight=400, stretch='normal', size='scalable')) = 10.05
2025-07-23 19:47:52,803 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\NHaasGroteskTXPro-76BdIt.ttf', name='Neue Haas Grotesk Text Pro', style='italic', variant='normal', weight=700, stretch='normal', size='scalable')) = 11.335
2025-07-23 19:47:52,803 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\msuighur.ttf', name='Microsoft Uighur', style='normal', variant='normal', weight=400, stretch='normal', size='scalable')) = 10.05
2025-07-23 19:47:52,803 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\LTYPEBO.TTF', name='Lucida Sans Typewriter', style='oblique', variant='normal', weight=600, stretch='normal', size='scalable')) = 11.24
2025-07-23 19:47:52,803 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\LCALLIG.TTF', name='Lucida Calligraphy', style='italic', variant='normal', weight=400, stretch='normal', size='scalable')) = 11.05
2025-07-23 19:47:52,804 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\LBRITEI.TTF', name='Lucida Bright', style='italic', variant='normal', weight=400, stretch='normal', size='scalable')) = 11.05
2025-07-23 19:47:52,804 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\mingliu.ttc', name='MingLiU', style='normal', variant='normal', weight=400, stretch='normal', size='scalable')) = 10.05
2025-07-23 19:47:52,804 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\BOOKOSBI.TTF', name='Bookman Old Style', style='italic', variant='normal', weight=600, stretch='normal', size='scalable')) = 11.24
2025-07-23 19:47:52,804 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\ERASDEMI.TTF', name='Eras Demi ITC', style='normal', variant='normal', weight=400, stretch='normal', size='scalable')) = 10.05
2025-07-23 19:47:52,804 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\constanz.ttf', name='Constantia', style='italic', variant='normal', weight=700, stretch='normal', size='scalable')) = 11.335
2025-07-23 19:47:52,804 - DEBUG - findfont: Matching Arial:style=normal:variant=normal:weight=normal:stretch=normal:size=10.0 to Arial ('C:\\Windows\\Fonts\\arial.ttf') with score of 0.050000.
2025-07-23 19:47:52,818 - DEBUG - findfont: Matching Arial:style=normal:variant=normal:weight=normal:stretch=normal:size=12.0.
2025-07-23 19:47:52,819 - DEBUG - findfont: score(FontEntry(fname='C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\site-packages\\matplotlib\\mpl-data\\fonts\\ttf\\cmss10.ttf', name='cmss10', style='normal', variant='normal', weight=400, stretch='normal', size='scalable')) = 10.05
2025-07-23 19:47:52,820 - DEBUG - findfont: score(FontEntry(fname='C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\site-packages\\matplotlib\\mpl-data\\fonts\\ttf\\DejaVuSansMono.ttf', name='DejaVu Sans Mono', style='normal', variant='normal', weight=400, stretch='normal', size='scalable')) = 10.05
2025-07-23 19:47:52,820 - DEBUG - findfont: score(FontEntry(fname='C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\site-packages\\matplotlib\\mpl-data\\fonts\\ttf\\STIXGeneral.ttf', name='STIXGeneral', style='normal', variant='normal', weight=400, stretch='normal', size='scalable')) = 10.05
2025-07-23 19:47:52,820 - DEBUG - findfont: score(FontEntry(fname='C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\site-packages\\matplotlib\\mpl-data\\fonts\\ttf\\STIXGeneralItalic.ttf', name='STIXGeneral', style='italic', variant='normal', weight=400, stretch='normal', size='scalable')) = 11.05
2025-07-23 19:47:52,821 - DEBUG - findfont: score(FontEntry(fname='C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\site-packages\\matplotlib\\mpl-data\\fonts\\ttf\\STIXNonUniBol.ttf', name='STIXNonUnicode', style='normal', variant='normal', weight=700, stretch='normal', size='scalable')) = 10.335
2025-07-23 19:47:52,821 - DEBUG - findfont: score(FontEntry(fname='C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\site-packages\\matplotlib\\mpl-data\\fonts\\ttf\\cmsy10.ttf', name='cmsy10', style='normal', variant='normal', weight=400, stretch='normal', size='scalable')) = 10.05
2025-07-23 19:47:52,821 - DEBUG - findfont: score(FontEntry(fname='C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\site-packages\\matplotlib\\mpl-data\\fonts\\ttf\\DejaVuSansMono-Oblique.ttf', name='DejaVu Sans Mono', style='oblique', variant='normal', weight=400, stretch='normal', size='scalable')) = 11.05
2025-07-23 19:47:52,821 - DEBUG - findfont: score(FontEntry(fname='C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\site-packages\\matplotlib\\mpl-data\\fonts\\ttf\\DejaVuSansMono-Bold.ttf', name='DejaVu Sans Mono', style='normal', variant='normal', weight=700, stretch='normal', size='scalable')) = 10.335
2025-07-23 19:47:52,822 - DEBUG - findfont: score(FontEntry(fname='C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\site-packages\\matplotlib\\mpl-data\\fonts\\ttf\\DejaVuSerif-Italic.ttf', name='DejaVu Serif', style='italic', variant='normal', weight=400, stretch='normal', size='scalable')) = 11.05
2025-07-23 19:47:52,823 - DEBUG - findfont: score(FontEntry(fname='C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\site-packages\\matplotlib\\mpl-data\\fonts\\ttf\\cmtt10.ttf', name='cmtt10', style='normal', variant='normal', weight=400, stretch='normal', size='scalable')) = 10.05
2025-07-23 19:47:52,823 - DEBUG - findfont: score(FontEntry(fname='C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\site-packages\\matplotlib\\mpl-data\\fonts\\ttf\\STIXSizThreeSymReg.ttf', name='STIXSizeThreeSym', style='normal', variant='normal', weight=400, stretch='normal', size='scalable')) = 10.05
2025-07-23 19:47:52,823 - DEBUG - findfont: score(FontEntry(fname='C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\site-packages\\matplotlib\\mpl-data\\fonts\\ttf\\cmex10.ttf', name='cmex10', style='normal', variant='normal', weight=400, stretch='normal', size='scalable')) = 10.05
2025-07-23 19:47:52,823 - DEBUG - findfont: score(FontEntry(fname='C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\site-packages\\matplotlib\\mpl-data\\fonts\\ttf\\STIXSizFourSymBol.ttf', name='STIXSizeFourSym', style='normal', variant='normal', weight=700, stretch='normal', size='scalable')) = 10.335
2025-07-23 19:47:52,823 - DEBUG - findfont: score(FontEntry(fname='C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\site-packages\\matplotlib\\mpl-data\\fonts\\ttf\\STIXSizFourSymReg.ttf', name='STIXSizeFourSym', style='normal', variant='normal', weight=400, stretch='normal', size='scalable')) = 10.05
2025-07-23 19:47:52,823 - DEBUG - findfont: score(FontEntry(fname='C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\site-packages\\matplotlib\\mpl-data\\fonts\\ttf\\STIXSizTwoSymBol.ttf', name='STIXSizeTwoSym', style='normal', variant='normal', weight=700, stretch='normal', size='scalable')) = 10.335
2025-07-23 19:47:52,824 - DEBUG - findfont: score(FontEntry(fname='C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\site-packages\\matplotlib\\mpl-data\\fonts\\ttf\\STIXSizTwoSymReg.ttf', name='STIXSizeTwoSym', style='normal', variant='normal', weight=400, stretch='normal', size='scalable')) = 10.05
2025-07-23 19:47:52,825 - DEBUG - findfont: score(FontEntry(fname='C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\site-packages\\matplotlib\\mpl-data\\fonts\\ttf\\STIXSizFiveSymReg.ttf', name='STIXSizeFiveSym', style='normal', variant='normal', weight=400, stretch='normal', size='scalable')) = 10.05
2025-07-23 19:47:52,825 - DEBUG - findfont: score(FontEntry(fname='C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\site-packages\\matplotlib\\mpl-data\\fonts\\ttf\\cmmi10.ttf', name='cmmi10', style='normal', variant='normal', weight=400, stretch='normal', size='scalable')) = 10.05
2025-07-23 19:47:52,825 - DEBUG - findfont: score(FontEntry(fname='C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\site-packages\\matplotlib\\mpl-data\\fonts\\ttf\\STIXSizOneSymReg.ttf', name='STIXSizeOneSym', style='normal', variant='normal', weight=400, stretch='normal', size='scalable')) = 10.05
2025-07-23 19:47:52,825 - DEBUG - findfont: score(FontEntry(fname='C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\site-packages\\matplotlib\\mpl-data\\fonts\\ttf\\DejaVuSansMono-BoldOblique.ttf', name='DejaVu Sans Mono', style='oblique', variant='normal', weight=700, stretch='normal', size='scalable')) = 11.335
2025-07-23 19:47:52,825 - DEBUG - findfont: score(FontEntry(fname='C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\site-packages\\matplotlib\\mpl-data\\fonts\\ttf\\STIXNonUni.ttf', name='STIXNonUnicode', style='normal', variant='normal', weight=400, stretch='normal', size='scalable')) = 10.05
2025-07-23 19:47:52,826 - DEBUG - findfont: score(FontEntry(fname='C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\site-packages\\matplotlib\\mpl-data\\fonts\\ttf\\DejaVuSerif-BoldItalic.ttf', name='DejaVu Serif', style='italic', variant='normal', weight=700, stretch='normal', size='scalable')) = 11.335
2025-07-23 19:47:52,826 - DEBUG - findfont: score(FontEntry(fname='C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\site-packages\\matplotlib\\mpl-data\\fonts\\ttf\\STIXGeneralBol.ttf', name='STIXGeneral', style='normal', variant='normal', weight=700, stretch='normal', size='scalable')) = 10.335
2025-07-23 19:47:52,826 - DEBUG - findfont: score(FontEntry(fname='C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\site-packages\\matplotlib\\mpl-data\\fonts\\ttf\\DejaVuSerif.ttf', name='DejaVu Serif', style='normal', variant='normal', weight=400, stretch='normal', size='scalable')) = 10.05
2025-07-23 19:47:52,826 - DEBUG - findfont: score(FontEntry(fname='C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\site-packages\\matplotlib\\mpl-data\\fonts\\ttf\\STIXNonUniBolIta.ttf', name='STIXNonUnicode', style='italic', variant='normal', weight=700, stretch='normal', size='scalable')) = 11.335
2025-07-23 19:47:52,826 - DEBUG - findfont: score(FontEntry(fname='C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\site-packages\\matplotlib\\mpl-data\\fonts\\ttf\\STIXSizOneSymBol.ttf', name='STIXSizeOneSym', style='normal', variant='normal', weight=700, stretch='normal', size='scalable')) = 10.335
2025-07-23 19:47:52,826 - DEBUG - findfont: score(FontEntry(fname='C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\site-packages\\matplotlib\\mpl-data\\fonts\\ttf\\DejaVuSerif-Bold.ttf', name='DejaVu Serif', style='normal', variant='normal', weight=700, stretch='normal', size='scalable')) = 10.335
2025-07-23 19:47:52,827 - DEBUG - findfont: score(FontEntry(fname='C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\site-packages\\matplotlib\\mpl-data\\fonts\\ttf\\DejaVuSans-Oblique.ttf', name='DejaVu Sans', style='oblique', variant='normal', weight=400, stretch='normal', size='scalable')) = 11.05
2025-07-23 19:47:52,827 - DEBUG - findfont: score(FontEntry(fname='C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\site-packages\\matplotlib\\mpl-data\\fonts\\ttf\\DejaVuSans-Bold.ttf', name='DejaVu Sans', style='normal', variant='normal', weight=700, stretch='normal', size='scalable')) = 10.335
2025-07-23 19:47:52,827 - DEBUG - findfont: score(FontEntry(fname='C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\site-packages\\matplotlib\\mpl-data\\fonts\\ttf\\cmr10.ttf', name='cmr10', style='normal', variant='normal', weight=400, stretch='normal', size='scalable')) = 10.05
2025-07-23 19:47:52,827 - DEBUG - findfont: score(FontEntry(fname='C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\site-packages\\matplotlib\\mpl-data\\fonts\\ttf\\cmb10.ttf', name='cmb10', style='normal', variant='normal', weight=400, stretch='normal', size='scalable')) = 10.05
2025-07-23 19:47:52,827 - DEBUG - findfont: score(FontEntry(fname='C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\site-packages\\matplotlib\\mpl-data\\fonts\\ttf\\DejaVuSansDisplay.ttf', name='DejaVu Sans Display', style='normal', variant='normal', weight=400, stretch='normal', size='scalable')) = 10.05
2025-07-23 19:47:52,827 - DEBUG - findfont: score(FontEntry(fname='C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\site-packages\\matplotlib\\mpl-data\\fonts\\ttf\\DejaVuSans.ttf', name='DejaVu Sans', style='normal', variant='normal', weight=400, stretch='normal', size='scalable')) = 10.05
2025-07-23 19:47:52,828 - DEBUG - findfont: score(FontEntry(fname='C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\site-packages\\matplotlib\\mpl-data\\fonts\\ttf\\STIXSizThreeSymBol.ttf', name='STIXSizeThreeSym', style='normal', variant='normal', weight=700, stretch='normal', size='scalable')) = 10.335
2025-07-23 19:47:52,828 - DEBUG - findfont: score(FontEntry(fname='C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\site-packages\\matplotlib\\mpl-data\\fonts\\ttf\\STIXGeneralBolIta.ttf', name='STIXGeneral', style='italic', variant='normal', weight=700, stretch='normal', size='scalable')) = 11.335
2025-07-23 19:47:52,828 - DEBUG - findfont: score(FontEntry(fname='C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\site-packages\\matplotlib\\mpl-data\\fonts\\ttf\\DejaVuSerifDisplay.ttf', name='DejaVu Serif Display', style='normal', variant='normal', weight=400, stretch='normal', size='scalable')) = 10.05
2025-07-23 19:47:52,828 - DEBUG - findfont: score(FontEntry(fname='C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\site-packages\\matplotlib\\mpl-data\\fonts\\ttf\\DejaVuSans-BoldOblique.ttf', name='DejaVu Sans', style='oblique', variant='normal', weight=700, stretch='normal', size='scalable')) = 11.335
2025-07-23 19:47:52,844 - DEBUG - findfont: score(FontEntry(fname='C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\site-packages\\matplotlib\\mpl-data\\fonts\\ttf\\STIXNonUniIta.ttf', name='STIXNonUnicode', style='italic', variant='normal', weight=400, stretch='normal', size='scalable')) = 11.05
2025-07-23 19:47:52,844 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\VerdanaPro-Regular.ttf', name='Verdana Pro', style='normal', variant='normal', weight=400, stretch='normal', size='scalable')) = 10.05
2025-07-23 19:47:52,844 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\ArialNova.ttf', name='Arial Nova', style='normal', variant='normal', weight=400, stretch='normal', size='scalable')) = 10.05
2025-07-23 19:47:52,844 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\dokchamp.ttf', name='DokChampa', style='normal', variant='normal', weight=400, stretch='normal', size='scalable')) = 10.05
2025-07-23 19:47:52,845 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\ROCKI.TTF', name='Rockwell', style='italic', variant='normal', weight=400, stretch='normal', size='scalable')) = 11.05
2025-07-23 19:47:52,845 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\upcebi.ttf', name='EucrosiaUPC', style='italic', variant='normal', weight=700, stretch='normal', size='scalable')) = 11.335
2025-07-23 19:47:52,846 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\LFAXDI.TTF', name='Lucida Fax', style='italic', variant='normal', weight=600, stretch='normal', size='scalable')) = 11.24
2025-07-23 19:47:52,846 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\BOD_I.TTF', name='Bodoni MT', style='italic', variant='normal', weight=400, stretch='normal', size='scalable')) = 11.05
2025-07-23 19:47:52,847 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\comicz.ttf', name='Comic Sans MS', style='italic', variant='normal', weight=700, stretch='normal', size='scalable')) = 11.335
2025-07-23 19:47:52,847 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\VerdanaPro-CondRegular.ttf', name='Verdana Pro', style='normal', variant='normal', weight=400, stretch='condensed', size='scalable')) = 10.25
2025-07-23 19:47:52,847 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\GillSansLightItNova.ttf', name='Gill Sans Nova', style='italic', variant='normal', weight=300, stretch='normal', size='scalable')) = 11.145
2025-07-23 19:47:52,847 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\NotoSansTC-VF.ttf', name='Noto Sans TC', style='normal', variant='normal', weight=100, stretch='normal', size='scalable')) = 10.335
2025-07-23 19:47:52,847 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\upcll.ttf', name='LilyUPC', style='normal', variant='normal', weight=400, stretch='normal', size='scalable')) = 10.05
2025-07-23 19:47:52,848 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\REFSAN.TTF', name='MS Reference Sans Serif', style='normal', variant='normal', weight=400, stretch='normal', size='scalable')) = 10.05
2025-07-23 19:47:52,848 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\consolai.ttf', name='Consolas', style='italic', variant='normal', weight=400, stretch='normal', size='scalable')) = 11.05
2025-07-23 19:47:52,849 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\verdana.ttf', name='Verdana', style='normal', variant='normal', weight=400, stretch='normal', size='scalable')) = 10.05
2025-07-23 19:47:52,849 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\SCHLBKB.TTF', name='Century Schoolbook', style='normal', variant='normal', weight=700, stretch='normal', size='scalable')) = 10.335
2025-07-23 19:47:52,849 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\SNAP____.TTF', name='Snap ITC', style='normal', variant='normal', weight=400, stretch='normal', size='scalable')) = 10.05
2025-07-23 19:47:52,849 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\upckbi.ttf', name='KodchiangUPC', style='italic', variant='normal', weight=700, stretch='normal', size='scalable')) = 11.335
2025-07-23 19:47:52,849 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\latha.ttf', name='Latha', style='normal', variant='normal', weight=400, stretch='normal', size='scalable')) = 10.05
2025-07-23 19:47:52,850 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\monbaiti.ttf', name='Mongolian Baiti', style='normal', variant='normal', weight=400, stretch='normal', size='scalable')) = 10.05
2025-07-23 19:47:52,850 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\JOKERMAN.TTF', name='Jokerman', style='normal', variant='normal', weight=400, stretch='normal', size='scalable')) = 10.05
2025-07-23 19:47:52,850 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\mangalb.ttf', name='Mangal', style='normal', variant='normal', weight=700, stretch='normal', size='scalable')) = 10.335
2025-07-23 19:47:52,850 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\GeorgiaPro-CondItalic.ttf', name='Georgia Pro', style='italic', variant='normal', weight=400, stretch='condensed', size='scalable')) = 11.25
2025-07-23 19:47:52,851 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\arialni.ttf', name='Arial', style='italic', variant='normal', weight=400, stretch='condensed', size='scalable')) = 1.25
2025-07-23 19:47:52,851 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\arialnb.ttf', name='Arial', style='normal', variant='normal', weight=700, stretch='condensed', size='scalable')) = 0.5349999999999999
2025-07-23 19:47:52,851 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\ANTQUABI.TTF', name='Book Antiqua', style='italic', variant='normal', weight=700, stretch='normal', size='scalable')) = 11.335
2025-07-23 19:47:52,852 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\FTLTLT.TTF', name='Footlight MT Light', style='normal', variant='normal', weight=300, stretch='normal', size='scalable')) = 10.145
2025-07-23 19:47:52,852 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\Shonarb.ttf', name='Shonar Bangla', style='normal', variant='normal', weight=700, stretch='normal', size='scalable')) = 10.335
2025-07-23 19:47:52,852 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\msyhbd.ttc', name='Microsoft YaHei', style='normal', variant='normal', weight=700, stretch='normal', size='scalable')) = 10.335
2025-07-23 19:47:52,853 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\GIL_____.TTF', name='Gill Sans MT', style='normal', variant='normal', weight=400, stretch='normal', size='scalable')) = 10.05
2025-07-23 19:47:52,853 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\FORTE.TTF', name='Forte', style='normal', variant='normal', weight=400, stretch='normal', size='scalable')) = 10.05
2025-07-23 19:47:52,853 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\cambriai.ttf', name='Cambria', style='italic', variant='normal', weight=400, stretch='normal', size='scalable')) = 11.05
2025-07-23 19:47:52,855 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\arial.ttf', name='Arial', style='normal', variant='normal', weight=400, stretch='normal', size='scalable')) = 0.05
2025-07-23 19:47:52,856 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\GillSansItNova.ttf', name='Gill Sans Nova', style='italic', variant='normal', weight=500, stretch='normal', size='scalable')) = 11.145
2025-07-23 19:47:52,856 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\kokilai.ttf', name='Kokila', style='italic', variant='normal', weight=400, stretch='normal', size='scalable')) = 11.05
2025-07-23 19:47:52,856 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\tunga.ttf', name='Tunga', style='normal', variant='normal', weight=400, stretch='normal', size='scalable')) = 10.05
2025-07-23 19:47:52,856 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\nyala.ttf', name='Nyala', style='normal', variant='normal', weight=400, stretch='normal', size='scalable')) = 10.05
2025-07-23 19:47:52,856 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\AGENCYR.TTF', name='Agency FB', style='normal', variant='normal', weight=400, stretch='normal', size='scalable')) = 10.05
2025-07-23 19:47:52,856 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\framdit.ttf', name='Franklin Gothic Medium', style='italic', variant='normal', weight=400, stretch='normal', size='scalable')) = 11.05
2025-07-23 19:47:52,857 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\bahnschrift.ttf', name='Bahnschrift', style='normal', variant='normal', weight=400, stretch='normal', size='scalable')) = 10.05
2025-07-23 19:47:52,857 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\GeorgiaPro-CondBlack.ttf', name='Georgia Pro', style='normal', variant='normal', weight=900, stretch='condensed', size='scalable')) = 10.725
2025-07-23 19:47:52,857 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\GillSansCondNova.ttf', name='Gill Sans Nova', style='normal', variant='normal', weight=500, stretch='condensed', size='scalable')) = 10.344999999999999
2025-07-23 19:47:52,857 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\GillSansNova.ttf', name='Gill Sans Nova', style='normal', variant='normal', weight=500, stretch='normal', size='scalable')) = 10.145
2025-07-23 19:47:52,857 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\msyhl.ttc', name='Microsoft YaHei', style='normal', variant='normal', weight=290, stretch='normal', size='scalable')) = 10.1545
2025-07-23 19:47:52,857 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\RockwellNova-Italic.ttf', name='Rockwell Nova', style='italic', variant='normal', weight=400, stretch='normal', size='scalable')) = 11.05
2025-07-23 19:47:52,858 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\BOD_BI.TTF', name='Bodoni MT', style='italic', variant='normal', weight=700, stretch='normal', size='scalable')) = 11.335
2025-07-23 19:47:52,858 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\COOPBL.TTF', name='Cooper Black', style='normal', variant='normal', weight=400, stretch='normal', size='scalable')) = 10.05
2025-07-23 19:47:52,858 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\CALIFI.TTF', name='Californian FB', style='italic', variant='normal', weight=400, stretch='normal', size='scalable')) = 11.05
2025-07-23 19:47:52,858 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\upclbi.ttf', name='LilyUPC', style='italic', variant='normal', weight=700, stretch='normal', size='scalable')) = 11.335
2025-07-23 19:47:52,858 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\RockwellNovaCond.ttf', name='Rockwell Nova', style='normal', variant='normal', weight=400, stretch='condensed', size='scalable')) = 10.25
2025-07-23 19:47:52,858 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\VerdanaPro-SemiBoldItalic.ttf', name='Verdana Pro', style='italic', variant='normal', weight=600, stretch='normal', size='scalable')) = 11.24
2025-07-23 19:47:52,859 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\GOUDOSI.TTF', name='Goudy Old Style', style='italic', variant='normal', weight=400, stretch='normal', size='scalable')) = 11.05
2025-07-23 19:47:52,859 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\andlso.ttf', name='Andalus', style='normal', variant='normal', weight=400, stretch='normal', size='scalable')) = 10.05
2025-07-23 19:47:52,859 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\SimsunExtG.ttf', name='SimSun-ExtG', style='normal', variant='normal', weight=400, stretch='normal', size='scalable')) = 10.05
2025-07-23 19:47:52,859 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\GILB____.TTF', name='Gill Sans MT', style='normal', variant='normal', weight=700, stretch='normal', size='scalable')) = 10.335
2025-07-23 19:47:52,859 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\kalingab.ttf', name='Kalinga', style='normal', variant='normal', weight=700, stretch='normal', size='scalable')) = 10.335
2025-07-23 19:47:52,860 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\Inkfree.ttf', name='Ink Free', style='normal', variant='normal', weight=400, stretch='normal', size='scalable')) = 10.05
2025-07-23 19:47:52,860 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\BSSYM7.TTF', name='Bookshelf Symbol 7', style='normal', variant='normal', weight=400, stretch='normal', size='scalable')) = 10.05
2025-07-23 19:47:52,860 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\INFROMAN.TTF', name='Informal Roman', style='normal', variant='normal', weight=400, stretch='normal', size='scalable')) = 10.05
2025-07-23 19:47:52,860 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\GillSansCondExtraItNova.ttf', name='Gill Sans Nova', style='italic', variant='normal', weight=800, stretch='condensed', size='scalable')) = 11.629999999999999
2025-07-23 19:47:52,861 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\BOD_CR.TTF', name='Bodoni MT', style='normal', variant='normal', weight=400, stretch='condensed', size='scalable')) = 10.25
2025-07-23 19:47:52,861 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\ITCBLKAD.TTF', name='Blackadder ITC', style='normal', variant='normal', weight=400, stretch='normal', size='scalable')) = 10.05
2025-07-23 19:47:52,861 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\ahronbd.ttf', name='Aharoni', style='normal', variant='normal', weight=700, stretch='normal', size='scalable')) = 10.335
2025-07-23 19:47:52,861 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\UDDigiKyokashoN-R.ttc', name='UD Digi Kyokasho N-R', style='normal', variant='normal', weight=400, stretch='normal', size='scalable')) = 10.05
2025-07-23 19:47:52,861 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\vijaya.ttf', name='Vijaya', style='normal', variant='normal', weight=400, stretch='normal', size='scalable')) = 10.05
2025-07-23 19:47:52,862 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\LaoUI.ttf', name='Lao UI', style='normal', variant='normal', weight=400, stretch='normal', size='scalable')) = 10.05
2025-07-23 19:47:52,862 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\GillSansUltraBoNova.ttf', name='Gill Sans Nova', style='normal', variant='normal', weight=900, stretch='normal', size='scalable')) = 10.525
2025-07-23 19:47:52,862 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\vijayab.ttf', name='Vijaya', style='normal', variant='normal', weight=700, stretch='normal', size='scalable')) = 10.335
2025-07-23 19:47:52,862 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\PRISTINA.TTF', name='Pristina', style='normal', variant='normal', weight=400, stretch='normal', size='scalable')) = 10.05
2025-07-23 19:47:52,862 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\msjh.ttc', name='Microsoft JhengHei', style='normal', variant='normal', weight=400, stretch='normal', size='scalable')) = 10.05
2025-07-23 19:47:52,862 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\mriamc.ttf', name='Miriam Fixed', style='normal', variant='normal', weight=400, stretch='normal', size='scalable')) = 10.05
2025-07-23 19:47:52,863 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\segoeuib.ttf', name='Segoe UI', style='normal', variant='normal', weight=700, stretch='normal', size='scalable')) = 10.335
2025-07-23 19:47:52,863 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\frank.ttf', name='FrankRuehl', style='normal', variant='normal', weight=400, stretch='normal', size='scalable')) = 10.05
2025-07-23 19:47:52,863 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\Dengb.ttf', name='DengXian', style='normal', variant='normal', weight=700, stretch='normal', size='scalable')) = 10.335
2025-07-23 19:47:52,863 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\LSANSDI.TTF', name='Lucida Sans', style='italic', variant='normal', weight=600, stretch='normal', size='scalable')) = 11.24
2025-07-23 19:47:52,863 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\CALISTBI.TTF', name='Calisto MT', style='italic', variant='normal', weight=700, stretch='normal', size='scalable')) = 11.335
2025-07-23 19:47:52,864 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\CALISTB.TTF', name='Calisto MT', style='normal', variant='normal', weight=700, stretch='normal', size='scalable')) = 10.335
2025-07-23 19:47:52,864 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\rod.ttf', name='Rod', style='normal', variant='normal', weight=400, stretch='normal', size='scalable')) = 10.05
2025-07-23 19:47:52,864 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\BROADW.TTF', name='Broadway', style='normal', variant='normal', weight=400, stretch='normal', size='scalable')) = 10.05
2025-07-23 19:47:52,864 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\lvnmbd.ttf', name='Levenim MT', style='normal', variant='normal', weight=700, stretch='normal', size='scalable')) = 10.335
2025-07-23 19:47:52,865 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\GILSANUB.TTF', name='Gill Sans Ultra Bold', style='normal', variant='normal', weight=400, stretch='normal', size='scalable')) = 10.05
2025-07-23 19:47:52,865 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\VerdanaPro-SemiBold.ttf', name='Verdana Pro', style='normal', variant='normal', weight=600, stretch='normal', size='scalable')) = 10.24
2025-07-23 19:47:52,865 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\GOTHICBI.TTF', name='Century Gothic', style='italic', variant='normal', weight=700, stretch='normal', size='scalable')) = 11.335
2025-07-23 19:47:52,865 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\simpo.ttf', name='Simplified Arabic', style='normal', variant='normal', weight=400, stretch='normal', size='scalable')) = 10.05
2025-07-23 19:47:52,865 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\RAVIE.TTF', name='Ravie', style='normal', variant='normal', weight=400, stretch='normal', size='scalable')) = 10.05
2025-07-23 19:47:52,866 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\majalla.ttf', name='Sakkal Majalla', style='normal', variant='normal', weight=400, stretch='normal', size='scalable')) = 10.05
2025-07-23 19:47:52,866 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\NIAGSOL.TTF', name='Niagara Solid', style='normal', variant='normal', weight=400, stretch='normal', size='scalable')) = 10.05
2025-07-23 19:47:52,866 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\CHILLER.TTF', name='Chiller', style='normal', variant='normal', weight=400, stretch='normal', size='scalable')) = 10.05
2025-07-23 19:47:52,866 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\consola.ttf', name='Consolas', style='normal', variant='normal', weight=400, stretch='normal', size='scalable')) = 10.05
2025-07-23 19:47:52,866 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\LBRITED.TTF', name='Lucida Bright', style='normal', variant='normal', weight=600, stretch='normal', size='scalable')) = 10.24
2025-07-23 19:47:52,866 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\BRLNSR.TTF', name='Berlin Sans FB', style='normal', variant='normal', weight=400, stretch='normal', size='scalable')) = 10.05
2025-07-23 19:47:52,867 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\calibrili.ttf', name='Calibri', style='italic', variant='normal', weight=300, stretch='normal', size='scalable')) = 11.145
2025-07-23 19:47:52,867 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\COPRGTB.TTF', name='Copperplate Gothic Bold', style='normal', variant='normal', weight=400, stretch='normal', size='scalable')) = 10.05
2025-07-23 19:47:52,867 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\simsun.ttc', name='SimSun', style='normal', variant='normal', weight=400, stretch='normal', size='scalable')) = 10.05
2025-07-23 19:47:52,867 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\MTCORSVA.TTF', name='Monotype Corsiva', style='normal', variant='normal', weight=400, stretch='normal', size='scalable')) = 10.05
2025-07-23 19:47:52,867 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\RockwellNovaCond-Light.ttf', name='Rockwell Nova', style='normal', variant='normal', weight=300, stretch='condensed', size='scalable')) = 10.344999999999999
2025-07-23 19:47:52,867 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\Candaral.ttf', name='Candara', style='normal', variant='normal', weight=400, stretch='normal', size='scalable')) = 10.05
2025-07-23 19:47:52,867 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\Candara.ttf', name='Candara', style='normal', variant='normal', weight=400, stretch='normal', size='scalable')) = 10.05
2025-07-23 19:47:52,868 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\seguibli.ttf', name='Segoe UI', style='italic', variant='normal', weight=900, stretch='normal', size='scalable')) = 11.525
2025-07-23 19:47:52,868 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\cambriaz.ttf', name='Cambria', style='italic', variant='normal', weight=700, stretch='normal', size='scalable')) = 11.335
2025-07-23 19:47:52,868 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\upcjl.ttf', name='JasmineUPC', style='normal', variant='normal', weight=400, stretch='normal', size='scalable')) = 10.05
2025-07-23 19:47:52,868 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\BOOKOSB.TTF', name='Bookman Old Style', style='normal', variant='normal', weight=600, stretch='normal', size='scalable')) = 10.24
2025-07-23 19:47:52,869 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\JUICE___.TTF', name='Juice ITC', style='normal', variant='normal', weight=400, stretch='normal', size='scalable')) = 10.05
2025-07-23 19:47:52,869 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\NotoSansHK-VF.ttf', name='Noto Sans HK', style='normal', variant='normal', weight=100, stretch='normal', size='scalable')) = 10.335
2025-07-23 19:47:52,869 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\Shonar.ttf', name='Shonar Bangla', style='normal', variant='normal', weight=400, stretch='normal', size='scalable')) = 10.05
2025-07-23 19:47:52,869 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\nrkis.ttf', name='Narkisim', style='normal', variant='normal', weight=400, stretch='normal', size='scalable')) = 10.05
2025-07-23 19:47:52,869 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\mingliub.ttc', name='MingLiU-ExtB', style='normal', variant='normal', weight=400, stretch='normal', size='scalable')) = 10.05
2025-07-23 19:47:52,870 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\GLSNECB.TTF', name='Gill Sans MT Ext Condensed Bold', style='normal', variant='normal', weight=400, stretch='condensed', size='scalable')) = 10.25
2025-07-23 19:47:52,870 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\BOOKOSI.TTF', name='Bookman Old Style', style='italic', variant='normal', weight=300, stretch='normal', size='scalable')) = 11.145
2025-07-23 19:47:52,870 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\HARLOWSI.TTF', name='Harlow Solid Italic', style='italic', variant='normal', weight=400, stretch='normal', size='scalable')) = 11.05
2025-07-23 19:47:52,870 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\VerdanaPro-CondBoldItalic.ttf', name='Verdana Pro', style='italic', variant='normal', weight=700, stretch='condensed', size='scalable')) = 11.535
2025-07-23 19:47:52,870 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\GillSansBoItNova.ttf', name='Gill Sans Nova', style='italic', variant='normal', weight=700, stretch='normal', size='scalable')) = 11.335
2025-07-23 19:47:52,870 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\l_10646.ttf', name='Lucida Sans Unicode', style='normal', variant='normal', weight=400, stretch='normal', size='scalable')) = 10.05
2025-07-23 19:47:52,871 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\gishabd.ttf', name='Gisha', style='normal', variant='normal', weight=700, stretch='normal', size='scalable')) = 10.335
2025-07-23 19:47:52,871 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\BOD_B.TTF', name='Bodoni MT', style='normal', variant='normal', weight=700, stretch='normal', size='scalable')) = 10.335
2025-07-23 19:47:52,871 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\utsaah.ttf', name='Utsaah', style='normal', variant='normal', weight=400, stretch='normal', size='scalable')) = 10.05
2025-07-23 19:47:52,871 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\utsaahb.ttf', name='Utsaah', style='normal', variant='normal', weight=700, stretch='normal', size='scalable')) = 10.335
2025-07-23 19:47:52,871 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\TCCEB.TTF', name='Tw Cen MT Condensed Extra Bold', style='normal', variant='normal', weight=400, stretch='condensed', size='scalable')) = 10.25
2025-07-23 19:47:52,871 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\GeorgiaPro-CondLightItalic.ttf', name='Georgia Pro', style='italic', variant='normal', weight=300, stretch='condensed', size='scalable')) = 11.344999999999999
2025-07-23 19:47:52,872 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\verdanai.ttf', name='Verdana', style='italic', variant='normal', weight=400, stretch='normal', size='scalable')) = 11.05
2025-07-23 19:47:52,872 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\ntailu.ttf', name='Microsoft New Tai Lue', style='normal', variant='normal', weight=400, stretch='normal', size='scalable')) = 10.05
2025-07-23 19:47:52,872 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\SitkaZ.ttc', name='Sitka Small', style='italic', variant='normal', weight=700, stretch='normal', size='scalable')) = 11.335
2025-07-23 19:47:52,872 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\LATINWD.TTF', name='Wide Latin', style='normal', variant='normal', weight=400, stretch='expanded', size='scalable')) = 10.25
2025-07-23 19:47:52,872 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\segoeprb.ttf', name='Segoe Print', style='normal', variant='normal', weight=700, stretch='normal', size='scalable')) = 10.335
2025-07-23 19:47:52,872 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\segoeui.ttf', name='Segoe UI', style='normal', variant='normal', weight=400, stretch='normal', size='scalable')) = 10.05
2025-07-23 19:47:52,872 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\COLONNA.TTF', name='Colonna MT', style='normal', variant='normal', weight=400, stretch='normal', size='scalable')) = 10.05
2025-07-23 19:47:52,873 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\Gabriola.ttf', name='Gabriola', style='normal', variant='normal', weight=400, stretch='normal', size='scalable')) = 10.05
2025-07-23 19:47:52,873 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\UDDigiKyokashoN-B.ttc', name='UD Digi Kyokasho N-B', style='normal', variant='normal', weight=700, stretch='normal', size='scalable')) = 10.335
2025-07-23 19:47:52,873 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\phagspa.ttf', name='Microsoft PhagsPa', style='normal', variant='normal', weight=400, stretch='normal', size='scalable')) = 10.05
2025-07-23 19:47:52,873 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\FRSCRIPT.TTF', name='French Script MT', style='normal', variant='normal', weight=400, stretch='normal', size='scalable')) = 10.05
2025-07-23 19:47:52,873 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\NotoSansJP-VF.ttf', name='Noto Sans JP', style='normal', variant='normal', weight=100, stretch='normal', size='scalable')) = 10.335
2025-07-23 19:47:52,873 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\GeorgiaPro-CondSemiBoldItalic.ttf', name='Georgia Pro', style='italic', variant='normal', weight=600, stretch='condensed', size='scalable')) = 11.44
2025-07-23 19:47:52,874 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\ArialNova-Light.ttf', name='Arial Nova', style='normal', variant='normal', weight=300, stretch='normal', size='scalable')) = 10.145
2025-07-23 19:47:52,874 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\davidbd.ttf', name='David', style='normal', variant='normal', weight=700, stretch='normal', size='scalable')) = 10.335
2025-07-23 19:47:52,874 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\GeorgiaPro-CondLight.ttf', name='Georgia Pro', style='normal', variant='normal', weight=300, stretch='condensed', size='scalable')) = 10.344999999999999
2025-07-23 19:47:52,874 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\GOTHICI.TTF', name='Century Gothic', style='italic', variant='normal', weight=400, stretch='normal', size='scalable')) = 11.05
2025-07-23 19:47:52,874 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\VLADIMIR.TTF', name='Vladimir Script', style='normal', variant='normal', weight=400, stretch='normal', size='scalable')) = 10.05
2025-07-23 19:47:52,874 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\upcjbi.ttf', name='JasmineUPC', style='italic', variant='normal', weight=700, stretch='normal', size='scalable')) = 11.335
2025-07-23 19:47:52,874 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\HPSimplified_It.ttf', name='HP Simplified', style='italic', variant='normal', weight=400, stretch='normal', size='scalable')) = 11.05
2025-07-23 19:47:52,876 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\GeorgiaPro-Black.ttf', name='Georgia Pro', style='normal', variant='normal', weight=900, stretch='normal', size='scalable')) = 10.525
2025-07-23 19:47:52,876 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\ERASMD.TTF', name='Eras Medium ITC', style='normal', variant='normal', weight=400, stretch='normal', size='scalable')) = 10.05
2025-07-23 19:47:52,876 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\VerdanaPro-LightItalic.ttf', name='Verdana Pro', style='italic', variant='normal', weight=300, stretch='normal', size='scalable')) = 11.145
2025-07-23 19:47:52,876 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\yumindb.ttf', name='Yu Mincho', style='normal', variant='normal', weight=600, stretch='normal', size='scalable')) = 10.24
2025-07-23 19:47:52,876 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\BAUHS93.TTF', name='Bauhaus 93', style='normal', variant='normal', weight=400, stretch='normal', size='scalable')) = 10.05
2025-07-23 19:47:52,876 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\mmrtextb.ttf', name='Myanmar Text', style='normal', variant='normal', weight=700, stretch='normal', size='scalable')) = 10.335
2025-07-23 19:47:52,877 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\OCRAEXT.TTF', name='OCR A Extended', style='normal', variant='normal', weight=400, stretch='expanded', size='scalable')) = 10.25
2025-07-23 19:47:52,877 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\kalinga.ttf', name='Kalinga', style='normal', variant='normal', weight=400, stretch='normal', size='scalable')) = 10.05
2025-07-23 19:47:52,877 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\MISTRAL.TTF', name='Mistral', style='normal', variant='normal', weight=400, stretch='normal', size='scalable')) = 10.05
2025-07-23 19:47:52,877 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\simfang.ttf', name='FangSong', style='normal', variant='normal', weight=400, stretch='normal', size='scalable')) = 10.05
2025-07-23 19:47:52,878 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\VerdanaPro-Bold.ttf', name='Verdana Pro', style='normal', variant='normal', weight=700, stretch='normal', size='scalable')) = 10.335
2025-07-23 19:47:52,878 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\upcki.ttf', name='KodchiangUPC', style='italic', variant='normal', weight=400, stretch='normal', size='scalable')) = 11.05
2025-07-23 19:47:52,878 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\ntailub.ttf', name='Microsoft New Tai Lue', style='normal', variant='normal', weight=700, stretch='normal', size='scalable')) = 10.335
2025-07-23 19:47:52,878 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\ALGER.TTF', name='Algerian', style='normal', variant='normal', weight=400, stretch='normal', size='scalable')) = 10.05
2025-07-23 19:47:52,878 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\RockwellNova-LightItalic.ttf', name='Rockwell Nova', style='italic', variant='normal', weight=300, stretch='normal', size='scalable')) = 11.145
2025-07-23 19:47:52,879 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\upcdi.ttf', name='DilleniaUPC', style='italic', variant='normal', weight=400, stretch='normal', size='scalable')) = 11.05
2025-07-23 19:47:52,879 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\GillSansCondExtraNova.ttf', name='Gill Sans Nova', style='normal', variant='normal', weight=800, stretch='condensed', size='scalable')) = 10.629999999999999
2025-07-23 19:47:52,879 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\ARIALUNI.TTF', name='Arial Unicode MS', style='normal', variant='normal', weight=400, stretch='normal', size='scalable')) = 10.05
2025-07-23 19:47:52,879 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\palai.ttf', name='Palatino Linotype', style='italic', variant='normal', weight=400, stretch='normal', size='scalable')) = 11.05
2025-07-23 19:47:52,879 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\ROCC____.TTF', name='Rockwell Condensed', style='normal', variant='normal', weight=400, stretch='condensed', size='scalable')) = 10.25
2025-07-23 19:47:52,879 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\GeorgiaPro-CondRegular.ttf', name='Georgia Pro', style='normal', variant='normal', weight=400, stretch='condensed', size='scalable')) = 10.25
2025-07-23 19:47:52,880 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\LFAX.TTF', name='Lucida Fax', style='normal', variant='normal', weight=400, stretch='normal', size='scalable')) = 10.05
2025-07-23 19:47:52,880 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\TCCB____.TTF', name='Tw Cen MT Condensed', style='normal', variant='normal', weight=700, stretch='condensed', size='scalable')) = 10.535
2025-07-23 19:47:52,880 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\tungab.ttf', name='Tunga', style='normal', variant='normal', weight=700, stretch='normal', size='scalable')) = 10.335
2025-07-23 19:47:52,880 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\FRAMDCN.TTF', name='Franklin Gothic Medium Cond', style='normal', variant='normal', weight=400, stretch='condensed', size='scalable')) = 10.25
2025-07-23 19:47:52,880 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\kartika.ttf', name='Kartika', style='normal', variant='normal', weight=400, stretch='normal', size='scalable')) = 10.05
2025-07-23 19:47:52,880 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\PAPYRUS.TTF', name='Papyrus', style='normal', variant='normal', weight=400, stretch='normal', size='scalable')) = 10.05
2025-07-23 19:47:52,881 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\LFAXI.TTF', name='Lucida Fax', style='italic', variant='normal', weight=400, stretch='normal', size='scalable')) = 11.05
2025-07-23 19:47:52,881 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\BERNHC.TTF', name='Bernard MT Condensed', style='normal', variant='normal', weight=400, stretch='condensed', size='scalable')) = 10.25
2025-07-23 19:47:52,881 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\GeorgiaPro-SemiBold.ttf', name='Georgia Pro', style='normal', variant='normal', weight=600, stretch='normal', size='scalable')) = 10.24
2025-07-23 19:47:52,881 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\simsunb.ttf', name='SimSun-ExtB', style='normal', variant='normal', weight=400, stretch='normal', size='scalable')) = 10.05
2025-07-23 19:47:52,881 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\cambriab.ttf', name='Cambria', style='normal', variant='normal', weight=700, stretch='normal', size='scalable')) = 10.335
2025-07-23 19:47:52,881 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\ArialNovaCond-Light.ttf', name='Arial Nova', style='normal', variant='normal', weight=300, stretch='condensed', size='scalable')) = 10.344999999999999
2025-07-23 19:47:52,881 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\ArialNova-BoldItalic.ttf', name='Arial Nova', style='italic', variant='normal', weight=700, stretch='normal', size='scalable')) = 11.335
2025-07-23 19:47:52,881 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\yuminl.ttf', name='Yu Mincho', style='normal', variant='normal', weight=300, stretch='normal', size='scalable')) = 10.145
2025-07-23 19:47:52,882 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\BRLNSDB.TTF', name='Berlin Sans FB Demi', style='normal', variant='normal', weight=700, stretch='normal', size='scalable')) = 10.335
2025-07-23 19:47:52,882 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\taile.ttf', name='Microsoft Tai Le', style='normal', variant='normal', weight=400, stretch='normal', size='scalable')) = 10.05
2025-07-23 19:47:52,882 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\courbd.ttf', name='Courier New', style='normal', variant='normal', weight=700, stretch='normal', size='scalable')) = 10.335
2025-07-23 19:47:52,882 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\gadugib.ttf', name='Gadugi', style='normal', variant='normal', weight=700, stretch='normal', size='scalable')) = 10.335
2025-07-23 19:47:52,883 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\mvboli.ttf', name='MV Boli', style='normal', variant='normal', weight=400, stretch='normal', size='scalable')) = 10.05
2025-07-23 19:47:52,883 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\upcfi.ttf', name='FreesiaUPC', style='italic', variant='normal', weight=400, stretch='normal', size='scalable')) = 11.05
2025-07-23 19:47:52,883 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\VerdanaPro-CondBlackItalic.ttf', name='Verdana Pro', style='italic', variant='normal', weight=900, stretch='condensed', size='scalable')) = 11.725
2025-07-23 19:47:52,883 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\consolaz.ttf', name='Consolas', style='italic', variant='normal', weight=700, stretch='normal', size='scalable')) = 11.335
2025-07-23 19:47:52,883 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\GeorgiaPro-Italic.ttf', name='Georgia Pro', style='italic', variant='normal', weight=400, stretch='normal', size='scalable')) = 11.05
2025-07-23 19:47:52,883 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\PALSCRI.TTF', name='Palace Script MT', style='normal', variant='normal', weight=400, stretch='normal', size='scalable')) = 10.05
2025-07-23 19:47:52,883 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\PERTIBD.TTF', name='Perpetua Titling MT', style='normal', variant='normal', weight=700, stretch='normal', size='scalable')) = 10.335
2025-07-23 19:47:52,884 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\segoepr.ttf', name='Segoe Print', style='normal', variant='normal', weight=400, stretch='normal', size='scalable')) = 10.05
2025-07-23 19:47:52,884 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\ROCK.TTF', name='Rockwell', style='normal', variant='normal', weight=400, stretch='normal', size='scalable')) = 10.05
2025-07-23 19:47:52,884 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\GILI____.TTF', name='Gill Sans MT', style='italic', variant='normal', weight=400, stretch='normal', size='scalable')) = 11.05
2025-07-23 19:47:52,885 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\upcib.ttf', name='IrisUPC', style='normal', variant='normal', weight=700, stretch='normal', size='scalable')) = 10.335
2025-07-23 19:47:52,885 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\GillSansCondBoItNova.ttf', name='Gill Sans Nova', style='italic', variant='normal', weight=700, stretch='condensed', size='scalable')) = 11.535
2025-07-23 19:47:52,886 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\upcli.ttf', name='LilyUPC', style='italic', variant='normal', weight=400, stretch='normal', size='scalable')) = 11.05
2025-07-23 19:47:52,886 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\NirmalaB.ttf', name='Nirmala UI', style='normal', variant='normal', weight=700, stretch='normal', size='scalable')) = 10.335
2025-07-23 19:47:52,886 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\PARCHM.TTF', name='Parchment', style='normal', variant='normal', weight=400, stretch='normal', size='scalable')) = 10.05
2025-07-23 19:47:52,886 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\FRADMIT.TTF', name='Franklin Gothic Demi', style='italic', variant='normal', weight=400, stretch='normal', size='scalable')) = 11.05
2025-07-23 19:47:52,886 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\arabtype.ttf', name='Arabic Typesetting', style='normal', variant='normal', weight=400, stretch='normal', size='scalable')) = 10.05
2025-07-23 19:47:52,887 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\TEMPSITC.TTF', name='Tempus Sans ITC', style='normal', variant='normal', weight=400, stretch='normal', size='scalable')) = 10.05
2025-07-23 19:47:52,887 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\cordia.ttc', name='Cordia New', style='normal', variant='normal', weight=400, stretch='normal', size='scalable')) = 10.05
2025-07-23 19:47:52,887 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\ITCEDSCR.TTF', name='Edwardian Script ITC', style='normal', variant='normal', weight=400, stretch='normal', size='scalable')) = 10.05
2025-07-23 19:47:52,887 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\MAGNETOB.TTF', name='Magneto', style='normal', variant='normal', weight=700, stretch='normal', size='scalable')) = 10.335
2025-07-23 19:47:52,887 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\seguibl.ttf', name='Segoe UI', style='normal', variant='normal', weight=900, stretch='normal', size='scalable')) = 10.525
2025-07-23 19:47:52,887 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\YuGothB.ttc', name='Yu Gothic', style='normal', variant='normal', weight=700, stretch='normal', size='scalable')) = 10.335
2025-07-23 19:47:52,887 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\VerdanaPro-Light.ttf', name='Verdana Pro', style='normal', variant='normal', weight=300, stretch='normal', size='scalable')) = 10.145
2025-07-23 19:47:52,888 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\NotoSansSC-VF.ttf', name='Noto Sans SC', style='normal', variant='normal', weight=100, stretch='normal', size='scalable')) = 10.335
2025-07-23 19:47:52,888 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\micross.ttf', name='Microsoft Sans Serif', style='normal', variant='normal', weight=400, stretch='normal', size='scalable')) = 10.05
2025-07-23 19:47:52,888 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\FRADMCN.TTF', name='Franklin Gothic Demi Cond', style='normal', variant='normal', weight=400, stretch='condensed', size='scalable')) = 10.25
2025-07-23 19:47:52,889 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\upcel.ttf', name='EucrosiaUPC', style='normal', variant='normal', weight=400, stretch='normal', size='scalable')) = 10.05
2025-07-23 19:47:52,889 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\BOD_BLAI.TTF', name='Bodoni MT', style='italic', variant='normal', weight=900, stretch='normal', size='scalable')) = 11.525
2025-07-23 19:47:52,889 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\CALIFR.TTF', name='Californian FB', style='normal', variant='normal', weight=400, stretch='normal', size='scalable')) = 10.05
2025-07-23 19:47:52,889 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\VIVALDII.TTF', name='Vivaldi', style='italic', variant='normal', weight=400, stretch='normal', size='scalable')) = 11.05
2025-07-23 19:47:52,889 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\GARABD.TTF', name='Garamond', style='normal', variant='normal', weight=700, stretch='normal', size='scalable')) = 10.335
2025-07-23 19:47:52,889 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\upckb.ttf', name='KodchiangUPC', style='normal', variant='normal', weight=700, stretch='normal', size='scalable')) = 10.335
2025-07-23 19:47:52,889 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\FRABK.TTF', name='Franklin Gothic Book', style='normal', variant='normal', weight=400, stretch='normal', size='scalable')) = 10.05
2025-07-23 19:47:52,890 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\georgiai.ttf', name='Georgia', style='italic', variant='normal', weight=400, stretch='normal', size='scalable')) = 11.05
2025-07-23 19:47:52,890 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\COPRGTL.TTF', name='Copperplate Gothic Light', style='normal', variant='normal', weight=400, stretch='normal', size='scalable')) = 10.05
2025-07-23 19:47:52,890 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\VerdanaPro-BlackItalic.ttf', name='Verdana Pro', style='italic', variant='normal', weight=900, stretch='normal', size='scalable')) = 11.525
2025-07-23 19:47:52,890 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\upcji.ttf', name='JasmineUPC', style='italic', variant='normal', weight=400, stretch='normal', size='scalable')) = 11.05
2025-07-23 19:47:52,890 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\GillSansCondItNova.ttf', name='Gill Sans Nova', style='italic', variant='normal', weight=500, stretch='condensed', size='scalable')) = 11.344999999999999
2025-07-23 19:47:52,891 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\GeorgiaPro-Bold.ttf', name='Georgia Pro', style='normal', variant='normal', weight=700, stretch='normal', size='scalable')) = 10.335
2025-07-23 19:47:52,891 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\BASKVILL.TTF', name='Baskerville Old Face', style='normal', variant='normal', weight=400, stretch='normal', size='scalable')) = 10.05
2025-07-23 19:47:52,891 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\segoeuil.ttf', name='Segoe UI', style='normal', variant='normal', weight=300, stretch='normal', size='scalable')) = 10.145
2025-07-23 19:47:52,891 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\GIGI.TTF', name='Gigi', style='normal', variant='normal', weight=400, stretch='normal', size='scalable')) = 10.05
2025-07-23 19:47:52,892 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\IMPRISHA.TTF', name='Imprint MT Shadow', style='normal', variant='normal', weight=400, stretch='normal', size='scalable')) = 10.05
2025-07-23 19:47:52,892 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\GLECB.TTF', name='Gloucester MT Extra Condensed', style='normal', variant='normal', weight=400, stretch='condensed', size='scalable')) = 10.25
2025-07-23 19:47:52,892 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\KUNSTLER.TTF', name='Kunstler Script', style='normal', variant='normal', weight=400, stretch='normal', size='scalable')) = 10.05
2025-07-23 19:47:52,893 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\GillSansCondUltraBoNova.ttf', name='Gill Sans Nova', style='normal', variant='normal', weight=900, stretch='condensed', size='scalable')) = 10.725
2025-07-23 19:47:52,893 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\CENSCBK.TTF', name='Century Schoolbook', style='normal', variant='normal', weight=400, stretch='normal', size='scalable')) = 10.05
2025-07-23 19:47:52,893 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\MOD20.TTF', name='Modern No. 20', style='normal', variant='normal', weight=400, stretch='normal', size='scalable')) = 10.05
2025-07-23 19:47:52,893 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\VerdanaPro-BoldItalic.ttf', name='Verdana Pro', style='italic', variant='normal', weight=700, stretch='normal', size='scalable')) = 11.335
2025-07-23 19:47:52,893 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\LBRITEDI.TTF', name='Lucida Bright', style='italic', variant='normal', weight=600, stretch='normal', size='scalable')) = 11.24
2025-07-23 19:47:52,893 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\POORICH.TTF', name='Poor Richard', style='normal', variant='normal', weight=400, stretch='normal', size='scalable')) = 10.05
2025-07-23 19:47:52,894 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\UrdType.ttf', name='Urdu Typesetting', style='normal', variant='normal', weight=400, stretch='normal', size='scalable')) = 10.05
2025-07-23 19:47:52,894 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\aparajbi.ttf', name='Aparajita', style='italic', variant='normal', weight=700, stretch='normal', size='scalable')) = 11.335
2025-07-23 19:47:52,894 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\ROCKEB.TTF', name='Rockwell Extra Bold', style='normal', variant='normal', weight=800, stretch='normal', size='scalable')) = 10.43
2025-07-23 19:47:52,894 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\euphemia.ttf', name='Euphemia', style='normal', variant='normal', weight=400, stretch='normal', size='scalable')) = 10.05
2025-07-23 19:47:52,894 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\RockwellNovaCond-BoldItalic.ttf', name='Rockwell Nova', style='italic', variant='normal', weight=700, stretch='condensed', size='scalable')) = 11.535
2025-07-23 19:47:52,894 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\TCB_____.TTF', name='Tw Cen MT', style='normal', variant='normal', weight=700, stretch='normal', size='scalable')) = 10.335
2025-07-23 19:47:52,894 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\aparaji.ttf', name='Aparajita', style='italic', variant='normal', weight=400, stretch='normal', size='scalable')) = 11.05
2025-07-23 19:47:52,895 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\upcfl.ttf', name='FreesiaUPC', style='normal', variant='normal', weight=400, stretch='normal', size='scalable')) = 10.05
2025-07-23 19:47:52,895 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\upceb.ttf', name='EucrosiaUPC', style='normal', variant='normal', weight=700, stretch='normal', size='scalable')) = 10.335
2025-07-23 19:47:52,895 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\calibri.ttf', name='Calibri', style='normal', variant='normal', weight=400, stretch='normal', size='scalable')) = 10.05
2025-07-23 19:47:52,895 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\seguisli.ttf', name='Segoe UI', style='italic', variant='normal', weight=350, stretch='normal', size='scalable')) = 11.0975
2025-07-23 19:47:52,895 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\TCM_____.TTF', name='Tw Cen MT', style='normal', variant='normal', weight=400, stretch='normal', size='scalable')) = 10.05
2025-07-23 19:47:52,895 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\corbelz.ttf', name='Corbel', style='italic', variant='normal', weight=700, stretch='normal', size='scalable')) = 11.335
2025-07-23 19:47:52,896 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\ariblk.ttf', name='Arial', style='normal', variant='normal', weight=900, stretch='normal', size='scalable')) = 0.525
2025-07-23 19:47:52,896 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\mmrtext.ttf', name='Myanmar Text', style='normal', variant='normal', weight=400, stretch='normal', size='scalable')) = 10.05
2025-07-23 19:47:52,896 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\georgia.ttf', name='Georgia', style='normal', variant='normal', weight=400, stretch='normal', size='scalable')) = 10.05
2025-07-23 19:47:52,896 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\msjhl.ttc', name='Microsoft JhengHei', style='normal', variant='normal', weight=290, stretch='normal', size='scalable')) = 10.1545
2025-07-23 19:47:52,896 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\tradbdo.ttf', name='Traditional Arabic', style='normal', variant='normal', weight=700, stretch='normal', size='scalable')) = 10.335
2025-07-23 19:47:52,896 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\ArialNovaCond-Italic.ttf', name='Arial Nova', style='italic', variant='normal', weight=400, stretch='condensed', size='scalable')) = 11.25
2025-07-23 19:47:52,896 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\msjhbd.ttc', name='Microsoft JhengHei', style='normal', variant='normal', weight=700, stretch='normal', size='scalable')) = 10.335
2025-07-23 19:47:52,897 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\FRAHVIT.TTF', name='Franklin Gothic Heavy', style='italic', variant='normal', weight=400, stretch='normal', size='scalable')) = 11.05
2025-07-23 19:47:52,897 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\georgiab.ttf', name='Georgia', style='normal', variant='normal', weight=700, stretch='normal', size='scalable')) = 10.335
2025-07-23 19:47:52,897 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\daunpenh.ttf', name='DaunPenh', style='normal', variant='normal', weight=400, stretch='normal', size='scalable')) = 10.05
2025-07-23 19:47:52,897 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\HTOWERT.TTF', name='High Tower Text', style='normal', variant='normal', weight=400, stretch='normal', size='scalable')) = 10.05
2025-07-23 19:47:52,897 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\david.ttf', name='David', style='normal', variant='normal', weight=400, stretch='normal', size='scalable')) = 10.05
2025-07-23 19:47:52,897 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\NotoSansKR-VF.ttf', name='Noto Sans KR', style='normal', variant='normal', weight=100, stretch='normal', size='scalable')) = 10.335
2025-07-23 19:47:52,897 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\ANTQUAI.TTF', name='Book Antiqua', style='italic', variant='normal', weight=400, stretch='normal', size='scalable')) = 11.05
2025-07-23 19:47:52,898 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\holomdl2.ttf', name='HoloLens MDL2 Assets', style='normal', variant='normal', weight=400, stretch='normal', size='scalable')) = 10.05
2025-07-23 19:47:52,898 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\corbeli.ttf', name='Corbel', style='italic', variant='normal', weight=400, stretch='normal', size='scalable')) = 11.05
2025-07-23 19:47:52,898 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\aparajb.ttf', name='Aparajita', style='normal', variant='normal', weight=700, stretch='normal', size='scalable')) = 10.335
2025-07-23 19:47:52,898 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\constanb.ttf', name='Constantia', style='normal', variant='normal', weight=700, stretch='normal', size='scalable')) = 10.335
2025-07-23 19:47:52,898 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\GeorgiaPro-LightItalic.ttf', name='Georgia Pro', style='italic', variant='normal', weight=300, stretch='normal', size='scalable')) = 11.145
2025-07-23 19:47:52,899 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\plantc.ttf', name='Plantagenet Cherokee', style='normal', variant='normal', weight=400, stretch='normal', size='scalable')) = 10.05
2025-07-23 19:47:52,899 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\HARNGTON.TTF', name='Harrington', style='normal', variant='normal', weight=400, stretch='normal', size='scalable')) = 10.05
2025-07-23 19:47:52,899 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\himalaya.ttf', name='Microsoft Himalaya', style='normal', variant='normal', weight=400, stretch='normal', size='scalable')) = 10.05
2025-07-23 19:47:52,899 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\TCBI____.TTF', name='Tw Cen MT', style='italic', variant='normal', weight=700, stretch='normal', size='scalable')) = 11.335
2025-07-23 19:47:52,899 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\leelawdb.ttf', name='Leelawadee', style='normal', variant='normal', weight=700, stretch='normal', size='scalable')) = 10.335
2025-07-23 19:47:52,899 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\ariali.ttf', name='Arial', style='italic', variant='normal', weight=400, stretch='normal', size='scalable')) = 1.05
2025-07-23 19:47:52,899 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\LSANS.TTF', name='Lucida Sans', style='normal', variant='normal', weight=400, stretch='normal', size='scalable')) = 10.05
2025-07-23 19:47:52,900 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\iskpota.ttf', name='Iskoola Pota', style='normal', variant='normal', weight=400, stretch='normal', size='scalable')) = 10.05
2025-07-23 19:47:52,900 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\raavi.ttf', name='Raavi', style='normal', variant='normal', weight=400, stretch='normal', size='scalable')) = 10.05
2025-07-23 19:47:52,901 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\ArialNovaCond-LightItalic.ttf', name='Arial Nova', style='italic', variant='normal', weight=300, stretch='condensed', size='scalable')) = 11.344999999999999
2025-07-23 19:47:52,901 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\PLAYBILL.TTF', name='Playbill', style='normal', variant='normal', weight=400, stretch='normal', size='scalable')) = 10.05
2025-07-23 19:47:52,901 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\vrindab.ttf', name='Vrinda', style='normal', variant='normal', weight=700, stretch='normal', size='scalable')) = 10.335
2025-07-23 19:47:52,901 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\LSANSD.TTF', name='Lucida Sans', style='normal', variant='normal', weight=600, stretch='normal', size='scalable')) = 10.24
2025-07-23 19:47:52,902 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\seguili.ttf', name='Segoe UI', style='italic', variant='normal', weight=300, stretch='normal', size='scalable')) = 11.145
2025-07-23 19:47:52,902 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\estre.ttf', name='Estrangelo Edessa', style='normal', variant='normal', weight=400, stretch='normal', size='scalable')) = 10.05
2025-07-23 19:47:52,902 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\upckl.ttf', name='KodchiangUPC', style='normal', variant='normal', weight=400, stretch='normal', size='scalable')) = 10.05
2025-07-23 19:47:52,903 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\arialbd.ttf', name='Arial', style='normal', variant='normal', weight=700, stretch='normal', size='scalable')) = 0.33499999999999996
2025-07-23 19:47:52,903 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\verdanab.ttf', name='Verdana', style='normal', variant='normal', weight=700, stretch='normal', size='scalable')) = 10.335
2025-07-23 19:47:52,904 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\Dengl.ttf', name='DengXian', style='normal', variant='normal', weight=300, stretch='normal', size='scalable')) = 10.145
2025-07-23 19:47:52,904 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\KhmerUIb.ttf', name='Khmer UI', style='normal', variant='normal', weight=700, stretch='normal', size='scalable')) = 10.335
2025-07-23 19:47:52,904 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\ArialNovaCond-BoldItalic.ttf', name='Arial Nova', style='italic', variant='normal', weight=700, stretch='condensed', size='scalable')) = 11.535
2025-07-23 19:47:52,904 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\meiryob.ttc', name='Meiryo', style='normal', variant='normal', weight=700, stretch='normal', size='scalable')) = 10.335
2025-07-23 19:47:52,904 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\BRLNSB.TTF', name='Berlin Sans FB', style='normal', variant='normal', weight=700, stretch='normal', size='scalable')) = 10.335
2025-07-23 19:47:52,905 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\verdanaz.ttf', name='Verdana', style='italic', variant='normal', weight=700, stretch='normal', size='scalable')) = 11.335
2025-07-23 19:47:52,905 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\GOUDOS.TTF', name='Goudy Old Style', style='normal', variant='normal', weight=400, stretch='normal', size='scalable')) = 10.05
2025-07-23 19:47:52,905 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\upcdbi.ttf', name='DilleniaUPC', style='italic', variant='normal', weight=700, stretch='normal', size='scalable')) = 11.335
2025-07-23 19:47:52,905 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\SitkaB.ttc', name='Sitka Small', style='normal', variant='normal', weight=700, stretch='normal', size='scalable')) = 10.335
2025-07-23 19:47:52,905 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\ARLRDBD.TTF', name='Arial Rounded MT Bold', style='normal', variant='normal', weight=400, stretch='normal', size='scalable')) = 10.05
2025-07-23 19:47:52,905 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\kartikab.ttf', name='Kartika', style='normal', variant='normal', weight=700, stretch='normal', size='scalable')) = 10.335
2025-07-23 19:47:52,905 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\BELLB.TTF', name='Bell MT', style='normal', variant='normal', weight=700, stretch='normal', size='scalable')) = 10.335
2025-07-23 19:47:52,906 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\wingding.ttf', name='Wingdings', style='normal', variant='normal', weight=400, stretch='normal', size='scalable')) = 10.05
2025-07-23 19:47:52,906 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\mangal.ttf', name='Mangal', style='normal', variant='normal', weight=400, stretch='normal', size='scalable')) = 10.05
2025-07-23 19:47:52,906 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\WINGDNG3.TTF', name='Wingdings 3', style='normal', variant='normal', weight=400, stretch='normal', size='scalable')) = 10.05
2025-07-23 19:47:52,906 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\RockwellNovaCond-LightItalic.ttf', name='Rockwell Nova', style='italic', variant='normal', weight=300, stretch='condensed', size='scalable')) = 11.344999999999999
2025-07-23 19:47:52,906 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\HPSimplified_LtIt.ttf', name='HP Simplified', style='italic', variant='normal', weight=300, stretch='normal', size='scalable')) = 11.145
2025-07-23 19:47:52,906 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\VerdanaPro-CondLight.ttf', name='Verdana Pro', style='normal', variant='normal', weight=300, stretch='condensed', size='scalable')) = 10.344999999999999
2025-07-23 19:47:52,907 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\cambria.ttc', name='Cambria', style='normal', variant='normal', weight=400, stretch='normal', size='scalable')) = 10.05
2025-07-23 19:47:52,907 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\Candarai.ttf', name='Candara', style='italic', variant='normal', weight=400, stretch='normal', size='scalable')) = 11.05
2025-07-23 19:47:52,907 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\CALIST.TTF', name='Calisto MT', style='normal', variant='normal', weight=400, stretch='normal', size='scalable')) = 10.05
2025-07-23 19:47:52,907 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\ebrimabd.ttf', name='Ebrima', style='normal', variant='normal', weight=700, stretch='normal', size='scalable')) = 10.335
2025-07-23 19:47:52,907 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\CALIFB.TTF', name='Californian FB', style='normal', variant='normal', weight=700, stretch='normal', size='scalable')) = 10.335
2025-07-23 19:47:52,907 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\framd.ttf', name='Franklin Gothic Medium', style='normal', variant='normal', weight=400, stretch='normal', size='scalable')) = 10.05
2025-07-23 19:47:52,907 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\constani.ttf', name='Constantia', style='italic', variant='normal', weight=400, stretch='normal', size='scalable')) = 11.05
2025-07-23 19:47:52,908 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\LTYPE.TTF', name='Lucida Sans Typewriter', style='normal', variant='normal', weight=400, stretch='normal', size='scalable')) = 10.05
2025-07-23 19:47:52,908 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\LFAXD.TTF', name='Lucida Fax', style='normal', variant='normal', weight=600, stretch='normal', size='scalable')) = 10.24
2025-07-23 19:47:52,908 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\symbol.ttf', name='Symbol', style='normal', variant='normal', weight=400, stretch='normal', size='scalable')) = 10.05
2025-07-23 19:47:52,908 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\BIZ-UDGothicB.ttc', name='BIZ UDGothic', style='normal', variant='normal', weight=700, stretch='normal', size='scalable')) = 10.335
2025-07-23 19:47:52,909 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\VerdanaPro-Italic.ttf', name='Verdana Pro', style='italic', variant='normal', weight=400, stretch='normal', size='scalable')) = 11.05
2025-07-23 19:47:52,909 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\corbell.ttf', name='Corbel', style='normal', variant='normal', weight=300, stretch='normal', size='scalable')) = 10.145
2025-07-23 19:47:52,909 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\simpbdo.ttf', name='Simplified Arabic', style='normal', variant='normal', weight=700, stretch='normal', size='scalable')) = 10.335
2025-07-23 19:47:52,910 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\PER_____.TTF', name='Perpetua', style='normal', variant='normal', weight=400, stretch='normal', size='scalable')) = 10.05
2025-07-23 19:47:52,910 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\LaoUIb.ttf', name='Lao UI', style='normal', variant='normal', weight=700, stretch='normal', size='scalable')) = 10.335
2025-07-23 19:47:52,910 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\FELIXTI.TTF', name='Felix Titling', style='normal', variant='normal', weight=400, stretch='normal', size='scalable')) = 10.05
2025-07-23 19:47:52,910 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\trebuc.ttf', name='Trebuchet MS', style='normal', variant='normal', weight=400, stretch='normal', size='scalable')) = 10.05
2025-07-23 19:47:52,910 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\HTOWERTI.TTF', name='High Tower Text', style='italic', variant='normal', weight=400, stretch='normal', size='scalable')) = 11.05
2025-07-23 19:47:52,910 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\BIZ-UDMinchoM.ttc', name='BIZ UDMincho', style='normal', variant='normal', weight=500, stretch='normal', size='scalable')) = 10.145
2025-07-23 19:47:52,911 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\UrdTypeb.ttf', name='Urdu Typesetting', style='normal', variant='normal', weight=700, stretch='normal', size='scalable')) = 10.335
2025-07-23 19:47:52,911 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\VerdanaPro-CondBlack.ttf', name='Verdana Pro', style='normal', variant='normal', weight=900, stretch='condensed', size='scalable')) = 10.725
2025-07-23 19:47:52,911 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\Candarali.ttf', name='Candara', style='italic', variant='normal', weight=400, stretch='normal', size='scalable')) = 11.05
2025-07-23 19:47:52,911 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\ROCKB.TTF', name='Rockwell', style='normal', variant='normal', weight=700, stretch='normal', size='scalable')) = 10.335
2025-07-23 19:47:52,912 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\msyi.ttf', name='Microsoft Yi Baiti', style='normal', variant='normal', weight=400, stretch='normal', size='scalable')) = 10.05
2025-07-23 19:47:52,912 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\comici.ttf', name='Comic Sans MS', style='italic', variant='normal', weight=400, stretch='normal', size='scalable')) = 11.05
2025-07-23 19:47:52,912 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\cour.ttf', name='Courier New', style='normal', variant='normal', weight=400, stretch='normal', size='scalable')) = 10.05
2025-07-23 19:47:52,912 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\CENTAUR.TTF', name='Centaur', style='normal', variant='normal', weight=400, stretch='normal', size='scalable')) = 10.05
2025-07-23 19:47:52,912 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\VerdanaPro-CondBold.ttf', name='Verdana Pro', style='normal', variant='normal', weight=700, stretch='condensed', size='scalable')) = 10.535
2025-07-23 19:47:52,912 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\GOUDOSB.TTF', name='Goudy Old Style', style='normal', variant='normal', weight=700, stretch='normal', size='scalable')) = 10.335
2025-07-23 19:47:52,912 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\MATURASC.TTF', name='Matura MT Script Capitals', style='normal', variant='normal', weight=400, stretch='normal', size='scalable')) = 10.05
2025-07-23 19:47:52,913 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\FRAHV.TTF', name='Franklin Gothic Heavy', style='normal', variant='normal', weight=400, stretch='normal', size='scalable')) = 10.05
2025-07-23 19:47:52,913 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\corbelb.ttf', name='Corbel', style='normal', variant='normal', weight=700, stretch='normal', size='scalable')) = 10.335
2025-07-23 19:47:52,913 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\ArialNova-Bold.ttf', name='Arial Nova', style='normal', variant='normal', weight=700, stretch='normal', size='scalable')) = 10.335
2025-07-23 19:47:52,913 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\timesbd.ttf', name='Times New Roman', style='normal', variant='normal', weight=700, stretch='normal', size='scalable')) = 10.335
2025-07-23 19:47:52,913 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\LSANSI.TTF', name='Lucida Sans', style='italic', variant='normal', weight=400, stretch='normal', size='scalable')) = 11.05
2025-07-23 19:47:52,913 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\utsaahi.ttf', name='Utsaah', style='italic', variant='normal', weight=400, stretch='normal', size='scalable')) = 11.05
2025-07-23 19:47:52,914 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\georgiaz.ttf', name='Georgia', style='italic', variant='normal', weight=700, stretch='normal', size='scalable')) = 11.335
2025-07-23 19:47:52,914 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\GillSansCondBoNova.ttf', name='Gill Sans Nova', style='normal', variant='normal', weight=700, stretch='condensed', size='scalable')) = 10.535
2025-07-23 19:47:52,914 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\lvnm.ttf', name='Levenim MT', style='normal', variant='normal', weight=400, stretch='normal', size='scalable')) = 10.05
2025-07-23 19:47:52,914 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\FRABKIT.TTF', name='Franklin Gothic Book', style='italic', variant='normal', weight=400, stretch='normal', size='scalable')) = 11.05
2025-07-23 19:47:52,914 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\PERBI___.TTF', name='Perpetua', style='italic', variant='normal', weight=700, stretch='normal', size='scalable')) = 11.335
2025-07-23 19:47:52,914 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\Vanib.ttf', name='Vani', style='normal', variant='normal', weight=700, stretch='normal', size='scalable')) = 10.335
2025-07-23 19:47:52,915 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\segoesc.ttf', name='Segoe Script', style='normal', variant='normal', weight=400, stretch='normal', size='scalable')) = 10.05
2025-07-23 19:47:52,915 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\TCMI____.TTF', name='Tw Cen MT', style='italic', variant='normal', weight=400, stretch='normal', size='scalable')) = 11.05
2025-07-23 19:47:52,915 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\BELLI.TTF', name='Bell MT', style='italic', variant='normal', weight=400, stretch='normal', size='scalable')) = 11.05
2025-07-23 19:47:52,915 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\javatext.ttf', name='Javanese Text', style='normal', variant='normal', weight=400, stretch='normal', size='scalable')) = 10.05
2025-07-23 19:47:52,915 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\HPSimplified.ttf', name='HP Simplified', style='normal', variant='normal', weight=700, stretch='normal', size='scalable')) = 10.335
2025-07-23 19:47:52,915 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\HPSimplified_Lt.ttf', name='HP Simplified', style='normal', variant='normal', weight=300, stretch='normal', size='scalable')) = 10.145
2025-07-23 19:47:52,915 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\FRADM.TTF', name='Franklin Gothic Demi', style='normal', variant='normal', weight=400, stretch='normal', size='scalable')) = 10.05
2025-07-23 19:47:52,916 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\TCCM____.TTF', name='Tw Cen MT Condensed', style='normal', variant='normal', weight=400, stretch='condensed', size='scalable')) = 10.25
2025-07-23 19:47:52,916 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\constan.ttf', name='Constantia', style='normal', variant='normal', weight=400, stretch='normal', size='scalable')) = 10.05
2025-07-23 19:47:52,917 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\HPSimplified_BdIt.ttf', name='HP Simplified', style='italic', variant='normal', weight=700, stretch='normal', size='scalable')) = 11.335
2025-07-23 19:47:52,917 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\Candaraz.ttf', name='Candara', style='italic', variant='normal', weight=700, stretch='normal', size='scalable')) = 11.335
2025-07-23 19:47:52,917 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\ArialNovaCond.ttf', name='Arial Nova', style='normal', variant='normal', weight=400, stretch='condensed', size='scalable')) = 10.25
2025-07-23 19:47:52,918 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\Sitka.ttc', name='Sitka Small', style='normal', variant='normal', weight=400, stretch='normal', size='scalable')) = 10.05
2025-07-23 19:47:52,918 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\consolab.ttf', name='Consolas', style='normal', variant='normal', weight=700, stretch='normal', size='scalable')) = 10.335
2025-07-23 19:47:52,918 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\NHaasGroteskTXPro-66MdIt.ttf', name='Neue Haas Grotesk Text Pro', style='italic', variant='normal', weight=500, stretch='normal', size='scalable')) = 11.145
2025-07-23 19:47:52,918 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\PERTILI.TTF', name='Perpetua Titling MT', style='normal', variant='normal', weight=300, stretch='normal', size='scalable')) = 10.145
2025-07-23 19:47:52,918 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\leelawad.ttf', name='Leelawadee', style='normal', variant='normal', weight=400, stretch='normal', size='scalable')) = 10.05
2025-07-23 19:47:52,918 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\VerdanaPro-CondLightItalic.ttf', name='Verdana Pro', style='italic', variant='normal', weight=300, stretch='condensed', size='scalable')) = 11.344999999999999
2025-07-23 19:47:52,919 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\VerdanaPro-CondItalic.ttf', name='Verdana Pro', style='italic', variant='normal', weight=400, stretch='condensed', size='scalable')) = 11.25
2025-07-23 19:47:52,919 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\calibrib.ttf', name='Calibri', style='normal', variant='normal', weight=700, stretch='normal', size='scalable')) = 10.335
2025-07-23 19:47:52,919 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\vrinda.ttf', name='Vrinda', style='normal', variant='normal', weight=400, stretch='normal', size='scalable')) = 10.05
2025-07-23 19:47:52,919 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\BOD_PSTC.TTF', name='Bodoni MT', style='normal', variant='normal', weight=300, stretch='normal', size='scalable')) = 10.145
2025-07-23 19:47:52,919 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\segoescb.ttf', name='Segoe Script', style='normal', variant='normal', weight=700, stretch='normal', size='scalable')) = 10.335
2025-07-23 19:47:52,919 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\trebucbd.ttf', name='Trebuchet MS', style='normal', variant='normal', weight=700, stretch='normal', size='scalable')) = 10.335
2025-07-23 19:47:52,919 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\KhmerUI.ttf', name='Khmer UI', style='normal', variant='normal', weight=400, stretch='normal', size='scalable')) = 10.05
2025-07-23 19:47:52,920 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\AGENCYB.TTF', name='Agency FB', style='normal', variant='normal', weight=700, stretch='normal', size='scalable')) = 10.335
2025-07-23 19:47:52,920 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\BOD_BLAR.TTF', name='Bodoni MT', style='normal', variant='normal', weight=900, stretch='normal', size='scalable')) = 10.525
2025-07-23 19:47:52,920 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\upclb.ttf', name='LilyUPC', style='normal', variant='normal', weight=700, stretch='normal', size='scalable')) = 10.335
2025-07-23 19:47:52,920 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\seguisym.ttf', name='Segoe UI Symbol', style='normal', variant='normal', weight=400, stretch='normal', size='scalable')) = 10.05
2025-07-23 19:47:52,920 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\couri.ttf', name='Courier New', style='italic', variant='normal', weight=400, stretch='normal', size='scalable')) = 11.05
2025-07-23 19:47:52,920 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\angsana.ttc', name='Angsana New', style='normal', variant='normal', weight=400, stretch='normal', size='scalable')) = 10.05
2025-07-23 19:47:52,921 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\lathab.ttf', name='Latha', style='normal', variant='normal', weight=700, stretch='normal', size='scalable')) = 10.335
2025-07-23 19:47:52,921 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\GillSansBoNova.ttf', name='Gill Sans Nova', style='normal', variant='normal', weight=700, stretch='normal', size='scalable')) = 10.335
2025-07-23 19:47:52,921 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\GeorgiaPro-Light.ttf', name='Georgia Pro', style='normal', variant='normal', weight=300, stretch='normal', size='scalable')) = 10.145
2025-07-23 19:47:52,921 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\ELEPHNTI.TTF', name='Elephant', style='italic', variant='normal', weight=400, stretch='normal', size='scalable')) = 11.05
2025-07-23 19:47:52,922 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\simhei.ttf', name='SimHei', style='normal', variant='normal', weight=400, stretch='normal', size='scalable')) = 10.05
2025-07-23 19:47:52,922 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\LeelUIsl.ttf', name='Leelawadee UI', style='normal', variant='normal', weight=350, stretch='normal', size='scalable')) = 10.0975
2025-07-23 19:47:52,922 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\CURLZ___.TTF', name='Curlz MT', style='normal', variant='normal', weight=400, stretch='normal', size='scalable')) = 10.05
2025-07-23 19:47:52,923 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\upcil.ttf', name='IrisUPC', style='normal', variant='normal', weight=400, stretch='normal', size='scalable')) = 10.05
2025-07-23 19:47:52,923 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\BRITANIC.TTF', name='Britannic Bold', style='normal', variant='normal', weight=400, stretch='normal', size='scalable')) = 10.05
2025-07-23 19:47:52,923 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\aparaj.ttf', name='Aparajita', style='normal', variant='normal', weight=400, stretch='normal', size='scalable')) = 10.05
2025-07-23 19:47:52,923 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\LeelawUI.ttf', name='Leelawadee UI', style='normal', variant='normal', weight=400, stretch='normal', size='scalable')) = 10.05
2025-07-23 19:47:52,923 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\upcei.ttf', name='EucrosiaUPC', style='italic', variant='normal', weight=400, stretch='normal', size='scalable')) = 11.05
2025-07-23 19:47:52,923 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\seguiemj.ttf', name='Segoe UI Emoji', style='normal', variant='normal', weight=400, stretch='normal', size='scalable')) = 10.05
2025-07-23 19:47:52,923 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\GeorgiaPro-CondBoldItalic.ttf', name='Georgia Pro', style='italic', variant='normal', weight=700, stretch='condensed', size='scalable')) = 11.535
2025-07-23 19:47:52,924 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\iskpotab.ttf', name='Iskoola Pota', style='normal', variant='normal', weight=700, stretch='normal', size='scalable')) = 10.335
2025-07-23 19:47:52,924 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\LeelaUIb.ttf', name='Leelawadee UI', style='normal', variant='normal', weight=700, stretch='normal', size='scalable')) = 10.335
2025-07-23 19:47:52,924 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\GARAIT.TTF', name='Garamond', style='italic', variant='normal', weight=400, stretch='normal', size='scalable')) = 11.05
2025-07-23 19:47:52,925 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\mriam.ttf', name='Miriam', style='normal', variant='normal', weight=400, stretch='normal', size='scalable')) = 10.05
2025-07-23 19:47:52,926 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\LHANDW.TTF', name='Lucida Handwriting', style='italic', variant='normal', weight=400, stretch='normal', size='scalable')) = 11.05
2025-07-23 19:47:52,926 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\timesbi.ttf', name='Times New Roman', style='italic', variant='normal', weight=700, stretch='normal', size='scalable')) = 11.335
2025-07-23 19:47:52,926 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\sylfaen.ttf', name='Sylfaen', style='normal', variant='normal', weight=400, stretch='normal', size='scalable')) = 10.05
2025-07-23 19:47:52,926 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\ROCCB___.TTF', name='Rockwell Condensed', style='normal', variant='normal', weight=700, stretch='condensed', size='scalable')) = 10.535
2025-07-23 19:47:52,926 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\RockwellNovaCond-Italic.ttf', name='Rockwell Nova', style='italic', variant='normal', weight=400, stretch='condensed', size='scalable')) = 11.25
2025-07-23 19:47:52,926 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\upcjb.ttf', name='JasmineUPC', style='normal', variant='normal', weight=700, stretch='normal', size='scalable')) = 10.335
2025-07-23 19:47:52,927 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\HPSimplified_Rg.ttf', name='HP Simplified', style='normal', variant='normal', weight=400, stretch='normal', size='scalable')) = 10.05
2025-07-23 19:47:52,927 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\timesi.ttf', name='Times New Roman', style='italic', variant='normal', weight=400, stretch='normal', size='scalable')) = 11.05
2025-07-23 19:47:52,927 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\gautamib.ttf', name='Gautami', style='normal', variant='normal', weight=700, stretch='normal', size='scalable')) = 10.335
2025-07-23 19:47:52,927 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\comicbd.ttf', name='Comic Sans MS', style='normal', variant='normal', weight=700, stretch='normal', size='scalable')) = 10.335
2025-07-23 19:47:52,927 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\yumin.ttf', name='Yu Mincho', style='normal', variant='normal', weight=400, stretch='normal', size='scalable')) = 10.05
2025-07-23 19:47:52,927 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\taileb.ttf', name='Microsoft Tai Le', style='normal', variant='normal', weight=700, stretch='normal', size='scalable')) = 10.335
2025-07-23 19:47:52,928 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\ENGR.TTF', name='Engravers MT', style='normal', variant='normal', weight=500, stretch='normal', size='scalable')) = 10.145
2025-07-23 19:47:52,928 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\lucon.ttf', name='Lucida Console', style='normal', variant='normal', weight=400, stretch='normal', size='scalable')) = 10.05
2025-07-23 19:47:52,928 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\GILBI___.TTF', name='Gill Sans MT', style='italic', variant='normal', weight=700, stretch='normal', size='scalable')) = 11.335
2025-07-23 19:47:52,928 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\msgothic.ttc', name='MS Gothic', style='normal', variant='normal', weight=400, stretch='normal', size='scalable')) = 10.05
2025-07-23 19:47:52,928 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\ArialNova-Italic.ttf', name='Arial Nova', style='italic', variant='normal', weight=400, stretch='normal', size='scalable')) = 11.05
2025-07-23 19:47:52,928 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\trebucit.ttf', name='Trebuchet MS', style='italic', variant='normal', weight=400, stretch='normal', size='scalable')) = 11.05
2025-07-23 19:47:52,928 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\segmdl2.ttf', name='Segoe MDL2 Assets', style='normal', variant='normal', weight=400, stretch='normal', size='scalable')) = 10.05
2025-07-23 19:47:52,929 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\Candarab.ttf', name='Candara', style='normal', variant='normal', weight=700, stretch='normal', size='scalable')) = 10.335
2025-07-23 19:47:52,929 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\kaiu.ttf', name='DFKai-SB', style='normal', variant='normal', weight=400, stretch='normal', size='scalable')) = 10.05
2025-07-23 19:47:52,929 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\GeorgiaPro-CondBlackItalic.ttf', name='Georgia Pro', style='italic', variant='normal', weight=900, stretch='condensed', size='scalable')) = 11.725
2025-07-23 19:47:52,930 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\malgunsl.ttf', name='Malgun Gothic', style='normal', variant='normal', weight=300, stretch='normal', size='scalable')) = 10.145
2025-07-23 19:47:52,930 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\RockwellNova-Light.ttf', name='Rockwell Nova', style='normal', variant='normal', weight=300, stretch='normal', size='scalable')) = 10.145
2025-07-23 19:47:52,930 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\utsaahbi.ttf', name='Utsaah', style='italic', variant='normal', weight=700, stretch='normal', size='scalable')) = 11.335
2025-07-23 19:47:52,931 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\moolbor.ttf', name='MoolBoran', style='normal', variant='normal', weight=400, stretch='normal', size='scalable')) = 10.05
2025-07-23 19:47:52,931 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\GeorgiaPro-BlackItalic.ttf', name='Georgia Pro', style='italic', variant='normal', weight=900, stretch='normal', size='scalable')) = 11.525
2025-07-23 19:47:52,931 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\palabi.ttf', name='Palatino Linotype', style='italic', variant='normal', weight=700, stretch='normal', size='scalable')) = 11.335
2025-07-23 19:47:52,931 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\pala.ttf', name='Palatino Linotype', style='normal', variant='normal', weight=400, stretch='normal', size='scalable')) = 10.05
2025-07-23 19:47:52,931 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\NHaasGroteskTXPro-56It.ttf', name='Neue Haas Grotesk Text Pro', style='italic', variant='normal', weight=400, stretch='normal', size='scalable')) = 11.05
2025-07-23 19:47:52,931 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\HATTEN.TTF', name='Haettenschweiler', style='normal', variant='normal', weight=400, stretch='normal', size='scalable')) = 10.05
2025-07-23 19:47:52,931 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\BOD_CB.TTF', name='Bodoni MT', style='normal', variant='normal', weight=700, stretch='condensed', size='scalable')) = 10.535
2025-07-23 19:47:52,932 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\CALISTI.TTF', name='Calisto MT', style='italic', variant='normal', weight=400, stretch='normal', size='scalable')) = 11.05
2025-07-23 19:47:52,932 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\NHaasGroteskTXPro-55Rg.ttf', name='Neue Haas Grotesk Text Pro', style='normal', variant='normal', weight=400, stretch='normal', size='scalable')) = 10.05
2025-07-23 19:47:52,932 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\kokilab.ttf', name='Kokila', style='normal', variant='normal', weight=700, stretch='normal', size='scalable')) = 10.335
2025-07-23 19:47:52,932 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\BKANT.TTF', name='Book Antiqua', style='normal', variant='normal', weight=400, stretch='normal', size='scalable')) = 10.05
2025-07-23 19:47:52,932 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\ROCKBI.TTF', name='Rockwell', style='italic', variant='normal', weight=700, stretch='normal', size='scalable')) = 11.335
2025-07-23 19:47:52,933 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\BOD_CBI.TTF', name='Bodoni MT', style='italic', variant='normal', weight=700, stretch='condensed', size='scalable')) = 11.535
2025-07-23 19:47:52,933 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\Nirmala.ttf', name='Nirmala UI', style='normal', variant='normal', weight=400, stretch='normal', size='scalable')) = 10.05
2025-07-23 19:47:52,933 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\malgunbd.ttf', name='Malgun Gothic', style='normal', variant='normal', weight=700, stretch='normal', size='scalable')) = 10.335
2025-07-23 19:47:52,933 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\SitkaI.ttc', name='Sitka Small', style='italic', variant='normal', weight=400, stretch='normal', size='scalable')) = 11.05
2025-07-23 19:47:52,934 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\ERASBD.TTF', name='Eras Bold ITC', style='normal', variant='normal', weight=400, stretch='normal', size='scalable')) = 10.05
2025-07-23 19:47:52,934 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\seguisb.ttf', name='Segoe UI', style='normal', variant='normal', weight=600, stretch='normal', size='scalable')) = 10.24
2025-07-23 19:47:52,934 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\simpfxo.ttf', name='Simplified Arabic Fixed', style='normal', variant='normal', weight=400, stretch='normal', size='scalable')) = 10.05
2025-07-23 19:47:52,934 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\SCRIPTBL.TTF', name='Script MT Bold', style='normal', variant='normal', weight=700, stretch='normal', size='scalable')) = 10.335
2025-07-23 19:47:52,934 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\Deng.ttf', name='DengXian', style='normal', variant='normal', weight=400, stretch='normal', size='scalable')) = 10.05
2025-07-23 19:47:52,934 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\Vani.ttf', name='Vani', style='normal', variant='normal', weight=400, stretch='normal', size='scalable')) = 10.05
2025-07-23 19:47:52,935 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\comic.ttf', name='Comic Sans MS', style='normal', variant='normal', weight=400, stretch='normal', size='scalable')) = 10.05
2025-07-23 19:47:52,935 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\msyh.ttc', name='Microsoft YaHei', style='normal', variant='normal', weight=400, stretch='normal', size='scalable')) = 10.05
2025-07-23 19:47:52,935 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\BRUSHSCI.TTF', name='Brush Script MT', style='italic', variant='normal', weight=400, stretch='normal', size='scalable')) = 11.05
2025-07-23 19:47:52,935 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\GOTHIC.TTF', name='Century Gothic', style='normal', variant='normal', weight=400, stretch='normal', size='scalable')) = 10.05
2025-07-23 19:47:52,935 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\GOTHICB.TTF', name='Century Gothic', style='normal', variant='normal', weight=700, stretch='normal', size='scalable')) = 10.335
2025-07-23 19:47:52,935 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\msuighub.ttf', name='Microsoft Uighur', style='normal', variant='normal', weight=700, stretch='normal', size='scalable')) = 10.335
2025-07-23 19:47:52,935 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\kokila.ttf', name='Kokila', style='normal', variant='normal', weight=400, stretch='normal', size='scalable')) = 10.05
2025-07-23 19:47:52,936 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\VerdanaPro-CondSemiBoldItalic.ttf', name='Verdana Pro', style='italic', variant='normal', weight=600, stretch='condensed', size='scalable')) = 11.44
2025-07-23 19:47:52,936 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\browalia.ttc', name='Browallia New', style='normal', variant='normal', weight=400, stretch='normal', size='scalable')) = 10.05
2025-07-23 19:47:52,936 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\SCHLBKI.TTF', name='Century Schoolbook', style='italic', variant='normal', weight=400, stretch='normal', size='scalable')) = 11.05
2025-07-23 19:47:52,936 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\PERB____.TTF', name='Perpetua', style='normal', variant='normal', weight=700, stretch='normal', size='scalable')) = 10.335
2025-07-23 19:47:52,936 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\RockwellNovaCond-Bold.ttf', name='Rockwell Nova', style='normal', variant='normal', weight=700, stretch='condensed', size='scalable')) = 10.535
2025-07-23 19:47:52,937 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\ELEPHNT.TTF', name='Elephant', style='normal', variant='normal', weight=400, stretch='normal', size='scalable')) = 10.05
2025-07-23 19:47:52,937 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\NIAGENG.TTF', name='Niagara Engraved', style='normal', variant='normal', weight=400, stretch='normal', size='scalable')) = 10.05
2025-07-23 19:47:52,937 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\courbi.ttf', name='Courier New', style='italic', variant='normal', weight=700, stretch='normal', size='scalable')) = 11.335
2025-07-23 19:47:52,937 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\impact.ttf', name='Impact', style='normal', variant='normal', weight=400, stretch='normal', size='scalable')) = 10.05
2025-07-23 19:47:52,938 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\trebucbi.ttf', name='Trebuchet MS', style='italic', variant='normal', weight=700, stretch='normal', size='scalable')) = 11.335
2025-07-23 19:47:52,938 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\upcdb.ttf', name='DilleniaUPC', style='normal', variant='normal', weight=700, stretch='normal', size='scalable')) = 10.335
2025-07-23 19:47:52,938 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\LTYPEB.TTF', name='Lucida Sans Typewriter', style='normal', variant='normal', weight=600, stretch='normal', size='scalable')) = 10.24
2025-07-23 19:47:52,938 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\NHaasGroteskTXPro-75Bd.ttf', name='Neue Haas Grotesk Text Pro', style='normal', variant='normal', weight=700, stretch='normal', size='scalable')) = 10.335
2025-07-23 19:47:52,938 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\BRADHITC.TTF', name='Bradley Hand ITC', style='normal', variant='normal', weight=400, stretch='normal', size='scalable')) = 10.05
2025-07-23 19:47:52,939 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\GeorgiaPro-BoldItalic.ttf', name='Georgia Pro', style='italic', variant='normal', weight=700, stretch='normal', size='scalable')) = 11.335
2025-07-23 19:47:52,939 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\palab.ttf', name='Palatino Linotype', style='normal', variant='normal', weight=700, stretch='normal', size='scalable')) = 10.335
2025-07-23 19:47:52,939 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\malgun.ttf', name='Malgun Gothic', style='normal', variant='normal', weight=400, stretch='normal', size='scalable')) = 10.05
2025-07-23 19:47:52,939 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\NirmalaS.ttf', name='Nirmala UI', style='normal', variant='normal', weight=350, stretch='normal', size='scalable')) = 10.0975
2025-07-23 19:47:52,939 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\upcii.ttf', name='IrisUPC', style='italic', variant='normal', weight=400, stretch='normal', size='scalable')) = 11.05
2025-07-23 19:47:52,939 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\calibriz.ttf', name='Calibri', style='italic', variant='normal', weight=700, stretch='normal', size='scalable')) = 11.335
2025-07-23 19:47:52,939 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\tahomabd.ttf', name='Tahoma', style='normal', variant='normal', weight=700, stretch='normal', size='scalable')) = 10.335
2025-07-23 19:47:52,940 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\msmincho.ttc', name='MS Mincho', style='normal', variant='normal', weight=400, stretch='normal', size='scalable')) = 10.05
2025-07-23 19:47:52,940 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\segoeuii.ttf', name='Segoe UI', style='italic', variant='normal', weight=400, stretch='normal', size='scalable')) = 11.05
2025-07-23 19:47:52,940 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\GARA.TTF', name='Garamond', style='normal', variant='normal', weight=400, stretch='normal', size='scalable')) = 10.05
2025-07-23 19:47:52,940 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\STENCIL.TTF', name='Stencil', style='normal', variant='normal', weight=400, stretch='normal', size='scalable')) = 10.05
2025-07-23 19:47:52,940 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\ebrima.ttf', name='Ebrima', style='normal', variant='normal', weight=400, stretch='normal', size='scalable')) = 10.05
2025-07-23 19:47:52,941 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\tahoma.ttf', name='Tahoma', style='normal', variant='normal', weight=400, stretch='normal', size='scalable')) = 10.05
2025-07-23 19:47:52,941 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\CASTELAR.TTF', name='Castellar', style='normal', variant='normal', weight=400, stretch='normal', size='scalable')) = 10.05
2025-07-23 19:47:52,941 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\CENTURY.TTF', name='Century', style='normal', variant='normal', weight=400, stretch='normal', size='scalable')) = 10.05
2025-07-23 19:47:52,941 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\segoeuiz.ttf', name='Segoe UI', style='italic', variant='normal', weight=700, stretch='normal', size='scalable')) = 11.335
2025-07-23 19:47:52,942 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\GeorgiaPro-SemiBoldItalic.ttf', name='Georgia Pro', style='italic', variant='normal', weight=600, stretch='normal', size='scalable')) = 11.24
2025-07-23 19:47:52,942 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\GILLUBCD.TTF', name='Gill Sans Ultra Bold Condensed', style='normal', variant='normal', weight=400, stretch='condensed', size='scalable')) = 10.25
2025-07-23 19:47:52,942 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\upcfb.ttf', name='FreesiaUPC', style='normal', variant='normal', weight=700, stretch='normal', size='scalable')) = 10.335
2025-07-23 19:47:52,943 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\GillSansCondLightItNova.ttf', name='Gill Sans Nova', style='italic', variant='normal', weight=300, stretch='condensed', size='scalable')) = 11.344999999999999
2025-07-23 19:47:52,943 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\VerdanaPro-Black.ttf', name='Verdana Pro', style='normal', variant='normal', weight=900, stretch='normal', size='scalable')) = 10.525
2025-07-23 19:47:52,943 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\arialnbi.ttf', name='Arial', style='italic', variant='normal', weight=700, stretch='condensed', size='scalable')) = 1.535
2025-07-23 19:47:52,944 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\upcibi.ttf', name='IrisUPC', style='italic', variant='normal', weight=700, stretch='normal', size='scalable')) = 11.335
2025-07-23 19:47:52,944 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\BOOKOS.TTF', name='Bookman Old Style', style='normal', variant='normal', weight=300, stretch='normal', size='scalable')) = 10.145
2025-07-23 19:47:52,944 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\GillSansCondLightNova.ttf', name='Gill Sans Nova', style='normal', variant='normal', weight=300, stretch='condensed', size='scalable')) = 10.344999999999999
2025-07-23 19:47:52,944 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\BELL.TTF', name='Bell MT', style='normal', variant='normal', weight=400, stretch='normal', size='scalable')) = 10.05
2025-07-23 19:47:52,944 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\OUTLOOK.TTF', name='MS Outlook', style='normal', variant='normal', weight=400, stretch='normal', size='scalable')) = 10.05
2025-07-23 19:47:52,944 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\ITCKRIST.TTF', name='Kristen ITC', style='normal', variant='normal', weight=400, stretch='normal', size='scalable')) = 10.05
2025-07-23 19:47:52,945 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\simkai.ttf', name='KaiTi', style='normal', variant='normal', weight=400, stretch='normal', size='scalable')) = 10.05
2025-07-23 19:47:52,945 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\aldhabi.ttf', name='Aldhabi', style='normal', variant='normal', weight=400, stretch='normal', size='scalable')) = 10.05
2025-07-23 19:47:52,945 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\FREESCPT.TTF', name='Freestyle Script', style='normal', variant='normal', weight=400, stretch='normal', size='scalable')) = 10.05
2025-07-23 19:47:52,945 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\PERI____.TTF', name='Perpetua', style='italic', variant='normal', weight=400, stretch='normal', size='scalable')) = 11.05
2025-07-23 19:47:52,945 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\arialn.ttf', name='Arial', style='normal', variant='normal', weight=400, stretch='condensed', size='scalable')) = 0.25
2025-07-23 19:47:52,945 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\YuGothM.ttc', name='Yu Gothic', style='normal', variant='normal', weight=500, stretch='normal', size='scalable')) = 10.145
2025-07-23 19:47:52,946 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\batang.ttc', name='Batang', style='normal', variant='normal', weight=400, stretch='normal', size='scalable')) = 10.05
2025-07-23 19:47:52,946 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\meiryo.ttc', name='Meiryo', style='normal', variant='normal', weight=400, stretch='normal', size='scalable')) = 10.05
2025-07-23 19:47:52,947 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\shrutib.ttf', name='Shruti', style='normal', variant='normal', weight=700, stretch='normal', size='scalable')) = 10.335
2025-07-23 19:47:52,947 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\kokilabi.ttf', name='Kokila', style='italic', variant='normal', weight=700, stretch='normal', size='scalable')) = 11.335
2025-07-23 19:47:52,947 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\GOUDYSTO.TTF', name='Goudy Stout', style='normal', variant='normal', weight=400, stretch='normal', size='scalable')) = 10.05
2025-07-23 19:47:52,947 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\seguihis.ttf', name='Segoe UI Historic', style='normal', variant='normal', weight=400, stretch='normal', size='scalable')) = 10.05
2025-07-23 19:47:52,947 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\majallab.ttf', name='Sakkal Majalla', style='normal', variant='normal', weight=700, stretch='normal', size='scalable')) = 10.335
2025-07-23 19:47:52,947 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\GILC____.TTF', name='Gill Sans MT Condensed', style='normal', variant='normal', weight=400, stretch='condensed', size='scalable')) = 10.25
2025-07-23 19:47:52,947 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\corbelli.ttf', name='Corbel', style='italic', variant='normal', weight=300, stretch='normal', size='scalable')) = 11.145
2025-07-23 19:47:52,948 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\seguisbi.ttf', name='Segoe UI', style='italic', variant='normal', weight=600, stretch='normal', size='scalable')) = 11.24
2025-07-23 19:47:52,948 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\BOD_CI.TTF', name='Bodoni MT', style='italic', variant='normal', weight=400, stretch='condensed', size='scalable')) = 11.25
2025-07-23 19:47:52,948 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\VerdanaPro-CondSemiBold.ttf', name='Verdana Pro', style='normal', variant='normal', weight=600, stretch='condensed', size='scalable')) = 10.44
2025-07-23 19:47:52,948 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\YuGothR.ttc', name='Yu Gothic', style='normal', variant='normal', weight=400, stretch='normal', size='scalable')) = 10.05
2025-07-23 19:47:52,948 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\ONYX.TTF', name='Onyx', style='normal', variant='normal', weight=400, stretch='normal', size='scalable')) = 10.05
2025-07-23 19:47:52,949 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\ArialNova-LightItalic.ttf', name='Arial Nova', style='italic', variant='normal', weight=300, stretch='normal', size='scalable')) = 11.145
2025-07-23 19:47:52,950 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\phagspab.ttf', name='Microsoft PhagsPa', style='normal', variant='normal', weight=700, stretch='normal', size='scalable')) = 10.335
2025-07-23 19:47:52,950 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\BOD_R.TTF', name='Bodoni MT', style='normal', variant='normal', weight=400, stretch='normal', size='scalable')) = 10.05
2025-07-23 19:47:52,951 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\REFSPCL.TTF', name='MS Reference Specialty', style='normal', variant='normal', weight=400, stretch='normal', size='scalable')) = 10.05
2025-07-23 19:47:52,951 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\gulim.ttc', name='Gulim', style='normal', variant='normal', weight=400, stretch='normal', size='scalable')) = 10.05
2025-07-23 19:47:52,952 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\ANTQUAB.TTF', name='Book Antiqua', style='normal', variant='normal', weight=700, stretch='normal', size='scalable')) = 10.335
2025-07-23 19:47:52,952 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\gisha.ttf', name='Gisha', style='normal', variant='normal', weight=400, stretch='normal', size='scalable')) = 10.05
2025-07-23 19:47:52,952 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\LBRITE.TTF', name='Lucida Bright', style='normal', variant='normal', weight=400, stretch='normal', size='scalable')) = 10.05
2025-07-23 19:47:52,952 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\calibrii.ttf', name='Calibri', style='italic', variant='normal', weight=400, stretch='normal', size='scalable')) = 11.05
2025-07-23 19:47:52,952 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\LTYPEO.TTF', name='Lucida Sans Typewriter', style='oblique', variant='normal', weight=400, stretch='normal', size='scalable')) = 11.05
2025-07-23 19:47:52,952 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\webdings.ttf', name='Webdings', style='normal', variant='normal', weight=400, stretch='normal', size='scalable')) = 10.05
2025-07-23 19:47:52,953 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\WINGDNG2.TTF', name='Wingdings 2', style='normal', variant='normal', weight=400, stretch='normal', size='scalable')) = 10.05
2025-07-23 19:47:52,953 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\corbel.ttf', name='Corbel', style='normal', variant='normal', weight=400, stretch='normal', size='scalable')) = 10.05
2025-07-23 19:47:52,953 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\MAIAN.TTF', name='Maiandra GD', style='normal', variant='normal', weight=400, stretch='normal', size='scalable')) = 10.05
2025-07-23 19:47:52,954 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\YuGothL.ttc', name='Yu Gothic', style='normal', variant='normal', weight=300, stretch='normal', size='scalable')) = 10.145
2025-07-23 19:47:52,955 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\ERASLGHT.TTF', name='Eras Light ITC', style='normal', variant='normal', weight=400, stretch='normal', size='scalable')) = 10.05
2025-07-23 19:47:52,957 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\segoeuisl.ttf', name='Segoe UI', style='normal', variant='normal', weight=350, stretch='normal', size='scalable')) = 10.0975
2025-07-23 19:47:52,957 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\RockwellNova-ExtraBold.ttf', name='Rockwell Nova', style='normal', variant='normal', weight=800, stretch='normal', size='scalable')) = 10.43
2025-07-23 19:47:52,957 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\RAGE.TTF', name='Rage Italic', style='italic', variant='normal', weight=400, stretch='normal', size='scalable')) = 11.05
2025-07-23 19:47:52,957 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\GillSansLightNova.ttf', name='Gill Sans Nova', style='normal', variant='normal', weight=300, stretch='normal', size='scalable')) = 10.145
2025-07-23 19:47:52,958 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\RockwellNova-BoldItalic.ttf', name='Rockwell Nova', style='italic', variant='normal', weight=700, stretch='normal', size='scalable')) = 11.335
2025-07-23 19:47:52,959 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\calibril.ttf', name='Calibri', style='normal', variant='normal', weight=300, stretch='normal', size='scalable')) = 10.145
2025-07-23 19:47:52,959 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\NHaasGroteskTXPro-65Md.ttf', name='Neue Haas Grotesk Text Pro', style='normal', variant='normal', weight=500, stretch='normal', size='scalable')) = 10.145
2025-07-23 19:47:52,959 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\OLDENGL.TTF', name='Old English Text MT', style='normal', variant='normal', weight=400, stretch='normal', size='scalable')) = 10.05
2025-07-23 19:47:52,959 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\BIZ-UDGothicR.ttc', name='BIZ UDGothic', style='normal', variant='normal', weight=400, stretch='normal', size='scalable')) = 10.05
2025-07-23 19:47:52,959 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\trado.ttf', name='Traditional Arabic', style='normal', variant='normal', weight=400, stretch='normal', size='scalable')) = 10.05
2025-07-23 19:47:52,960 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\upcdl.ttf', name='DilleniaUPC', style='normal', variant='normal', weight=400, stretch='normal', size='scalable')) = 10.05
2025-07-23 19:47:52,960 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\GeorgiaPro-CondBold.ttf', name='Georgia Pro', style='normal', variant='normal', weight=700, stretch='condensed', size='scalable')) = 10.535
2025-07-23 19:47:52,960 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\RockwellNova-ExtraBoldItalic.ttf', name='Rockwell Nova', style='italic', variant='normal', weight=800, stretch='normal', size='scalable')) = 11.43
2025-07-23 19:47:52,960 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\SCHLBKBI.TTF', name='Century Schoolbook', style='italic', variant='normal', weight=700, stretch='normal', size='scalable')) = 11.335
2025-07-23 19:47:52,961 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\shruti.ttf', name='Shruti', style='normal', variant='normal', weight=400, stretch='normal', size='scalable')) = 10.05
2025-07-23 19:47:52,961 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\RockwellNova.ttf', name='Rockwell Nova', style='normal', variant='normal', weight=400, stretch='normal', size='scalable')) = 10.05
2025-07-23 19:47:52,961 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\ArialNovaCond-Bold.ttf', name='Arial Nova', style='normal', variant='normal', weight=700, stretch='condensed', size='scalable')) = 10.535
2025-07-23 19:47:52,961 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\Sanskr.ttf', name='Sanskrit Text', style='normal', variant='normal', weight=400, stretch='normal', size='scalable')) = 10.05
2025-07-23 19:47:52,961 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\upcfbi.ttf', name='FreesiaUPC', style='italic', variant='normal', weight=700, stretch='normal', size='scalable')) = 11.335
2025-07-23 19:47:52,961 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\gadugi.ttf', name='Gadugi', style='normal', variant='normal', weight=400, stretch='normal', size='scalable')) = 10.05
2025-07-23 19:47:52,962 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\times.ttf', name='Times New Roman', style='normal', variant='normal', weight=400, stretch='normal', size='scalable')) = 10.05
2025-07-23 19:47:52,962 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\RockwellNova-Bold.ttf', name='Rockwell Nova', style='normal', variant='normal', weight=700, stretch='normal', size='scalable')) = 10.335
2025-07-23 19:47:52,962 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\VINERITC.TTF', name='Viner Hand ITC', style='normal', variant='normal', weight=400, stretch='normal', size='scalable')) = 10.05
2025-07-23 19:47:52,962 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\gautami.ttf', name='Gautami', style='normal', variant='normal', weight=400, stretch='normal', size='scalable')) = 10.05
2025-07-23 19:47:52,963 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\raavib.ttf', name='Raavi', style='normal', variant='normal', weight=700, stretch='normal', size='scalable')) = 10.335
2025-07-23 19:47:52,963 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\arialbi.ttf', name='Arial', style='italic', variant='normal', weight=700, stretch='normal', size='scalable')) = 1.335
2025-07-23 19:47:52,963 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\SHOWG.TTF', name='Showcard Gothic', style='normal', variant='normal', weight=400, stretch='normal', size='scalable')) = 10.05
2025-07-23 19:47:52,963 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\GeorgiaPro-CondSemiBold.ttf', name='Georgia Pro', style='normal', variant='normal', weight=600, stretch='condensed', size='scalable')) = 10.44
2025-07-23 19:47:52,963 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\GeorgiaPro-Regular.ttf', name='Georgia Pro', style='normal', variant='normal', weight=400, stretch='normal', size='scalable')) = 10.05
2025-07-23 19:47:52,963 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\NHaasGroteskTXPro-76BdIt.ttf', name='Neue Haas Grotesk Text Pro', style='italic', variant='normal', weight=700, stretch='normal', size='scalable')) = 11.335
2025-07-23 19:47:52,963 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\msuighur.ttf', name='Microsoft Uighur', style='normal', variant='normal', weight=400, stretch='normal', size='scalable')) = 10.05
2025-07-23 19:47:52,964 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\LTYPEBO.TTF', name='Lucida Sans Typewriter', style='oblique', variant='normal', weight=600, stretch='normal', size='scalable')) = 11.24
2025-07-23 19:47:52,964 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\LCALLIG.TTF', name='Lucida Calligraphy', style='italic', variant='normal', weight=400, stretch='normal', size='scalable')) = 11.05
2025-07-23 19:47:52,965 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\LBRITEI.TTF', name='Lucida Bright', style='italic', variant='normal', weight=400, stretch='normal', size='scalable')) = 11.05
2025-07-23 19:47:52,966 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\mingliu.ttc', name='MingLiU', style='normal', variant='normal', weight=400, stretch='normal', size='scalable')) = 10.05
2025-07-23 19:47:52,966 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\BOOKOSBI.TTF', name='Bookman Old Style', style='italic', variant='normal', weight=600, stretch='normal', size='scalable')) = 11.24
2025-07-23 19:47:52,966 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\ERASDEMI.TTF', name='Eras Demi ITC', style='normal', variant='normal', weight=400, stretch='normal', size='scalable')) = 10.05
2025-07-23 19:47:52,967 - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\constanz.ttf', name='Constantia', style='italic', variant='normal', weight=700, stretch='normal', size='scalable')) = 11.335
2025-07-23 19:47:52,967 - DEBUG - findfont: Matching Arial:style=normal:variant=normal:weight=normal:stretch=normal:size=12.0 to Arial ('C:\\Windows\\Fonts\\arial.ttf') with score of 0.050000.
2025-07-23 19:47:53,381 - INFO - Using categorical units to plot a list of strings that are all parsable as floats or dates. If these strings should be plotted as numbers, cast to the appropriate data type before plotting.
2025-07-23 19:47:53,385 - INFO - Using categorical units to plot a list of strings that are all parsable as floats or dates. If these strings should be plotted as numbers, cast to the appropriate data type before plotting.
2025-07-23 19:47:53,635 - DEBUG - Database path: c:\Users\<USER>\Desktop\SupermarketSystem\supermarket.db
2025-07-23 19:47:54,000 - DEBUG - Database path: c:\Users\<USER>\Desktop\SupermarketSystem\supermarket.db
2025-07-23 19:47:54,568 - DEBUG - Database path: c:\Users\<USER>\Desktop\SupermarketSystem\supermarket.db
2025-07-23 19:47:54,904 - DEBUG - Database path: c:\Users\<USER>\Desktop\SupermarketSystem\supermarket.db
2025-07-23 19:47:55,024 - ERROR - Error creating page reports: 'Database' object has no attribute 'get_sales_by_date_range'
2025-07-23 19:49:21,401 - DEBUG - Database path: c:\Users\<USER>\Desktop\SupermarketSystem\supermarket.db
2025-07-23 19:49:23,279 - DEBUG - Database path: c:\Users\<USER>\Desktop\SupermarketSystem\supermarket.db
2025-07-23 19:49:23,795 - DEBUG - Database path: c:\Users\<USER>\Desktop\SupermarketSystem\supermarket.db
2025-07-23 19:49:24,631 - DEBUG - Database path: c:\Users\<USER>\Desktop\SupermarketSystem\supermarket.db
2025-07-23 19:49:25,314 - INFO - Using categorical units to plot a list of strings that are all parsable as floats or dates. If these strings should be plotted as numbers, cast to the appropriate data type before plotting.
2025-07-23 19:49:25,317 - INFO - Using categorical units to plot a list of strings that are all parsable as floats or dates. If these strings should be plotted as numbers, cast to the appropriate data type before plotting.
2025-07-23 19:49:39,263 - INFO - Using categorical units to plot a list of strings that are all parsable as floats or dates. If these strings should be plotted as numbers, cast to the appropriate data type before plotting.
2025-07-23 19:49:39,269 - INFO - Using categorical units to plot a list of strings that are all parsable as floats or dates. If these strings should be plotted as numbers, cast to the appropriate data type before plotting.
2025-07-23 19:49:50,740 - INFO - Using categorical units to plot a list of strings that are all parsable as floats or dates. If these strings should be plotted as numbers, cast to the appropriate data type before plotting.
2025-07-23 19:49:51,154 - INFO - Using categorical units to plot a list of strings that are all parsable as floats or dates. If these strings should be plotted as numbers, cast to the appropriate data type before plotting.
2025-07-23 19:50:46,216 - ERROR - Error showing page reports: 'reports'
2025-07-23 19:51:02,177 - INFO - Using categorical units to plot a list of strings that are all parsable as floats or dates. If these strings should be plotted as numbers, cast to the appropriate data type before plotting.
2025-07-23 19:51:02,180 - INFO - Using categorical units to plot a list of strings that are all parsable as floats or dates. If these strings should be plotted as numbers, cast to the appropriate data type before plotting.
2025-07-23 19:51:10,974 - INFO - Using categorical units to plot a list of strings that are all parsable as floats or dates. If these strings should be plotted as numbers, cast to the appropriate data type before plotting.
2025-07-23 19:51:10,977 - INFO - Using categorical units to plot a list of strings that are all parsable as floats or dates. If these strings should be plotted as numbers, cast to the appropriate data type before plotting.
